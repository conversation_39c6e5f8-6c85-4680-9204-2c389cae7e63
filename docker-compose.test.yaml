version: '3'

services:
  web:
    image: ${BACKEND_IMAGE}
    command: /start
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db
    networks:
      - symmy-ts-network

  db:
    image: postgres:15
    ports:
      - 5432
    env_file:
      - src/backend/.env
    networks:
      - symmy-ts-network

  rabbitmq:
    image: rabbitmq:3.12.8
    ports:
      - 5672
      - 15672
    networks:
      - symmy-ts-network

  redis:
    image: redis:7.2.2
    ports:
      - 6379
    networks:
      - symmy-ts-network

  celery_worker:
    image: ${BACKEND_IMAGE}
    command: /start-celeryworker
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db
    networks:
      - symmy-ts-network

  celery_beat:
    image: ${BACKEND_IMAGE}
    command: /start-celerybeat
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db
    networks:
      - symmy-ts-network

  mongo:
    image: mongo:7.0.4
    ports:
      - 27017
    env_file:
      - src/backend/.env
    networks:
      - symmy-ts-network

networks:
  symmy-ts-network:

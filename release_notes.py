import re
import subprocess
import sys
from datetime import date

# Configuration
PROJECT_URL = "https://kiwi.whys.dev/symmy/symmy/-/merge_requests/"
JIRA_URL = "https://whys.atlassian.net/browse/"
RELEASE_DATE = date.today().isoformat()


def generate_release_version(version):
    """Generate a new release version by incrementing the patch version."""
    if not version:
        with open("src/backend/symmy/__init__.py") as f:
            for line in f:
                if "__version__" in line:
                    # Extract the version number from the line and increment the patch version
                    version = line.split("=")[1].strip().replace('"', "")
                    version = version.split(".")
                    version[-1] = str(int(version[-1]) + 1)
                    version = ".".join(version)
                    break
    return version


RELEASE_VERSION = generate_release_version(sys.argv[1] if len(sys.argv) > 1 else None)
RELEASE_NOTES_FILE = f"release-notes-{RELEASE_VERSION}.md"


def are_branches_up_to_date():
    """Check if local 'main' and 'develop' branches are up-to-date with 'origin'."""
    try:
        # Fetch the latest branches from the origin
        subprocess.check_call(["git", "fetch", "origin"])

        # Check if 'main' or 'develop' is behind their corresponding upstream branch
        status_main = subprocess.check_output(
            ["git", "rev-list", "main..origin/main"], universal_newlines=True
        ).strip()
        status_develop = subprocess.check_output(
            ["git", "rev-list", "develop..origin/develop"], universal_newlines=True
        ).strip()

        # If either branch is not up-to-date, return False
        if status_main or status_develop:
            return False

        return True
    except subprocess.CalledProcessError as e:
        print(f"Error checking branch status: {e}")
        return False


def get_merged_develop_commits():
    """List all merge commits in 'develop' but not in 'main', only if both branches are up-to-date."""
    if not are_branches_up_to_date():
        print(
            "Cannot proceed. Ensure that local branches are up-to-date with 'origin'."
        )
        exit(1)

    try:
        # List all merge commits in `develop` but not in `main`
        output = subprocess.check_output(
            ["git", "log", "--merges", "--pretty=format:%s %b", "main..develop"],
            universal_newlines=True,
        )
        print("Fetched merged branches in 'develop' not yet in 'main'.")
        return output
    except subprocess.CalledProcessError as e:
        print(f"Error executing Git command: {e}")
        exit(1)


def parse_merged_branches(log):
    """Parse the log to find merge requests with the SYMMY-XXX pattern."""

    # Regex pattern to find the "See merge request" section and associated "Merge branch" lines
    merge_blocks = re.findall(
        r"(See merge request symmy/symmy!(\d+)\n[\w\s]*\n*"
        r"(?:Merge branch.*\'feature/SYMMY-\d+\S* into 'develop' \n*)+([\w\-\: ]*\n))",
        log,
        re.IGNORECASE,
    )

    results = []
    for block in merge_blocks:
        # Extract all SYMMY-XXX codes within this block
        jira_issues = re.findall(r"/(SYMMY-\d+)", block[0], re.IGNORECASE)
        for jira_issue in set(jira_issues):
            mr_name_no_code = (
                re.sub(r"(SYMMY(?:-|\s)\d+:)", "", block[2]).strip().capitalize()
            )
            results.append((block[1], jira_issue, mr_name_no_code))

    return results


def generate_release_notes(matches):
    """Generate the release notes for the given matches."""

    release_notes = f"## {RELEASE_VERSION} ({RELEASE_DATE})\n\n### Added ({len(matches)} changes)\n\n"
    for mr_number, jira_issue, mr_name in matches:
        jira_link = f"{JIRA_URL}{jira_issue}"
        mr_link = f"{PROJECT_URL}{mr_number}"
        release_notes += f"* [{jira_issue}]({jira_link}): {mr_name} ([merge request !{mr_number}]({mr_link}))\n"
    release_notes += "\n### Removed (0 changes)\n"
    return release_notes


# Generate release notes
def main():
    log = get_merged_develop_commits()
    matches = parse_merged_branches(log)

    # Generate release notes if matches are found
    if matches:
        release_notes = generate_release_notes(matches)
    else:
        release_notes = (
            "## Release Notes\n\nNo merged branches found with the SYMMY-XXX pattern."
        )

    with open(RELEASE_NOTES_FILE, "w") as file:
        file.write(release_notes)

    print(f"Release notes have been generated in {RELEASE_NOTES_FILE}")


if __name__ == "__main__":
    main()

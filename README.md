<a name="readme-top"></a>

<!-- PROJECT SHIELDS -->
[![pipeline status](https://gt.whys.dev/symmy/symmy/badges/main/pipeline.svg)](https://gt.whys.dev/symmy/symmy/-/commits/main)
[![Latest Release](https://gt.whys.dev/symmy/symmy/-/badges/release.svg)](https://gt.whys.dev/symmy/symmy/-/releases)
<br/>
<br/>

<div align="center" dir="ltr">
  <a href="https://symmy.com" target="_blank">
    <img align="center" src="https://symmy.com/wp-content/uploads/2024/04/logo_cerne.svg" alt="Logo" width="200">
  </a>
</div>
<br/>
<br/>

<div align="center">
  <h1 style="text-align: center">Symmy</h1>


<p align="center">
    Innovative B2B Integration platform
    <br />
    <a href="https://symmy.atlassian.net/wiki/spaces/engineering/pages/27885569/Docs"><strong>Explore the docs »</strong></a>
    <br />
    <br />
    <a href="https://tune.symmy.app">View Demo</a>
    ·
    <a href="https://symmy.app">View App</a>
    ·
    <a href="https://kiwi.whys.dev/symmy/symmy/-/issues">Report Bug</a>
  </p>
</div>

<div align="center">
<br />

[![code with love by symmy](https://img.shields.io/badge/%3C%2F%3E%20with%20%E2%99%A5%20by-symmy-ff1414.svg?style=flat-square)](https://kiwi.whys.dev/symmy)

</div>

<details open="open">
<summary>Table of Contents</summary>

- [About](#about)
  - [Built With](#built-with)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
- [Usage](#usage)
- [Roadmap](#roadmap)
- [Contributing](#contributing)
- [Authors & contributors](#authors--contributors)
- [Security](#security)

</details>

---

## About

Symmy is an innovative B2B Integration platform with a focus on the ERP segment.

### Built With

- Python
- Django
- PostgreSQL
- Celery
- Redis
- RabbitMQ
- Docker

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Getting Started

### Prerequisites

#### Docker

- [Docker](https://docs.docker.com/get-docker/)

### Installation

#### Local

##### Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- .env file (see .env.example)

1. Clone the repo
   ```sh
   git clone https://gt.whys.dev/symmy/symmy.git
   ```
2. Add your ssh key you use with the repo
   ```sh
   ssh-add ~/.ssh/id_rsa
   ```
3. Build the docker image
   ```sh
   docker compose build
   ```
4. Start the docker image
   ```sh
   docker compose up
   ```

<details>
<summary>Note for <b>ohmyzsh</b> users</summary>

If you have problems with project build due to ssh forwarding,
try to add this to your `~/.zshrc` before `source $ZSH/oh-my-zsh.sh`:

```sh
zstyle :omz:plugins:ssh-agent agent-forwarding on
```
Also add `ssh-agent` to plugins in `~/.zshrc`.

So your `~/.zshrc` should look like this:

```sh
plugins=(git ssh-agent <other-plugins>)

zstyle :omz:plugins:ssh-agent agent-forwarding on

source $ZSH/oh-my-zsh.sh
```
</details>

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Usage

### API

The app is available at `http://localhost:8010/`

### Celery

The celery flower dashboard is available at `http://localhost:5557/`

### Frontend

Frontend is available `http://localhost:3000/`

- email `<EMAIL>`
- password `asd123123`

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Testing

This project uses the standard [django tests](https://docs.djangoproject.com/en/4.2/topics/testing/overview/) for testing.

To run the tests, run the following command in the web container:

```sh
manage.py test
```

### Coverage
Before committing, ensure that the tests are passing and that the coverage is at least 90%.

To run the tests with coverage, run the following command in the web container:
```sh
coverage run --source='.' manage.py test
```

To see the coverage report, run the following command in the web container:
```sh
coverage report
```

## Debaging

If you want to debug the code synchronously, then you can read the article
[docs](https://kiwi.whys.dev/symmy/symmy/-/wikis/Code/Debaging)

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Roadmap

// TODO

## Contributing

1. Create your Feature Branch (`git checkout -b feature/amazing-feature`)
2. Commit your Changes (`git commit -m 'Add some amzing feature'`)
3. Push to the Branch (`git push`)
4. Open a Pull Request
5. Wait for the code review
6. Merge the changes
7. Celebrate 🎉

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Authors & contributors

The original setup of this repository is by [Johnny Unar](https://kiwi.whys.dev/johnnyunar).

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

## Security

Symmy follows good practices of security, but 100% security cannot be assured.
Symmy is provided **"as is"** without any **warranty**. Use at your own risk.

<div align="right" dir="ltr"><a href="#readme-top">Back to top &uarr;</a></div>

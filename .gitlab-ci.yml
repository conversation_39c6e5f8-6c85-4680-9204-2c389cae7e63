# .gitlab-ci.yml

stages:
  - build
  - test
  - deploy
  - collectstatic
  - release

# TEST PIPELINE # 

test_backend-build-job:
  stage: build
  script:
    - echo "$TEST_ENV" > src/backend/.env
    - mkdir src/backend/staticfiles/
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-test" >> src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    - |
      if [ "$REQUIREMENTS" ]; then
        echo $REQUIREMENTS > src/backend/requirements-no-cache.txt
        echo $REQUIREMENTS
      fi
    - cat src/backend/requirements-no-cache.txt
    # - echo 'VERSION_SRM="'$(grep -oP "^Version \K\d+\.\d+\.\d+" "webapp/CHANGES.rst" | head -n1)'"' >> src/backend/.env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - eval $(ssh-agent -s) && ssh-add ~/.ssh/id_rsa
    - docker buildx build --ssh default --build-arg CACHEBUST=$(date +%s) -t docker.whys.eu/symmy/symmy:backend-test -f compose/deploy/django/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:backend-test
    - echo "Build BACKEND is OK"
  only:
    - develop
    - merge_requests
  tags:
    - echo

test_frontend-build-job:
  stage: build
  script:
    - echo "$TEST_ENV" > src/frontend/.env
    # - echo "FRONTEND_IMAGE=$TEST_FRONTEND_IMAGE" >> .env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - docker buildx build -t docker.whys.eu/symmy/symmy:frontend-test -f compose/deploy/frontend/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:frontend-test
    - echo "Build FRONTEND is OK"
  only:
    - develop
    - merge_requests
  tags:
    - echo

test_backend-test-job:
  stage: test
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - echo "$TEST_ENV" > src/backend/.env
    - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-test" >> .env
    - echo "ALLOW_PROD_DB_TESTS=True" >> src/backend/.env
    - docker-compose -f docker-compose.test.yaml up -d --build
  script:
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test -v 2
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test_with_prod_db
  after_script:
    - docker-compose -f docker-compose.test.yaml down
  only:
    - develop
    - merge_requests
  tags:
    - echo

test_frontend-test-job:
  stage: test
  script:
    - echo "$TEST_ENV" > src/frontend/.env
    - docker run --rm docker.whys.eu/symmy/symmy:frontend-test sh -c "yarn test:no-watch"
  only:
    - develop
    - merge_requests
  tags:
    - echo

test_deploy-job:
  stage: deploy
  script:
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-test" > .env
    # - echo "FRONTEND_IMAGE=$TEST_FRONTEND_IMAGE" >> .env
    - echo "$TEST_ENV" > src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    # - docker stack deploy -c docker-compose.deploy.ts.yml symmy-test
    - |
      docker service update --image docker.whys.eu/symmy/symmy:backend-test -d --force symmy-test_celery_worker && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-test -d --force symmy-test_celery_beat && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-test --force symmy-test_web
  only:
    - develop
  tags:
    - echo

test_frontend-deploy-job:
  stage: deploy
  script:
    - echo "$TEST_ENV" > src/frontend/.env
    - docker run --rm -v /home/<USER>/app/frontend:/app/dist docker.whys.eu/symmy/symmy:frontend-test yarn build
  only:
    - develop
  tags:
    - echo

test_collectstatic-job:
  stage: collectstatic
  script:
    - docker exec $(docker ps --filter "name=symmy-test_web." -q | head -n 1) ./manage.py collectstatic --noinput
  only:
    - develop
  tags:
    - echo


# STAGE PIPELINE #

stage_backend-build-job:
  stage: build
  script:
    - echo "$STAGE_ENV" > src/backend/.env
    - mkdir src/backend/staticfiles/
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-stage" >> src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    - |
      if [ "$REQUIREMENTS" ]; then
        echo $REQUIREMENTS > src/backend/requirements-no-cache.txt
        echo $REQUIREMENTS
      fi
    - cat src/backend/requirements-no-cache.txt
    # - echo 'VERSION_SRM="'$(grep -oP "^Version \K\d+\.\d+\.\d+" "webapp/CHANGES.rst" | head -n1)'"' >> src/backend/.env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - eval $(ssh-agent -s) && ssh-add ~/.ssh/id_rsa
    - docker buildx build --ssh default --build-arg CACHEBUST=$(date +%s) -t docker.whys.eu/symmy/symmy:backend-stage -f compose/deploy/django/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:backend-stage
    - echo "Build BACKEND is OK"
  only:
    - /^release\/.*/
  tags:
    - tune

stage_frontend-build-job:
  stage: build
  script:
    - echo "$STAGE_ENV" > src/frontend/.env
    # - echo "FRONTEND_IMAGE=$TEST_FRONTEND_IMAGE" >> .env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - docker buildx build -t docker.whys.eu/symmy/symmy:frontend-stage -f compose/deploy/frontend/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:frontend-stage
    - echo "Build FRONTEND is OK"
  only:
    - /^release\/.*/
  tags:
    - tune

stage_backend-test-job:
  stage: test
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - echo "$STAGE_ENV" > src/backend/.env
    - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-stage" >> .env
    - echo "ALLOW_PROD_DB_TESTS=True" >> src/backend/.env
    - docker-compose -f docker-compose.test.yaml up -d --build
  script:
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test -v 2
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test_with_prod_db
  after_script:
    - docker-compose -f docker-compose.test.yaml down
  only:
    - /^release\/.*/
  tags:
    - tune

stage_frontend-test-job:
  stage: test
  script:
    - echo "$STAGE_ENV" > src/frontend/.env
    - docker run --rm docker.whys.eu/symmy/symmy:frontend-stage sh -c "yarn test:no-watch"
  only:
    - /^release\/.*/
  tags:
    - tune

stage_deploy-job:
  stage: deploy
  script:
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-stage" > .env
    # - echo "FRONTEND_IMAGE=$TEST_FRONTEND_IMAGE" >> .env
    - echo "$STAGE_ENV" > src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    # - docker stack deploy -c docker-compose.deploy.release.yml symmy-stage
    - |
      docker service update --image docker.whys.eu/symmy/symmy:backend-stage -d --force symmy-stage_celery_worker && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-stage -d --force symmy-stage_celery_beat && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-stage --force symmy-stage_web
  only:
    - /^release\/.*/
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - tune

stage_frontend-deploy-job:
  stage: deploy
  script:
    - echo "$STAGE_ENV" > src/frontend/.env
    - docker run --rm -v /home/<USER>/app/frontend:/app/dist docker.whys.eu/symmy/symmy:frontend-stage yarn build
  only:
    - /^release\/.*/
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - tune

stage_collectstatic-job:
  stage: collectstatic
  script:
    - docker exec $(docker ps --filter "name=symmy-stage_web." -q | head -n 1) ./manage.py collectstatic --noinput
  only:
    - /^release\/.*/
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - tune

stage_skip-deploy_job:
  stage: deploy
  script:
    - echo "This job will run only on the main branch and if SCHEDULE_PIPELINE is true"
  only:
    - /^release\/.*/
  except:
    variables:
      - $SCHEDULE_PIPELINE == "true"
  tags:
    - tune


# PRODUCTION PIPELINE #

prod_backend-build-job:
  stage: build
  script:
    - echo "$PROD_ENV" > src/backend/.env
    - mkdir src/backend/staticfiles/
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-prod" >> src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    # - echo 'VERSION_SRM="'$(grep -oP "^Version \K\d+\.\d+\.\d+" "webapp/CHANGES.rst" | head -n1)'"' >> src/backend/.env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - eval $(ssh-agent -s) && ssh-add ~/.ssh/id_rsa
    - docker buildx build --ssh default --build-arg CACHEBUST=$(date +%s) -t docker.whys.eu/symmy/symmy:backend-prod -f compose/deploy/django/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:backend-prod
    - echo "Build BACKEND is OK"
  only:
    - main
  tags:
    - prod

prod_frontend-build-job:
  stage: build
  script:
    - echo "$PROD_ENV" > src/frontend/.env
    # - echo "FRONTEND_IMAGE=docker.whys.eu/symmy/symmy:frontend-prod" >> .env
    - docker login docker.whys.eu --password "$PROJECT_ACCESS_TOKEN" --username "$PROJECT_USER"
    - docker buildx build -t docker.whys.eu/symmy/symmy:frontend-prod -f compose/deploy/frontend/Dockerfile .
    - docker push docker.whys.eu/symmy/symmy:frontend-prod
    - echo "Build FRONTEND is OK"
  only:
    - main
  tags:
    - prod

prod_backend-test-job:
  stage: test
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - echo "$PROD_ENV" > src/backend/.env
    - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-prod" >> .env
    - echo "ALLOW_PROD_DB_TESTS=True" >> src/backend/.env
    - docker-compose -f docker-compose.test.yaml up -d --build
  script:
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test -v 2
    - docker-compose -f docker-compose.test.yaml exec web /app/manage.py test_with_prod_db
  after_script:
    - docker-compose -f docker-compose.test.yaml down
  only:
    - main
  tags:
    - prod

prod_frontend-test-job:
  stage: test
  script:
    - echo "$PROD_ENV" > src/frontend/.env
    - docker run --rm docker.whys.eu/symmy/symmy:frontend-prod sh -c "npm run test:no-watch"
  only:
    - main
  tags:
    - prod

prod_deploy-job:
  stage: deploy
  script:
    # - echo "BACKEND_IMAGE=docker.whys.eu/symmy/symmy:backend-prod" > .env
    # - echo "FRONTEND_IMAGE=docker.whys.eu/symmy/symmy:frontend-prod" >> .env
    - echo "$PROD_ENV" > src/backend/.env
    - echo 'DATETIME_DEPLOY="'$(date +"%d-%m-%Y %H:%M:%S")'"' >> src/backend/.env
    # - docker stack deploy -c docker-compose.deploy.prod.yml symmy
    - |
      docker service update --image docker.whys.eu/symmy/symmy:backend-prod -d --force symmy_celery_worker && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-prod -d --force symmy_celery_beat && \
      docker service update --image docker.whys.eu/symmy/symmy:backend-prod --force symmy_web
    - docker exec $(docker ps --filter "name=symmy_web." -q | head -n 1) ./manage.py collectstatic --noinput
  only:
    - main
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - prod

prod_frontend-deploy-job:
  stage: deploy
  script:
    - echo "$PROD_ENV" > src/frontend/.env
    - docker run --rm -v /home/<USER>/app/frontend:/app/dist docker.whys.eu/symmy/symmy:frontend-prod yarn build
  only:
    - main
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - prod

prod_collectstatic-job:
  stage: collectstatic
  script:
    - docker exec $(docker ps --filter "name=symmy_web." -q | head -n 1) ./manage.py collectstatic --noinput
  only:
    - main
  except:
    variables:
      - $SCHEDULE_PIPELINE != "true"
  tags:
    - prod

prod_skip-deploy_job:
  stage: deploy
  script:
    - echo "This job will run only on the main branch and if SCHEDULE_PIPELINE is true"
  only:
    - main
  except:
    variables:
      - $SCHEDULE_PIPELINE == "true"
  tags:
    - prod

# Automatically create release after merging MR to main with "Release X.X.X" title
create_release:
  stage: release
  script:
    - |
      RELEASE_NAME=$(git log -1 --pretty=%B | grep -o "Release [0-9]*\.[0-9]*\.[0-9]*")
      if [ -n "$RELEASE_NAME" ]; then
        curl --request POST --header "PRIVATE-TOKEN: $PROJECT_ACCESS_TOKEN" \
          --data "name=$RELEASE_NAME" \
          --data "tag_name=$CI_COMMIT_TAG" \
          --data "ref=$CI_COMMIT_SHA" \
          "https://kiwi.whys.dev/api/v4/projects/$CI_PROJECT_ID/releases"
      fi
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_MERGE_REQUEST_TITLE =~ /^Release \d+\.\d+\.\d+$/'
version: '3.2'

services:
  agent:
    image: portainer/agent:2.18.4
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - /var/lib/docker/volumes:/var/lib/docker/volumes
    networks:
      - agent_network
    deploy:
      mode: global
      placement:
        constraints: [node.platform.os == linux]

  portainer:
    image: portainer/portainer-ce:2.18.4
    command: -H tcp://tasks.agent:9001 --tlsskipverify --sslcert /run/secrets/portainer.sslcert --sslkey /run/secrets/portainer.sslkey
    ports:
      - "9443:9443"
      - "9000:9000"
      - "8080:8080"
    volumes:
      - portainer_data:/data
    networks:
      - agent_network
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints: [node.role == manager]
    secrets:
        - portainer.sslcert
        - portainer.sslkey

networks:
  agent_network:
    driver: overlay
    attachable: true
    
volumes:
  portainer_data:

secrets:
  portainer.sslcert:
    external: true
  portainer.sslkey:
    external: true

version: '3'

services:
  web:
    container_name: web
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
      ssh:
        - default
    command: /start
    volumes:
      - ./src/backend:/app
    ports:
      - '8010:8000'
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db
      - mongo

  db:
    container_name: db
    image: postgres:15
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=symmy
      - POSTGRES_PASSWORD=symmy
      - POSTGRES_DB=symmy

  mongo:
    container_name: mongo
    image: mongo:7.0.4
    ports:
      - '27017:27017'
    environment:
      - MONGO_INITDB_ROOT_USERNAME=symmy
      - MONGO_INITDB_ROOT_PASSWORD=symmy

  rabbitmq:
    container_name: rabbitmq
    image: rabbitmq:latest
    ports:
      - '5672:5672'
      - '15672:15672'

  redis:
    container_name: redis
    image: redis:latest
    ports:
      - '6379:6379'

  celery_worker:
    container_name: celery-worker
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    command: /start-celeryworker
    volumes:
      - ./src/backend:/app
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db

  celery_beat:
    container_name: celery-beat
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    command: /start-celerybeat
    volumes:
      - ./src/backend:/app
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - db

  flower:
    container_name: flower
    build:
      context: .
      dockerfile: ./compose/local/django/Dockerfile
    command: /start-flower
    volumes:
      - ./src/backend:/app
    env_file:
      - src/backend/.env
    ports:
      - '5557:5557'
    depends_on:
      - redis
      - db

  frontend:
    container_name: frontend
    build:
      context: .
      dockerfile: ./compose/local/frontend/Dockerfile
    env_file:
      - src/frontend/.env
    ports:
      - '3000:3000'

volumes:
  flower_data:
  postgres_data:

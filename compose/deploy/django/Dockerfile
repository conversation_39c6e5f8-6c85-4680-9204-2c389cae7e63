FROM python:3.11-slim-buster

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1

### CACHED block code ###
RUN apt-get update \
  # dependencies for building Python packages
  && apt-get install -y build-essential \
  # psycopg2 dependencies
  && apt-get install -y libpq-dev \
  # fix ImportError: failed to find libmagic
  && apt-get install -y libmagic-dev \
  # Translations dependencies
  && apt-get install -y gettext \
  # git is required for pip install from git repo
  && apt-get install -y git ssh \
  # cleaning up unused files
  && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false \
  && rm -rf /var/lib/apt/lists/* \
  && apt-get update  \
  && apt-get install -y curl && apt install -y vim

# FreeTDS driver
RUN apt-get install -y freetds-bin freetds-dev tdsodbc unixodbc-dev
RUN echo "[FreeTDS] Description = FreeTDS Driver\nDriver = /usr/lib/x86_64-linux-gnu/odbc/libtdsodbc.so\nSetup = /usr/lib/x86_64-linux-gnu/odbc/libtdsS.so" >> /etc/odbcinst.ini

# ODBC driver
# https://learn.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server?view=sql-server-ver16&tabs=alpine18-install%2Cdebian17-install%2Cdebian8-install%2Credhat7-13-install%2Crhel7-offline#17
RUN curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/apt/trusted.gpg.d/microsoft.asc
RUN curl https://packages.microsoft.com/config/debian/10/prod.list | tee /etc/apt/sources.list.d/mssql-release.list
RUN apt-get update \
  && ACCEPT_EULA=Y apt-get install -y msodbcsql17 \
  && ACCEPT_EULA=Y apt-get install -y mssql-tools
RUN echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc
RUN apt-get install -y unixodbc-dev && apt-get install -y libgssapi-krb5-2


RUN mkdir -p -m 0600 ~/.ssh && \
    ssh-keyscan -H gt.whys.dev >> ~/.ssh/known_hosts && \
    ssh-keyscan -H kiwi.whys.dev >> ~/.ssh/known_hosts && \
    echo "Host kiwi.whys.dev" >> ~/.ssh/config && \
    echo "    HostName gt.whys.dev" >> ~/.ssh/config && \
    echo "    User git" >> ~/.ssh/config && \
    echo "Host *" >> ~/.ssh/config && \
    echo "    StrictHostKeyChecking no" >> ~/.ssh/config

# Requirements are installed here to ensure they will be cached.
COPY ./src/backend/requirements.txt /requirements.txt
RUN --mount=type=ssh pip install -r /requirements.txt
### END CACHED block code ###

### NO-CACHED block code ###
ARG CACHEBUST=1
# Disable the cache.
COPY ./src/backend/requirements-no-cache.txt /requirements-no-cache.txt
RUN --mount=type=ssh pip install --no-cache-dir -r /requirements-no-cache.txt

# Don't install package dependencies.
COPY ./src/backend/requirements-no-deps.txt /requirements-no-deps.txt
RUN --mount=type=ssh pip install --no-deps -r /requirements-no-deps.txt

COPY ./compose/deploy/django/entrypoint /entrypoint
RUN sed -i 's/\r$//g' /entrypoint
RUN chmod +x /entrypoint

COPY ./compose/deploy/django/start /start
RUN sed -i 's/\r$//g' /start
RUN chmod +x /start

COPY ./compose/deploy/django/celery/worker/start /start-celeryworker
RUN sed -i 's/\r$//g' /start-celeryworker
RUN chmod +x /start-celeryworker

COPY ./compose/deploy/django/celery/beat/start /start-celerybeat
RUN sed -i 's/\r$//g' /start-celerybeat
RUN chmod +x /start-celerybeat

COPY ./compose/deploy/django/celery/flower/start /start-flower
RUN sed -i 's/\r$//g' /start-flower
RUN chmod +x /start-flower

WORKDIR /app
COPY ./src/backend /app

# ENTRYPOINT ["/entrypoint"]

### END NO-CACHED block code ###
FROM python:3.11-slim-buster

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1

RUN apt-get update \
  # dependencies for building Python packages
  && apt-get install -y build-essential \
  # psycopg2 dependencies
  && apt-get install -y libpq-dev \
  # Translations dependencies
  && apt-get install -y gettext \
  # git is required for pip install from git repo
  && apt-get install -y git ssh \
  # libmagic1 is python-magic's dependency
  && apt-get install -y libmagic1 \
  # cleaning up unused files
  && apt-get purge -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false \
  && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y \
    unixodbc \
    unixodbc-dev \
    libgssapi-krb5-2 && \
    rm -rf /var/lib/apt/lists/*

RUN mkdir -p -m 0600 ~/.ssh && \
    ssh-keyscan -H gt.whys.dev >> ~/.ssh/known_hosts && \
    ssh-keyscan -H kiwi.whys.dev >> ~/.ssh/known_hosts && \
    echo "Host kiwi.whys.dev" >> ~/.ssh/config && \
    echo "    HostName gt.whys.dev" >> ~/.ssh/config && \
    echo "    User git" >> ~/.ssh/config && \
    echo "    StrictHostKeyChecking no" >> ~/.ssh/config

# Requirements are installed here to ensure they will be cached.
COPY ./src/backend/requirements.txt /requirements.txt
RUN --mount=type=ssh pip install -r /requirements.txt

# Disable the cache.
COPY ./src/backend/requirements-no-cache.txt /requirements-no-cache.txt
RUN --mount=type=ssh pip install --no-cache-dir -r /requirements-no-cache.txt

# Don't install package dependencies.
COPY ./src/backend/requirements-no-deps.txt /requirements-no-deps.txt
RUN --mount=type=ssh pip install --no-deps -r /requirements-no-deps.txt

COPY ./compose/local/django/entrypoint /entrypoint
RUN sed -i 's/\r$//g' /entrypoint
RUN chmod +x /entrypoint

COPY ./compose/local/django/start /start
RUN sed -i 's/\r$//g' /start
RUN chmod +x /start

COPY ./compose/local/django/celery/worker/start /start-celeryworker
RUN sed -i 's/\r$//g' /start-celeryworker
RUN chmod +x /start-celeryworker

COPY ./compose/local/django/celery/beat/start /start-celerybeat
RUN sed -i 's/\r$//g' /start-celerybeat
RUN chmod +x /start-celerybeat

COPY ./compose/local/django/celery/flower/start /start-flower
RUN sed -i 's/\r$//g' /start-flower
RUN chmod +x /start-flower

WORKDIR /app
COPY ./src/backend /app

ENTRYPOINT ["/entrypoint"]

from django.http import HttpResponse, HttpResponseNotFound, HttpResponseForbidden
from django.views import View

from core.models import Organization
from flows.models import Node
from mongo.operations import get_flow_store_data


class EndpointView(View):
    def get(self, request, **kwargs):
        store_data = None
        uuid = self.kwargs.get("node_uuid")

        if uuid is not None:
            node = Node.objects.get(uuid=uuid)
            if node and node.nodetype_class.name == "Endpoint":
                store_data = get_flow_store_data(node.flow)
        else:
            organization_slug = self.kwargs.get("organization")
            organization = Organization.objects.get(slug=organization_slug)
            node_slug = self.kwargs.get("slug")
            node = Node.objects.get(
                flow__project__organization=organization, slug=node_slug
            )
            if node and node.nodetype_class.name == "Endpoint":
                store_data = get_flow_store_data(node.flow)

        if store_data is not None:
            # FIXME: This is a temporary solution. There could be more than one endpoint.
            endpoint_data = store_data.get("endpoint_data", "")
            # FIXME: This is a temporary solution. We need to support any content type.
            return HttpResponse(endpoint_data, content_type="application/xml")

        return HttpResponseNotFound()

    def post(self, request):
        return HttpResponseForbidden()

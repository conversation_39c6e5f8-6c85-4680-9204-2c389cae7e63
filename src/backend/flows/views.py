import copy

from django.shortcuts import redirect
from django.urls import reverse, resolve
from django.shortcuts import render
from urllib.parse import urlparse
from django.http import HttpResponse
from django.http import Http404
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import permission_classes

from mongo.analytics import get_flow_logs, get_node_calls
from mongo.types import <PERSON>LogField, NodeCallField, Status
from mongo.operations import GridFSOperations
import datetime

from django.template.defaulttags import register
import json

from flows.models import Node, Flow
from flows.utils import cache_page_by_status


def _rendered_root_node_html(
    request, root_node: Node, origin_node: Node | None
) -> HttpResponse:

    return HttpResponse(
        render(
            request,
            "flows/admin/root_node.html",
            {"node": root_node, "flow": root_node.flow, "original": origin_node},
        ).content
    )


def _get_original_node(referer_url: str) -> Node | None:
    """
    Retrieve the original Node object based on the provided referer URL.
    """
    referer_path = urlparse(referer_url).path
    origin_resolver_match = resolve(referer_path)
    origin_view_name = origin_resolver_match.view_name

    if origin_view_name == "admin:flows_node_change":
        node_id = origin_resolver_match.kwargs.get("object_id")
        try:
            node = Node.objects.get(id=int(node_id))
        except Node.DoesNotExist:
            raise Http404(f"Node with id={node_id} doesn't exist.")
        return node


@permission_classes((IsAuthenticated,))
def create(request, uuid):
    """Creates a `node` in `Flow[uuid]`."""
    try:
        flow = Flow.objects.get(uuid=uuid)
    except Flow.DoesNotExist:
        raise Http404(f"Flow with uuid={uuid} doesn't exist.")

    node = Node.objects.create(flow=flow)

    return _rendered_root_node_html(request, node, None)


@permission_classes((IsAuthenticated,))
def create_and_append(request, uuid):
    try:
        node = Node.objects.get(uuid=uuid)
    except Node.DoesNotExist:
        raise Http404(f"Node with {uuid} doesn't exist")

    child = Node.objects.create(flow=node.flow)
    node.append(child)

    origin_node = _get_original_node(request.META.get("HTTP_REFERER"))

    return _rendered_root_node_html(request, node.root, origin_node)


@permission_classes((IsAuthenticated,))
def delete(request, uuid):
    try:
        node = Node.objects.get(uuid=uuid)
    except Node.DoesNotExist:
        raise Http404(f"Node with {uuid} doesn't exist")

    flow = node.flow.pk
    parent = node.parent
    original_node = _get_original_node(request.META["HTTP_REFERER"])
    is_original_node = original_node == node

    children = copy.copy(node.children)
    print(children)

    node.disinherit()
    print(children)
    if parent is not None:
        node.parent.remove(node)
        for child in children:
            parent.append(child)

    node.delete()

    if is_original_node:
        return redirect(reverse("admin:flows_flow_change", kwargs={"object_id": flow}))

    return _rendered_root_node_html(request, parent.root, original_node)


STATUS_COLOR = {
    Status.success.value[0]: "0 255 0",  # green
    Status.failed.value[0]: "255 0 0",  # red
    Status.retrying.value[0]: "255 255 0",  # yellow
    Status.in_progress.value[0]: "0 0 255",  # blue
    Status.skipped.value[0]: "128 128 128",  # gray
}


def _prep_date(date: None | str) -> datetime.date | None:
    if date:
        try:
            return datetime.datetime.strptime(date, "%Y-%m-%dT%H:%M:%S")
        except ValueError:
            return datetime.datetime.strptime(date, "%Y-%m-%dT%H:%M:00")


@register.filter
def get_item(dictionary: dict, key):
    return dictionary.get(key)


def get_context_logs_view(flow_id, date_to: str = None) -> dict:
    """
    View function for displaying flow logs.

    Parameters:
    - flow_id (int): The ID of the flow for which logs are to be displayed.
    - 'date_to' (optional): The end date until which logs should be fetched.

    Returns:
    - dict: A dictionary containing the following keys:
        - 'date_to' (str): The new end date until which logs are fetched.
        - 'logs' (list): A list of flow logs.
        - 'flow_id' (int): The ID of the flow.
        - 'log_color' (dict): A dictionary containing status codes and their
            corresponding colors.

    This function retrieves flow logs based on the provided
    flow ID and optional end date. It expects the 'flow_id' parameter
    to be provided in the GET request. If 'date_to' is provided,
    logs up to that date are fetched; otherwise, the latest logs are fetched.
    The response contains a dictionary with updated end date, fetched logs,
    flow ID, and status color mappings.

    """

    flow = Flow.objects.get(id=flow_id)
    flow_logs = get_flow_logs(
        flow_list=[flow],
        to=_prep_date(date_to),
        exclude_fields=[FlowLogField.node_calls],
        limit=10,
        json_serializable=True,
    )

    new_date_to = None
    logs = flow_logs["logs"]

    if flow_logs["total_count"] > 1:
        new_date_to = logs.pop(-1).get("start")
    elif logs:
        new_date_to = logs[-1].get("start")

    if new_date_to == date_to:
        logs = []

    return {
        "date_to": new_date_to,
        "logs": logs,
        "flow_id": flow_id,
        "log_color": STATUS_COLOR,
    }


@permission_classes((IsAuthenticated,))
def flow_short_logs_view(request) -> HttpResponse:
    """
    View function for displaying flow logs.

    Parameters:
    - request (HttpRequest): The HTTP request object.

    Returns:
    - HttpResponse: The HTTP response containing rendered flow logs.

    This function expects two parameters in the GET request:
    - 'flow_id': The ID of the flow for which logs are to be displayed.
    - 'date_to' (optional): The end date until which logs should be fetched.
    """

    flow_id = request.GET.get("flow_id")
    date_to = request.GET.get("date_to", "")

    try:
        return HttpResponse(
            render(
                request,
                "flows/admin/flow_logs.html",
                get_context_logs_view(flow_id, date_to),
            ).content
        )
    except Exception as e:
        return HttpResponse(
            render(
                request,
                "flows/admin/flow_logs.html",
                {
                    "error": e,
                },
            ).content
        )


@permission_classes((IsAuthenticated,))
@cache_page_by_status(60 * 15)  # cache for 15 minutes
def flow_log_node_calls(request) -> HttpResponse:
    """
    View function for displaying node-calls.

    Parameters:
    - request (HttpRequest): The HTTP request object.

    Returns:
    - HttpResponse: The HTTP response containing rendered node-calls logs.

    This function expects two parameters in the GET request:
    - 'log_id': The ID of the log for which node-calls are to be displayed.
    """

    log_id = request.GET.get("log_id")
    try:
        node_calls = get_node_calls(
            mongo_flow_ids=[log_id],
            json_serializable=True,
            exclude_fields=[
                NodeCallField.raw_data,
            ],
        ).get("node_calls")
        return HttpResponse(
            render(
                request,
                "flows/admin/flow_log_node_calls.html",
                {"node_calls": node_calls, "log_color": STATUS_COLOR},
            ).content
        )
    except Exception as e:
        return HttpResponse(
            render(
                request,
                "flows/admin/flow_log_node_calls.html",
                {
                    "error": e,
                },
            ).content
        )


@permission_classes((IsAuthenticated,))
@cache_page_by_status(60 * 15)  # cache for 15 minutes
def flow_log_node_call_data(request) -> HttpResponse:
    """
    View function for displaying node-call data.

    Parameters:
    - request (HttpRequest): The HTTP request object.

    Returns:
    - HttpResponse: The HTTP response containing rendered node-call data.

    This function expects two parameters in the GET request:
    - 'ids': The IDs of the GridFS-Data for which node-call data.
    """

    ids = request.GET.get("ids")
    try:
        ids = json.loads(ids.replace("'", '"'))
        data = GridFSOperations.get_data(file_ids=ids)
        return HttpResponse(
            render(request, "flows/admin/flow_log_node_call_data.html", {"data": data})
        )
    except Exception as e:
        return HttpResponse(
            render(
                request,
                "flows/admin/flow_log_node_call_data.html",
                {"error": f"ERROR: {e}"},
            )
        )

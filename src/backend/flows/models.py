from __future__ import annotations

import json
import logging
from typing import Iterable, Optional, Type

from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.db import models
from django.db.models.signals import post_delete, post_save, pre_save
from django.dispatch import receiver
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django_celery_beat.models import (
    ClockedSchedule,
    CrontabSchedule,
    IntervalSchedule,
    PeriodicTask,
    SolarSchedule,
)
from symmy_nodetype.connections.responses import ActionCall
from symmy_nodetype.nodetype.actions import Action
from symmy_nodetype.nodetype.node import NodeType

from connections.models import Connection
from core.exceptions import ImproperlyConfigured
from core.models import Project, SymmyBaseModel
from flows.managers import NodeManager, NodeQuerySet, NodeRelationManager
from flows.utils import get_flow_periodic_task_defaults
from mongo.operations import get_flow_store_data
from nodetypes.models import ActionResource, NodeTypeResource

logger = logging.getLogger("symmy")


def validate_nodes(flow: Flow) -> bool:
    """Checking that all node action_resource is not deprecated"""
    deprecated_nodes = flow.nodes.filter(action_resource__is_deprecated=True)
    return not deprecated_nodes.exists()


class Flow(SymmyBaseModel):
    name = models.CharField(_("Name"), max_length=255)
    project = models.ForeignKey(Project, related_name="flows", on_delete=models.CASCADE, verbose_name=_("Project"))
    periodic_task = models.OneToOneField(
        PeriodicTask,
        verbose_name=_("Periodic Task"),
        on_delete=models.PROTECT,
        related_name="flow",
        editable=False,
        default=None,
    )
    enabled = models.BooleanField(_("Enabled"), default=False)

    # TODO: is_flow_valid which will check if all actions and nodes are defined
    # TODO: check tree validity
    def execute(self, retry_backoff: int = settings.CELERY_TASK_RETRY_BACKOFF) -> str:
        from flows.tasks import execute_flow

        if not validate_nodes(self):
            msg = (
                f"Cannot execute flow {self.name} ({self.uuid}) because it contains "
                "nodes with deprecated action resources."
            )
            logger.warning(msg)
            return msg
        logger.info(f"Queuing flow {self.name} ({self.uuid}) with single instance constraint")
        try:
            res = execute_flow.delay(self.id, retry_backoff=retry_backoff)

            result = res.get()

            # Handle the new return format from execute_flow
            if isinstance(result, dict) and "error" in result:
                # This is an error response (like already running)
                logger.warning(result["error"])
                return result["error"]

            # This is a successful mongo_flow_id
            return result
        except Exception as e:
            msg = f"Flow {self.name} ({self.uuid}) failed"
            logger.exception(msg, exc_info=True)
            # return f"{msg}: {str(e)}"
            raise

    @property
    def root(self) -> Node:
        """
        Returns the root of the Flow tree
        if there's only one.

        Raises:
            Node.DoesNotExist: If there aren't any nodes or there are more than 1.

        Returns:
            Node: The root of the Flow tree.
        """

        if self.roots.count() != 1:
            raise Node.DoesNotExist("There must be exactly one root node.")

        return self.roots.first()

    @property
    def roots(self) -> NodeQuerySet:
        return self.nodes.roots()

    def save(self, *args, **kwargs):
        if self.pk is None:
            crontab, _ = CrontabSchedule.objects.get_or_create(
                minute="0",
                hour="*",
                day_of_week="*",
                day_of_month="*",
                month_of_year="*",
            )

            defaults = get_flow_periodic_task_defaults(self)
            defaults.update(
                {
                    "crontab": crontab,
                }
            )

            self.periodic_task = PeriodicTask.objects.create(**defaults)

        pt_created = self.pk is None

        super().save(*args, **kwargs)

        if pt_created:
            # INFO: Can't put that in the defaults above
            # because pk doesn't exist yet
            assert isinstance(self.periodic_task, PeriodicTask)
            self.periodic_task.args = json.dumps([self.pk])
            self.periodic_task.save()

    def __str__(self):
        return self.name


@receiver(pre_save, sender=IntervalSchedule)
@receiver(pre_save, sender=CrontabSchedule)
@receiver(pre_save, sender=SolarSchedule)
@receiver(pre_save, sender=ClockedSchedule)
def flow_pre_save(sender, instance, **kwargs):
    try:
        sender.objects.get(pk=instance.pk)
    except sender.DoesNotExist:
        pass
    else:
        raise PermissionDenied("Schedule objects cannot be modified.")


@receiver(pre_save, sender=Flow)
def flow_pre_save(sender, instance: Flow, **kwargs):
    try:
        obj = sender.objects.get(pk=instance.pk)
    except sender.DoesNotExist:
        pass
    else:
        if obj.periodic_task != instance.periodic_task:
            raise PermissionDenied("`flow.periodic_task` cannot be modified.")


@receiver(post_save, sender=Flow)
def flow_post_save(sender, instance: Flow, **kwargs):
    for attr, val in (defaults := get_flow_periodic_task_defaults(instance)).items():
        setattr(instance.periodic_task, attr, val)

    instance.periodic_task.save(update_fields=list(defaults.keys()))


@receiver(post_delete, sender=Flow)
def flow_post_delete(sender, instance: Flow, **kwargs):
    if instance.periodic_task is not None:
        instance.periodic_task.delete()


class Filter(SymmyBaseModel):
    expression = models.CharField(max_length=512)


class FlowExecutionLockRecord(SymmyBaseModel):
    """
    Database record for tracking flow execution locks.
    Provides persistence and crash recovery for the locking mechanism.
    """
    flow = models.OneToOneField(
        Flow,
        on_delete=models.CASCADE,
        related_name="execution_lock_record",
        verbose_name=_("Flow")
    )
    mongo_flow_id = models.CharField(
        _("MongoDB Flow ID"),
        max_length=24,
        help_text=_("MongoDB ObjectId of the flow execution log")
    )
    locked_at = models.DateTimeField(
        _("Locked At"),
        default=timezone.now,
        help_text=_("When the lock was acquired")
    )
    locked_by_task_id = models.CharField(
        _("Locked By Task ID"),
        max_length=255,
        help_text=_("Celery task ID that acquired the lock")
    )
    redis_lock_key = models.CharField(
        _("Redis Lock Key"),
        max_length=255,
        help_text=_("Redis key used for the lock")
    )
    timeout_at = models.DateTimeField(
        _("Timeout At"),
        help_text=_("When the lock will timeout")
    )

    class Meta:
        verbose_name = _("Flow Execution Lock Record")
        verbose_name_plural = _("Flow Execution Lock Records")
        indexes = [
            models.Index(fields=['locked_at']),
            models.Index(fields=['timeout_at']),
        ]

    def __str__(self):
        return f"Lock for {self.flow.name} (locked at {self.locked_at})"

    @property
    def is_expired(self):
        """Check if the lock has expired based on timeout."""
        return timezone.now() > self.timeout_at

    @property
    def time_remaining(self):
        """Get remaining time before lock expires."""
        remaining = self.timeout_at - timezone.now()
        return remaining if remaining.total_seconds() > 0 else timezone.timedelta(0)


class Node(SymmyBaseModel):
    flow = models.ForeignKey(Flow, related_name="nodes", on_delete=models.CASCADE)
    node_type_resource = models.ForeignKey(
        NodeTypeResource, null=True, on_delete=models.SET_NULL
    )
    action_resource = models.ForeignKey(
        ActionResource, blank=True, null=True, on_delete=models.SET_NULL
    )
    connection = models.ForeignKey(
        Connection, verbose_name=_("Connection"), null=True, on_delete=models.SET_NULL
    )
    input = models.JSONField(_("Input"), default=dict, blank=True)

    slug = models.SlugField(
        _("Slug"), max_length=255, unique=True, null=True, blank=True
    )

    objects = NodeManager()

    @cached_property
    def nodetype_class(self) -> Type[NodeType] | None:
        """
        Gets and returns the `Node` class
        referenced by the `node_type: NodeTypeResource` field.
        """

        if self.node_type_resource is None:
            return None

        return self.node_type_resource.nodetype_class

    @cached_property
    def node_instance(self) -> NodeType:
        nt_class = self.nodetype_class
        try:
            conn = self.connection.authenticate()
        except AttributeError:
            raise ImproperlyConfigured(
                f"Connection not defined for node: {self}. "
                f"Create a Connection object with foreign key to this NodeTypeResource."
            )
        return nt_class(connection=conn)

    @property
    def actions(self) -> Iterable[Action] | None:
        """Gets and returns `self.node`'s actions."""

        if self.node_instance is None:
            return None

        return self.node_instance.actions

    @property
    def root(self) -> Node:
        """Returns the eldest ancestor of the `Node` or self if no ancestors found."""

        return self.ancestor_relations.last().ancestor

    @property
    def ancestors(self) -> NodeQuerySet:
        """
        Gets all ancestor `Node` objects in a `NodeQuerySet`.
        Doesn't include `self` in ancestry.
        """

        return self.ancestor_relations.ancestors()

    @property
    def descendants(self) -> NodeQuerySet:
        """
        Gets all descendant `Node` objects in a `NodeQuerySet`.
        Doesn't include `self` in ancestry.
        """

        return self.descendant_relations.descendants()

    @property
    def parent(self) -> Node | None:
        """Gets and returns the immediate ancestor of this `Node`."""

        parent_relation = self.ancestor_relations.filter(depth=1).first()
        return parent_relation.ancestor if parent_relation else None

    @property
    def children(self) -> NodeQuerySet:
        """Gets and returns the immediate children of this `Node` in a `NodeQuerySet`."""

        return self.get_descendants(depth=1, include_self=False)

    @property
    def is_root(self) -> bool:
        """Returns whether `self` has no immediate parents."""

        return self == self.root

    @property
    def related(self) -> NodeQuerySet:
        """Gets and returns all `Node` objects linked by a relation to this `Node`."""

        return self.ancestor_relations.ancestors(
            include_self=True
        ) | self.descendant_relations.descendants(include_self=True)

    @property
    def callback_url(self) -> Optional[str]:
        if not self.action_resource or not self.action_resource.webhook:
            return

        return settings.BASE_URL + reverse(
            "api:v1:webhooks:process-webhook",
            kwargs={
                "flow_uuid": self.flow.uuid,
                "action_uuid": self.action_resource.uuid,
            },
        )

    def get_descendants(
        self, depth: int = 1, include_self: bool = False
    ) -> NodeQuerySet:
        """
        Gets all descendent `Node` objects up to a certain depth.

        Args:
            depth (int, optional): Depth up to which to search for descendents. Defaults to 1.
            include_self (bool, optional): Whether to include self in the results. Defaults to False.
        """

        filtered_descendants = self.descendant_relations.filter(
            depth__range=(0 if include_self else 1, depth)
        )

        return Node.objects.filter(pk__in=filtered_descendants.values("descendant_id"))

    def is_related(self, node: Node) -> bool:
        """
        Returns whether `node` is related to `self`.

        Args:
            node (Node): Node to check relation with.
        """

        return node in self.related

    def append(self, descendant: Node) -> bool:
        """
        Appends `descendant` to the current `Node` (`self`) instance;
        I.e., `NodeRelation` objects to `descendant` are created for
        each ancestor in `self`.

        Args:
            descendant (Node): Node to be appended.

        Returns:
            bool: Whether the operation was successful.
        """

        try:
            if descendant == self.root or descendant.parent is not None:
                return False

            for ancestor_relation in self.ancestor_relations.all():
                for descendant_relation in descendant.descendant_relations.all():
                    NodeRelation.objects.create(
                        ancestor=ancestor_relation.ancestor,
                        descendant=descendant_relation.descendant,
                        depth=ancestor_relation.depth + descendant_relation.depth + 1,
                    )

            return True
        except Exception as ex:
            print(ex)

    def remove(self, child: Node) -> bool:
        """Disconnects `self` from `child`."""

        return Node.disconnect(self, child)

    def disinherit(self):
        """Disconnects `self` from all children."""

        for child in self.children:
            Node.disconnect(parent=self, child=child)

    @staticmethod
    def disconnect(parent: Node, child: Node) -> bool:
        """
        Disconnects the relation `parent`---`child`
        if the `parent` is the immediate ancestor of the `child` node.

        Args:
            parent (Node): The ancestor node.
            child (Node): The `parent`'s immediate descendant.

        Returns:
            bool: Whether the operation was successful.
        """

        if parent != child.parent:
            return False

        relations = NodeRelation.objects.none()

        for ancestor in parent.ancestor_relations.ancestors(include_self=True):
            for descendant in child.descendant_relations.descendants(include_self=True):
                relations |= NodeRelation.objects.filter(
                    ancestor=ancestor, descendant=descendant
                )

        relations._raw_delete(relations.db)

        return True

    @staticmethod
    def insert(parent: Node, child: Node, node: Node) -> bool:
        """
        Disconnects the `parent` and `child`.
        Appends `node` to `parent`, and then appends `child` to `node`.

        Args:
            parent (Node): The ancestor node.
            child (Node): The `parent`'s immediate descendant.
            node (Node): The node to be inserted between the `parent` and `child`.

        Returns:
            bool: Whether the operation was successful.
        """

        if node.root == parent:
            return False

        if not Node.disconnect(parent=parent, child=child):
            return False

        node.disinherit()

        parent.append(node)
        node.append(child)

        return True

    @staticmethod
    def post_create(sender, instance, created, *args, **kwargs):
        """Post create signal which creates a NodeRelation from the node to itself."""

        if not created:
            return

        NodeRelation.objects.create(ancestor=instance, descendant=instance)

    def execute(self, data: dict) -> ActionCall:
        # Load flow data from mongo
        flow_store_data = {"store": get_flow_store_data(self.flow)}
        data.update(flow_store_data)
        return self.action_resource.execute(data, node=self.node_instance)

    def str_for_template(self):
        if self.action_resource is not None:
            return f"{self.action_resource.name} ({self.node_type_resource.name})"
        return f"Node ({self.pk})"

    def __str__(self):
        if self.node_type_resource is not None:
            return f"{self.node_type_resource} in {self.flow}"

        return super().__str__()


class NodeRelation(SymmyBaseModel):
    ancestor = models.ForeignKey(
        Node, related_name="descendant_relations", on_delete=models.CASCADE
    )
    descendant = models.ForeignKey(
        Node, related_name="ancestor_relations", on_delete=models.CASCADE
    )
    depth = models.IntegerField(default=0)

    objects = NodeRelationManager()

    class Meta:
        unique_together = [
            # To prevent duplicating
            ["ancestor", "descendant"],
            # You cannot have more than 1 ancestor from the same level
            ["descendant", "depth"],
        ]
        ordering = ["depth"]

    def __str__(self) -> str:
        return f"{self.ancestor}->{self.descendant} ({self.depth})"


post_save.connect(Node.post_create, sender=Node)

from contextlib import contextmanager
from typing import TYPE_CHECKING
import logging

from django.views.decorators.cache import cache_page
from django.core.cache import cache

from mongo.types import Status

if TYPE_CHECKING:
    from flows.models import Flow
    from flows.tasks import NodeTask

logger = logging.getLogger("symmy")


@contextmanager
def handle_exception_in_task(
    node_task_instance: "NodeTask", msg_key: str | None = None
):
    try:
        yield
    except Exception as ex:
        node_task_instance.node_call_log_operations.errors.append(ex)
        node_task_instance.task_logger.log_failed(msg_key)
        raise ex


def cache_page_by_status(timeout, *, cache=None, key_prefix=None):
    """
    A decorator to cache a view function's output based on
    the status parameter in the request.

    This decorator checks if the 'status' parameter in the request
    is equal to the value
    representing 'in_progress' status. If it is, the view function
    is called without caching.
    Otherwise, the output of the view function is cached using
    the specified timeout.

    Args:
        timeout (int): The timeout value for caching in seconds.
        cache (str, optional): The name of the cache alias.
        key_prefix (str, optional): A prefix for cache keys.

    Returns:
        function: The decorator function.
    """

    def decorator(view_func):
        def _wrapped_view_func(request, *args, **kwargs):
            if request.GET.get("status") == str(Status.in_progress.value[0]):
                return view_func(request, *args, **kwargs)
            else:
                return cache_page(timeout, cache=cache, key_prefix=key_prefix)(
                    view_func
                )(request, *args, **kwargs)

        return _wrapped_view_func

    return decorator


def get_flow_periodic_task_defaults(flow: "Flow") -> dict:
    return {
        "task": "flows.tasks.execute_flow",
        "name": f"{flow.name} ({flow.uuid})",
        "enabled": flow.enabled,
    }


class FlowExecutionLock:
    """
    Enhanced Redis-based locking mechanism with database persistence to ensure
    only one flow task can run per Flow instance. Provides crash recovery and
    flow completion tracking.
    """

    def __init__(self, flow_id: int, timeout: int = 1200, mongo_flow_id: str = None, task_id: str = None):
        self.flow_id = flow_id
        self.lock_key = f"flow_execution_lock_{flow_id}"
        self.timeout = timeout
        self.mongo_flow_id = mongo_flow_id
        self.task_id = task_id
        self._lock = None
        self._db_record = None

    def acquire(self, blocking: bool = False) -> bool:
        """
        Acquire the lock for flow execution with database persistence.

        Args:
            blocking: Whether to block until lock is available

        Returns:
            True if lock was acquired, False otherwise
        """
        try:
            from django_redis import get_redis_connection
            from django.utils import timezone
            from django.db import transaction

            # Check for existing database lock record first
            if self._check_existing_db_lock():
                logger.warning(f"Flow {self.flow_id} already has an active database lock record")
                return False

            redis_client = get_redis_connection("default")

            self._lock = redis_client.lock(
                self.lock_key,
                timeout=self.timeout,
                blocking=blocking
            )

            acquired = self._lock.acquire(blocking=blocking)

            if acquired:
                # Create database record for persistence
                if self._create_db_record():
                    logger.info(f"Acquired execution lock for flow {self.flow_id} with database persistence")
                    return True
                else:
                    # If DB record creation fails, release Redis lock
                    self._lock.release()
                    logger.error(f"Failed to create database lock record for flow {self.flow_id}")
                    return False
            else:
                logger.warning(f"Could not acquire Redis lock for flow {self.flow_id} - another instance may be running")
                return False

        except Exception as e:
            logger.error(f"Error acquiring flow execution lock for flow {self.flow_id}: {str(e)}")
            if self._lock:
                try:
                    self._lock.release()
                except:
                    pass
            return False

    def release(self):
        """Release the flow execution lock and clean up database record."""
        try:
            # Release Redis lock
            if self._lock:
                self._lock.release()
                logger.info(f"Released Redis lock for flow {self.flow_id}")

            # Clean up database record
            self._cleanup_db_record()

        except Exception as e:
            logger.error(f"Error releasing flow execution lock for flow {self.flow_id}: {str(e)}")

    def _check_existing_db_lock(self) -> bool:
        """Check if there's an existing active database lock record."""
        try:
            from flows.models import FlowExecutionLockRecord
            from django.utils import timezone

            # Check for existing non-expired lock
            existing_lock = FlowExecutionLockRecord.objects.filter(
                flow_id=self.flow_id,
                timeout_at__gt=timezone.now()
            ).first()

            return existing_lock is not None

        except Exception as e:
            logger.error(f"Error checking existing database lock for flow {self.flow_id}: {str(e)}")
            return False

    def _create_db_record(self) -> bool:
        """Create database record for lock persistence."""
        try:
            from flows.models import FlowExecutionLockRecord, Flow
            from django.utils import timezone
            from django.db import transaction

            if not self.mongo_flow_id or not self.task_id:
                logger.warning(f"Missing mongo_flow_id or task_id for flow {self.flow_id} lock record")
                return False

            with transaction.atomic():
                flow = Flow.objects.get(id=self.flow_id)
                timeout_at = timezone.now() + timezone.timedelta(seconds=self.timeout)

                self._db_record = FlowExecutionLockRecord.objects.create(
                    flow=flow,
                    mongo_flow_id=self.mongo_flow_id,
                    locked_by_task_id=self.task_id,
                    redis_lock_key=self.lock_key,
                    timeout_at=timeout_at
                )

                logger.info(f"Created database lock record for flow {self.flow_id}")
                return True

        except Exception as e:
            logger.error(f"Error creating database lock record for flow {self.flow_id}: {str(e)}")
            return False

    def _cleanup_db_record(self):
        """Clean up the database lock record."""
        try:
            if self._db_record:
                self._db_record.delete()
                logger.info(f"Cleaned up database lock record for flow {self.flow_id}")
            else:
                # Try to find and clean up any existing record
                from flows.models import FlowExecutionLockRecord
                FlowExecutionLockRecord.objects.filter(flow_id=self.flow_id).delete()

        except Exception as e:
            logger.error(f"Error cleaning up database lock record for flow {self.flow_id}: {str(e)}")

    def __enter__(self):
        if not self.acquire():
            raise FlowExecutionLockError(f"Could not acquire execution lock for flow {self.flow_id}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()


class FlowExecutionLockError(Exception):
    """Raised when flow execution lock cannot be acquired."""
    pass


def clear_stale_flow_locks():
    """
    Enhanced utility function to clear stale flow execution locks from both Redis and database.
    Useful for cleanup after worker crashes or system restarts.
    """
    try:
        from django_redis import get_redis_connection
        from flows.models import FlowExecutionLockRecord
        from django.utils import timezone

        redis_client = get_redis_connection("default")
        cleared_count = 0

        # Clear Redis locks
        redis_keys = redis_client.keys("flow_execution_lock_*")
        if redis_keys:
            redis_client.delete(*redis_keys)
            cleared_count += len(redis_keys)
            logger.info(f"Cleared {len(redis_keys)} Redis flow execution locks")

        # Clear expired database lock records
        expired_records = FlowExecutionLockRecord.objects.filter(
            timeout_at__lt=timezone.now()
        )
        expired_count = expired_records.count()
        if expired_count > 0:
            expired_records.delete()
            cleared_count += expired_count
            logger.info(f"Cleared {expired_count} expired database lock records")

        if cleared_count == 0:
            logger.info("No stale flow execution locks found")

        return cleared_count

    except Exception as e:
        logger.error(f"Error clearing stale flow locks: {str(e)}")
        return 0


def register_flow_completion_callback(mongo_flow_id: str):
    """
    Register a callback to release flow lock when flow completes.
    This ensures locks are held for the entire flow lifecycle.
    """
    try:
        from flows.models import FlowExecutionLockRecord

        # Find the lock record by mongo_flow_id
        lock_record = FlowExecutionLockRecord.objects.filter(
            mongo_flow_id=mongo_flow_id
        ).first()

        if lock_record:
            # Store the callback information in the lock record
            # The actual callback will be triggered by flow completion detection
            logger.info(f"Registered flow completion callback for mongo_flow_id {mongo_flow_id}")
            return True
        else:
            logger.warning(f"No lock record found for mongo_flow_id {mongo_flow_id}")
            return False

    except Exception as e:
        logger.error(f"Error registering flow completion callback: {str(e)}")
        return False


def handle_flow_completion(mongo_flow_id: str, flow_status: str):
    """
    Handle flow completion by releasing the associated lock.
    Called when a flow reaches a final state (success, failed, etc.).
    """
    try:
        from flows.models import FlowExecutionLockRecord
        from django_redis import get_redis_connection

        # Find and clean up the lock record
        lock_record = FlowExecutionLockRecord.objects.filter(
            mongo_flow_id=mongo_flow_id
        ).first()

        if lock_record:
            # Release Redis lock if it still exists
            try:
                redis_client = get_redis_connection("default")
                redis_client.delete(lock_record.redis_lock_key)
                logger.info(f"Released Redis lock {lock_record.redis_lock_key}")
            except Exception as redis_error:
                logger.warning(f"Could not release Redis lock: {redis_error}")

            # Delete the database record
            flow_id = lock_record.flow.id
            lock_record.delete()

            logger.info(f"Flow {flow_id} completed with status '{flow_status}' - lock released")
            return True
        else:
            logger.debug(f"No lock record found for completed flow {mongo_flow_id}")
            return False

    except Exception as e:
        logger.error(f"Error handling flow completion for {mongo_flow_id}: {str(e)}")
        return False


def get_active_flow_locks():
    """
    Get information about currently active flow execution locks from both Redis and database.

    Returns:
        dict: Dictionary with flow_id as key and comprehensive lock info as value
    """
    try:
        from django_redis import get_redis_connection
        from flows.models import FlowExecutionLockRecord
        from django.utils import timezone

        redis_client = get_redis_connection("default")
        active_locks = {}

        # Get Redis locks
        redis_keys = redis_client.keys("flow_execution_lock_*")
        for key in redis_keys:
            key_str = key.decode('utf-8') if isinstance(key, bytes) else key
            flow_id = key_str.replace("flow_execution_lock_", "")
            ttl = redis_client.ttl(key)

            active_locks[flow_id] = {
                "redis_key": key_str,
                "redis_ttl_seconds": ttl,
                "redis_expires_in": f"{ttl // 60}m {ttl % 60}s" if ttl > 0 else "expired",
                "has_redis_lock": True,
                "has_db_record": False
            }

        # Get database lock records
        db_locks = FlowExecutionLockRecord.objects.all()
        for db_lock in db_locks:
            flow_id = str(db_lock.flow.id)
            time_remaining = db_lock.time_remaining

            if flow_id in active_locks:
                # Update existing entry with database info
                active_locks[flow_id].update({
                    "has_db_record": True,
                    "db_locked_at": db_lock.locked_at,
                    "db_timeout_at": db_lock.timeout_at,
                    "db_expires_in": f"{int(time_remaining.total_seconds() // 60)}m {int(time_remaining.total_seconds() % 60)}s" if time_remaining.total_seconds() > 0 else "expired",
                    "mongo_flow_id": db_lock.mongo_flow_id,
                    "task_id": db_lock.locked_by_task_id,
                    "is_expired": db_lock.is_expired
                })
            else:
                # Database record without Redis lock (possibly stale)
                active_locks[flow_id] = {
                    "has_redis_lock": False,
                    "has_db_record": True,
                    "db_locked_at": db_lock.locked_at,
                    "db_timeout_at": db_lock.timeout_at,
                    "db_expires_in": f"{int(time_remaining.total_seconds() // 60)}m {int(time_remaining.total_seconds() % 60)}s" if time_remaining.total_seconds() > 0 else "expired",
                    "mongo_flow_id": db_lock.mongo_flow_id,
                    "task_id": db_lock.locked_by_task_id,
                    "is_expired": db_lock.is_expired,
                    "status": "stale_db_record" if db_lock.is_expired else "db_only"
                }

        return active_locks

    except Exception as e:
        logger.error(f"Error getting active flow locks: {str(e)}")
        return {}

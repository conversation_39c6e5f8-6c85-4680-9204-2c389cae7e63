from django.core.management.base import BaseCommand
from flows.utils import clear_stale_flow_locks, get_active_flow_locks


class Command(BaseCommand):
    help = 'Clear stale flow execution locks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--list',
            action='store_true',
            help='List active flow locks without clearing them',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force clear all locks without confirmation',
        )

    def handle(self, *args, **options):
        if options['list']:
            self.list_active_locks()
        else:
            self.clear_locks(force=options['force'])

    def list_active_locks(self):
        """List all currently active flow execution locks."""
        self.stdout.write("Checking for active flow execution locks...")

        active_locks = get_active_flow_locks()

        if not active_locks:
            self.stdout.write(
                self.style.SUCCESS("No active flow execution locks found.")
            )
            return

        self.stdout.write(f"Found {len(active_locks)} active flow execution locks:")

        for flow_id, lock_info in active_locks.items():
            redis_status = "✓" if lock_info.get('has_redis_lock', False) else "✗"
            db_status = "✓" if lock_info.get('has_db_record', False) else "✗"

            status_info = f"Redis: {redis_status}, DB: {db_status}"

            if lock_info.get('has_redis_lock'):
                time_info = lock_info.get('redis_expires_in', 'unknown')
            elif lock_info.get('has_db_record'):
                time_info = lock_info.get('db_expires_in', 'unknown')
            else:
                time_info = 'unknown'

            self.stdout.write(
                f"  Flow ID {flow_id}: {time_info} remaining ({status_info})"
            )

            if lock_info.get('mongo_flow_id'):
                self.stdout.write(f"    MongoDB Flow ID: {lock_info['mongo_flow_id']}")
            if lock_info.get('task_id'):
                self.stdout.write(f"    Task ID: {lock_info['task_id']}")
            if lock_info.get('status'):
                self.stdout.write(f"    Status: {lock_info['status']}")

    def clear_locks(self, force=False):
        """Clear all stale flow execution locks."""
        if not force:
            # Show current locks first
            active_locks = get_active_flow_locks()

            if not active_locks:
                self.stdout.write(
                    self.style.SUCCESS("No flow execution locks found to clear.")
                )
                return

            self.stdout.write(f"Found {len(active_locks)} flow execution locks:")
            for flow_id, lock_info in active_locks.items():
                redis_status = "✓" if lock_info.get('has_redis_lock', False) else "✗"
                db_status = "✓" if lock_info.get('has_db_record', False) else "✗"

                if lock_info.get('has_redis_lock'):
                    time_info = lock_info.get('redis_expires_in', 'unknown')
                elif lock_info.get('has_db_record'):
                    time_info = lock_info.get('db_expires_in', 'unknown')
                else:
                    time_info = 'unknown'

                self.stdout.write(
                    f"  Flow ID {flow_id}: {time_info} remaining (Redis: {redis_status}, DB: {db_status})"
                )

            # Ask for confirmation
            confirm = input("\nAre you sure you want to clear all these locks? (y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                self.stdout.write("Operation cancelled.")
                return

        # Clear the locks
        cleared_count = clear_stale_flow_locks()

        if cleared_count > 0:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully cleared {cleared_count} flow execution locks.")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("No flow execution locks found to clear.")
            )

"""
Django management command to set up periodic tasks for flow monitoring.
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta


class Command(BaseCommand):
    help = 'Set up periodic tasks for flow monitoring and cleanup'

    def add_arguments(self, parser):
        parser.add_argument(
            '--check-interval',
            type=int,
            default=10,
            help='Interval in minutes for checking stuck flows (default: 10)',
        )
        parser.add_argument(
            '--cleanup-interval',
            type=int,
            default=30,
            help='Interval in minutes for cleaning up expired locks (default: 30)',
        )
        parser.add_argument(
            '--remove-existing',
            action='store_true',
            help='Remove existing periodic tasks before creating new ones',
        )
        parser.add_argument(
            '--list-only',
            action='store_true',
            help='List existing periodic tasks without making changes',
        )

    def handle(self, *args, **options):
        try:
            from django_celery_beat.models import PeriodicTask, IntervalSchedule
        except ImportError:
            self.stdout.write(
                self.style.ERROR(
                    "django-celery-beat is not installed. "
                    "Please install it to use periodic tasks."
                )
            )
            return

        if options['list_only']:
            self.list_existing_tasks()
            return

        check_interval = options['check_interval']
        cleanup_interval = options['cleanup_interval']
        remove_existing = options['remove_existing']

        self.stdout.write(f"Setting up flow monitoring periodic tasks...")
        self.stdout.write(f"  - Stuck flow check interval: {check_interval} minutes")
        self.stdout.write(f"  - Lock cleanup interval: {cleanup_interval} minutes")

        if remove_existing:
            self.remove_existing_tasks()

        # Create or update the stuck flow check task
        self.setup_stuck_flow_check_task(check_interval)
        
        # Create or update the lock cleanup task
        self.setup_lock_cleanup_task(cleanup_interval)

        self.stdout.write(
            self.style.SUCCESS("Successfully set up flow monitoring periodic tasks!")
        )

    def list_existing_tasks(self):
        """List existing flow monitoring periodic tasks."""
        try:
            from django_celery_beat.models import PeriodicTask
            
            task_names = [
                'Check Stuck Flows',
                'Clear Expired Flow Locks'
            ]
            
            self.stdout.write("Existing flow monitoring periodic tasks:")
            
            for task_name in task_names:
                try:
                    task = PeriodicTask.objects.get(name=task_name)
                    status = "enabled" if task.enabled else "disabled"
                    interval_info = ""
                    
                    if task.interval:
                        interval_info = f"every {task.interval.every} {task.interval.period}"
                    elif task.crontab:
                        interval_info = f"cron: {task.crontab}"
                    
                    self.stdout.write(f"  ✓ {task_name}: {status}, {interval_info}")
                    
                except PeriodicTask.DoesNotExist:
                    self.stdout.write(f"  ✗ {task_name}: not found")
                    
        except ImportError:
            self.stdout.write(
                self.style.ERROR("django-celery-beat is not installed.")
            )

    def remove_existing_tasks(self):
        """Remove existing flow monitoring periodic tasks."""
        try:
            from django_celery_beat.models import PeriodicTask
            
            task_names = [
                'Check Stuck Flows',
                'Clear Expired Flow Locks'
            ]
            
            removed_count = 0
            for task_name in task_names:
                try:
                    task = PeriodicTask.objects.get(name=task_name)
                    task.delete()
                    removed_count += 1
                    self.stdout.write(f"  Removed existing task: {task_name}")
                except PeriodicTask.DoesNotExist:
                    pass
                    
            if removed_count > 0:
                self.stdout.write(f"Removed {removed_count} existing tasks.")
            else:
                self.stdout.write("No existing tasks found to remove.")
                
        except ImportError:
            pass

    def setup_stuck_flow_check_task(self, interval_minutes):
        """Set up the periodic task for checking stuck flows."""
        try:
            from django_celery_beat.models import PeriodicTask, IntervalSchedule
            
            # Create or get the interval schedule
            schedule, created = IntervalSchedule.objects.get_or_create(
                every=interval_minutes,
                period=IntervalSchedule.MINUTES,
            )
            
            # Create or update the periodic task
            task, created = PeriodicTask.objects.update_or_create(
                name='Check Stuck Flows',
                defaults={
                    'task': 'flows.tasks.check_stuck_flows',
                    'interval': schedule,
                    'enabled': True,
                    'description': 'Check for flows stuck in in_progress status and timeout them',
                }
            )
            
            action = "Created" if created else "Updated"
            self.stdout.write(f"  {action} stuck flow check task (every {interval_minutes} minutes)")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up stuck flow check task: {str(e)}")
            )

    def setup_lock_cleanup_task(self, interval_minutes):
        """Set up the periodic task for cleaning up expired locks."""
        try:
            from django_celery_beat.models import PeriodicTask, IntervalSchedule
            
            # Create or get the interval schedule
            schedule, created = IntervalSchedule.objects.get_or_create(
                every=interval_minutes,
                period=IntervalSchedule.MINUTES,
            )
            
            # Create or update the periodic task
            task, created = PeriodicTask.objects.update_or_create(
                name='Clear Expired Flow Locks',
                defaults={
                    'task': 'flows.tasks.clear_expired_flow_locks',
                    'interval': schedule,
                    'enabled': True,
                    'description': 'Clear expired flow execution locks as backup cleanup',
                }
            )
            
            action = "Created" if created else "Updated"
            self.stdout.write(f"  {action} lock cleanup task (every {interval_minutes} minutes)")
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error setting up lock cleanup task: {str(e)}")
            )

import sympy
from sympy import sympify


def _is_invalid(c):
    return c.isalpha() or c == "_"


def is_safe(expression: str) -> bool:
    """
    Blacklist attribute access, simply by checking for any period that is
    not surrounded by numbers. Returns True for '3.4', but not for 'a.b'
    Args:
        expression (str): expression to be checked

    Returns: True if safe, False otherwise
    """

    components = expression.split(".")
    if len(components) == 1:
        return True
    for c in components:
        if _is_invalid(c[0]) or _is_invalid(c[-1]):
            return False
    return True


def evaluate(expression: str) -> int | float | str:
    """
    If safe, sympifies the expression and converts it to the appropriate type.

    Args:
        expression (str): expression to be evaluated

    Returns: evaluated expression
    """

    # if not is_safe(expression) and "<?xml" not in expression:
    #     return expression

    result = sympify(expression)

    match type(result):
        case sympy.core.numbers.Integer:
            return int(result)
        case sympy.core.numbers.Float | sympy.core.numbers.Rational:
            return float(result)
        case sympy.core.symbol.Symbol:
            return str(result)

    return result

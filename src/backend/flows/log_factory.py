import datetime
import pickle
from collections import deque
import uuid

from django.utils import timezone

from mongo.utils import status_as_dict
from mongo.types import Status


def create_log_str(log_info: dict) -> str:
    log_string = ""
    for (k, v) in log_info.items():
        log_string += f"{str(k)} : {str(v)}\n"
    return log_string


def flow_msg(flow, status: Status) -> str:
    log_info = {
        "id": flow.uuid,
        "name": flow.name,
        "timestamp": timezone.now().strftime("%d-%m-%Y %H:%M:%S"),
        "status": status_as_dict(status),
    }
    return create_log_str(log_info)


def node_msg(node, status: Status) -> str:
    log_info = {
        "id": node.uuid,
        "flow_id": node.flow.uuid,
        "name": node.node_instance.name,
        "action": node.action_resource.name,
        "timestamp": timezone.now().strftime("%d-%m-%Y %H:%M:%S"),
        "status": status_as_dict(status),
    }
    return create_log_str(log_info)


def construct_raw_data(action_call) -> dict[str, dict]:
    def get_raw_data(ac):
        data = dict()
        for unified_response in ac.responses:
            data[f"{action_call.action.name}_{str(uuid.uuid4())[:8]}"] = {
                "data": unified_response.response,
                "type": type(unified_response.response),
            }
        return data

    raw_data: dict[str, dict] = get_raw_data(action_call)

    action_calls = deque(action_call.action_calls)
    while len(action_calls) != 0:
        curr_action_call = action_calls.popleft()
        raw_data.update(get_raw_data(curr_action_call))
        action_calls += curr_action_call.action_calls

    return raw_data

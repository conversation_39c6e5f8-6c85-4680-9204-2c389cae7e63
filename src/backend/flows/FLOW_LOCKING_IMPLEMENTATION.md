# Flow Execution Locking Implementation

## Overview

This document describes the enhanced flow execution locking mechanism that ensures mutual exclusion for flow execution with the following key features:

- **Scope**: Only one instance of each unique flow can execute at any given time
- **Duration**: Locks remain active for the entire flow lifecycle, including child node execution
- **Release**: Locks are released immediately upon flow completion (success or failure)
- **Performance**: Efficient handling of hundreds of concurrent flows
- **Reliability**: Crash recovery and cleanup mechanisms

## Architecture

### Components

1. **FlowExecutionLockRecord** (Database Model)
   - Persistent storage for lock information
   - Provides crash recovery capabilities
   - Tracks lock metadata (task ID, MongoDB flow ID, timeout)

2. **FlowExecutionLock** (Enhanced Lock Class)
   - Redis-based fast locking with database persistence
   - Context manager support
   - Automatic cleanup on release

3. **Flow Completion Callbacks**
   - Automatic lock release when flows complete
   - Triggered by MongoDB flow status updates
   - Handles both success and failure scenarios

4. **Management Commands**
   - Enhanced lock monitoring and cleanup
   - Stale lock detection and removal

### Lock Lifecycle

```
1. Flow Execution Request
   ↓
2. Create MongoDB Flow Log
   ↓
3. Acquire FlowExecutionLock (Redis + DB)
   ↓
4. Start Flow Execution (Root + Child Tasks)
   ↓
5. Flow Completion Detection
   ↓
6. Trigger Completion Callback
   ↓
7. Release Lock (Redis + DB Cleanup)
```

## Implementation Details

### Database Model

```python
class FlowExecutionLockRecord(SymmyBaseModel):
    flow = models.OneToOneField(Flow, ...)
    mongo_flow_id = models.CharField(max_length=24)
    locked_at = models.DateTimeField(default=timezone.now)
    locked_by_task_id = models.CharField(max_length=255)
    redis_lock_key = models.CharField(max_length=255)
    timeout_at = models.DateTimeField()
```

### Enhanced Lock Class

```python
class FlowExecutionLock:
    def __init__(self, flow_id, timeout=1200, mongo_flow_id=None, task_id=None):
        # Hybrid Redis + Database locking
        
    def acquire(self, blocking=False):
        # 1. Check existing database locks
        # 2. Acquire Redis lock
        # 3. Create database record
        
    def release(self):
        # 1. Release Redis lock
        # 2. Clean up database record
```

### Flow Completion Detection

The system detects flow completion in `mongo/operations.py`:

```python
def _set_flow_final_status(self, node_calls):
    # Update flow status in MongoDB
    # Trigger completion callback
    handle_flow_completion(mongo_flow_id, status)
```

## Key Features

### 1. Mutual Exclusion
- Only one instance per flow ID can execute
- Prevents resource conflicts and data corruption
- Fast Redis-based lock acquisition

### 2. Full Lifecycle Coverage
- Locks held from flow start to completion
- Covers all child node execution
- No premature lock release

### 3. Crash Recovery
- Database persistence survives worker crashes
- Automatic cleanup of stale locks
- Timeout-based expiration

### 4. Performance Optimization
- Redis for fast lock operations
- Database only for persistence
- Efficient cleanup mechanisms

### 5. Monitoring and Management
- Enhanced management commands
- Detailed lock status reporting
- Manual cleanup capabilities

## Usage Examples

### Basic Flow Execution
```python
# Automatic locking in flow execution
flow = Flow.objects.get(id=1)
result = flow.execute()  # Handles locking automatically
```

### Manual Lock Management
```python
# Manual lock usage (for testing/debugging)
with FlowExecutionLock(flow_id=1, mongo_flow_id="...", task_id="...") as lock:
    # Flow execution logic
    pass
```

### Lock Monitoring
```bash
# List active locks
python manage.py clear_flow_locks --list

# Clear stale locks
python manage.py clear_flow_locks --force
```

## Error Handling

### Lock Acquisition Failures
- Returns error message instead of raising exceptions
- Creates skipped flow log entries
- Graceful degradation

### Timeout Handling
- 20-minute default timeout
- Automatic cleanup on timeout
- Flow status updated to failed

### Crash Recovery
- Expired locks automatically cleaned up
- Database records provide audit trail
- Redis locks naturally expire

## Performance Considerations

### Scalability
- Supports hundreds of concurrent flows
- Minimal database overhead
- Fast Redis operations

### Resource Usage
- One database record per active flow
- Redis memory usage proportional to active flows
- Automatic cleanup prevents accumulation

## Migration and Deployment

### Database Migration
```bash
# Apply the migration (in Docker)
docker-compose exec backend python manage.py migrate flows
```

### Backward Compatibility
- Existing flows continue to work
- Enhanced features activate automatically
- No breaking changes

## Monitoring and Troubleshooting

### Health Checks
1. Monitor active lock count
2. Check for stale locks
3. Verify completion callbacks

### Common Issues
1. **Stale Locks**: Use `clear_flow_locks` command
2. **High Lock Count**: Check for stuck flows
3. **Lock Acquisition Failures**: Verify Redis connectivity

### Debugging
```python
from flows.utils import get_active_flow_locks
locks = get_active_flow_locks()
print(locks)  # Detailed lock information
```

## Testing

Comprehensive test suite covers:
- Basic lock acquisition/release
- Concurrent execution prevention
- Flow completion callbacks
- Crash recovery scenarios
- Integration with flow execution

Run tests:
```bash
docker-compose exec backend python manage.py test flows.tests.test_flow_execution_locking
```

## Future Enhancements

1. **Distributed Locking**: Support for multi-instance deployments
2. **Lock Metrics**: Detailed performance monitoring
3. **Priority Queuing**: Handle high-priority flows
4. **Lock Escalation**: Automatic timeout extension for long flows

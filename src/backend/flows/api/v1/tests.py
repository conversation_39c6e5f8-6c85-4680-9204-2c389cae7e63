from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, force_authenticate, APIRequestFactory

from faker import Faker

from flows.api.v1.views import FlowDescriptionView, FlowListView
from api.test import test_not_allowed_methods
from core.models import Organization, Project
from flows.models import Flow, Node, NodeRelation
from users.models import SymmyUser

from symmy.test_utils.model_mixins import SimpleFlowData

fake = Faker()


class TestFlowAPI(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.factory = APIRequestFactory()
        cls.admin = SymmyUser.objects.create_superuser(
            email=fake.ascii_email(), password=fake.password()
        )
        cls.flow_list_url = reverse("api:v1:flows:flow-list")

    def test_flow_description(self):
        test_user = SymmyUser.objects.create_user(
            email="<EMAIL>", first_name="user", password="PASSWORD"
        )
        test_org = Organization.objects.create(name="Test Org", email="<EMAIL>")
        test_project = Project.objects.create(
            name="Test Project", organization=test_org, user=test_user
        )
        flow = Flow.objects.create(project=test_project)

        n1 = Node.objects.create(flow=flow)
        n2 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)
        n4 = Node.objects.create(flow=flow)
        n5 = Node.objects.create(flow=flow)
        n6 = Node.objects.create(flow=flow)

        n1.append(n2)
        n3.append(n4)
        n2.append(n3)
        n2.append(n5)
        # This test uses the following schema:
        # n1 -> n2 -> n3 -> n4
        #       n2 -> n5
        # n6
        request = self.factory.get(
            reverse("api:v1:flows:flow-description", kwargs={"uuid": flow.uuid})
        )
        force_authenticate(request, user=self.admin)
        response = FlowDescriptionView.as_view()(request, uuid=flow.uuid)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("nodes", response.data)
        self.assertIn("relations", response.data)
        self.assertEqual(
            len(response.data["nodes"]), Node.objects.filter(flow=flow).count()
        )
        self.assertEqual(
            len(response.data["relations"]),
            NodeRelation.objects.filter(descendant__flow=flow, depth=1).count(),
        )

        # test_not_allowed_methods(self, self.client, url, allowed_methods={"get"})

    def test_flow_list(self):
        SimpleFlowData()
        SimpleFlowData()
        request = self.factory.get(self.flow_list_url, {})
        force_authenticate(request, user=self.admin)
        response = FlowListView.as_view()(request)
        self.assertEqual(len(response.data), 2)
        for flow in response.data:
            for k, v in flow.items():
                self.assertIn(k, ("uuid", "name", "enabled"))
                if k == "uuid":
                    self.assertTrue(Flow.objects.get(uuid=v) is not None)
                self.assertTrue(v is not None)

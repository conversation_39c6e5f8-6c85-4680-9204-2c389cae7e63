from collections import OrderedDict

from rest_framework import serializers
from drf_spectacular.utils import extend_schema_serializer, OpenApiExample

from flows.models import Node, NodeRelation, Flow

from nodetypes.models import NodeTypeResource, ActionResource


class FlowUpdateSerializer(serializers.ModelSerializer):

    class Meta:
        model = Flow
        fields = ["name", "enabled"]


class NodeSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    node_type_resource = serializers.SerializerMethodField()
    action_resource = serializers.SerializerMethodField()
    connection = serializers.SerializerMethodField()

    class Meta:
        model = Node
        fields = [
            "uuid",
            "name",
            "node_type_resource",
            "action_resource",
            "connection",
        ]

    def get_name(self, instance):
        return getattr(instance.node_type_resource, "name", None)

    def get_node_type_resource(self, instance):
        return getattr(instance.node_type_resource, "uuid", None)

    def get_action_resource(self, instance):
        return getattr(instance.action_resource, "uuid", None)

    def get_connection(self, instance):
        return getattr(instance.connection, "uuid", None)


class NodeRelationSerializer(serializers.ModelSerializer):
    ancestor = serializers.UUIDField(source="ancestor.uuid")
    descendant = serializers.UUIDField(source="descendant.uuid")

    class Meta:
        model = NodeRelation
        fields = ["ancestor", "descendant"]


@extend_schema_serializer(
    examples=[
        OpenApiExample(
            "Flow Description Example 1",
            value={
                "nodes": [
                    {"uuid": "7e655ed6-834d-47ae-af74-03ced0c0ba78"},
                    {"uuid": "811896b1-f682-444e-9851-71974e2ce069"},
                    {"uuid": "55c6865e-1a69-4d1b-8f86-302cb2ab7a92"},
                ],
                "relations": [
                    {
                        "ancestor": "7e655ed6-834d-47ae-af74-03ced0c0ba78",
                        "descendant": "811896b1-f682-444e-9851-71974e2ce069",
                    },
                    {
                        "ancestor": "811896b1-f682-444e-9851-71974e2ce069",
                        "descendant": "55c6865e-1a69-4d1b-8f86-302cb2ab7a92",
                    },
                ],
            },
        )
    ]
)
class FlowDescriptionSerializer(serializers.Serializer):
    nodes = serializers.JSONField()
    relations = serializers.JSONField()

    def to_representation(self, instance):
        if not isinstance(instance, Flow):
            return super().to_representation(instance)

        data = OrderedDict()

        nodes = instance.nodes.all()
        relations = NodeRelation.objects.filter(
            descendant__flow=instance, depth=1
        ).prefetch_related("ancestor", "descendant")

        data["nodes"] = NodeSerializer(nodes, many=True).data
        data["relations"] = NodeRelationSerializer(relations, many=True).data

        return data


class FlowBasicSerializer(serializers.ModelSerializer):
    class Meta:
        model = Flow
        fields = ["uuid", "name", "enabled"]


class NodeTypeResourceSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="display_name")

    class Meta:
        model = NodeTypeResource
        fields = ["uuid", "name"]


class ActionResourceSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source="display_name")
    node_type__uuid = serializers.UUIDField(source="node_type.uuid")

    class Meta:
        model = ActionResource
        fields = ["uuid", "name", "node_type__uuid"]

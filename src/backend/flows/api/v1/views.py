import copy

from django.shortcuts import get_object_or_404

from rest_framework import status
from rest_framework.generics import (
    GenericAPIView,
    DestroyAPIView,
    ListAPIView,
    RetrieveAPIView,
    UpdateAPIView,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import (
    extend_schema_view,
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
)

# from connections.models import Connection

from flows.api.v1.serializers import (
    FlowUpdateSerializer,
    FlowDescriptionSerializer,
    FlowBasicSerializer,
    NodeTypeResourceSerializer,
    ActionResourceSerializer,
)
from flows.mixins import FilterByUserFlowsMixin, FilterByUserNodesMixin
from flows.models import Node

from nodetypes.models import NodeTypeResource, ActionResource


@extend_schema_view(
    patch=extend_schema(
        operation_id="Update Flow data",
        description="Updates Flow data",
        responses={
            200: FlowUpdateSerializer,
            400: OpenApiResponse(description="Invalid data"),
            404: OpenApiResponse(description="Flow not found"),
        },
    )
)
class FlowUpdateView(FilterByUserFlowsMixin, UpdateAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"
    serializer_class = FlowUpdateSerializer


@extend_schema_view(
    delete=extend_schema(
        operation_id="Delete Flow",
        description="Deletes a Flow.",
        responses={
            204: OpenApiResponse(description="Flow deleted successfully"),
            404: OpenApiResponse(description="Flow not found"),
        },
    )
)
class FlowDeleteView(FilterByUserFlowsMixin, DestroyAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"


@extend_schema_view(
    delete=extend_schema(
        operation_id="Execute Flow",
        description="Executes a flow.",
        responses={
            200: OpenApiResponse(description="Flow executed successfully"),
            404: OpenApiResponse(description="Flow not found"),
            500: OpenApiResponse(description="Flow execution error"),
        },
    )
)
class FlowExecuteView(FilterByUserFlowsMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, uuid):
        queryset = self.get_queryset()
        flow = get_object_or_404(queryset, uuid=uuid)

        try:
            flow.execute()
        except Exception as e:
            return Response(
                {"error_text": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response(status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get Flow description",
        summary="Gets all Nodes in the particular Flow and all depth-1 NodeRelations.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                location=OpenApiParameter.PATH,
                description="Flow Identifier",
            )
        ],
        responses={
            200: FlowDescriptionSerializer,
            404: OpenApiResponse(description="Flow not found"),
        },
    )
)
class FlowDescriptionView(FilterByUserFlowsMixin, RetrieveAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = FlowDescriptionSerializer


@extend_schema_view(
    post=extend_schema(
        operation_id="Create Flow root Node",
        summary="Creates a root Node within a Flow",
        responses={
            201: OpenApiResponse(description="Node created successfully"),
            404: OpenApiResponse(description="Flow not found"),
        },
    )
)
class FlowCreateRootNodeView(FilterByUserFlowsMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, uuid):
        queryset = self.get_queryset()
        flow = get_object_or_404(queryset, uuid=uuid)

        Node.objects.create(flow=flow)

        return Response(status=status.HTTP_201_CREATED)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get basic information for Flows",
        summary="Returns Flow uuid and name.",
        description="If flow_uuids parameter is specified, returns only the Flows with specified uuids."
        " If project_uuids parameter is specified, returns only the Flows related to these projects."
        " If organization_uuids parameter is specified, returns only the Flows related to these organizations",
        parameters=[
            OpenApiParameter(
                name="flow_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Flow model uuids",
            ),
            OpenApiParameter(
                name="project_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Project model uuids",
            ),
            OpenApiParameter(
                name="organization_uuids",
                type=OpenApiTypes.UUID,
                many=True,
                location=OpenApiParameter.QUERY,
                description="Organization model uuids",
            ),
        ],
        responses={
            200: FlowBasicSerializer,
            404: OpenApiResponse(description="Flow not found"),
        },
    )
)
class FlowListView(FilterByUserFlowsMixin, ListAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    serializer_class = FlowBasicSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        flow_uuids = request.query_params.getlist("flow_uuids")
        if flow_uuids:
            queryset = queryset.filter(uuid__in=flow_uuids)

        project_uuids = request.query_params.getlist("project_uuids")
        if project_uuids and not flow_uuids:
            queryset = queryset.filter(project__uuid__in=project_uuids)

        organization_uuids = request.query_params.getlist("organization_uuids")
        if organization_uuids and not project_uuids and not flow_uuids:
            queryset = queryset.filter(
                project__organization__uuid__in=organization_uuids
            )

        serializer = FlowBasicSerializer(queryset, many=True)
        return Response(serializer.data)


@extend_schema_view(
    post=extend_schema(
        operation_id="Create nested Node",
        summary="Creates a new Node nested within another Node",
        responses={
            201: OpenApiResponse(description="Node created successfully"),
            404: OpenApiResponse(description="Node not found"),
        },
    )
)
class NodeCreateNestedNodeView(FilterByUserNodesMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, uuid):
        queryset = self.get_queryset()
        parent_node = get_object_or_404(queryset, uuid=uuid)

        child_node = Node.objects.create(flow=parent_node.flow)
        parent_node.append(child_node)

        return Response(status=status.HTTP_201_CREATED)


@extend_schema_view(
    post=extend_schema(
        operation_id="Delete Node",
        summary="Deletes a Node.",
        responses={
            204: OpenApiResponse(description="Node deleted successfully"),
            404: OpenApiResponse(description="Node not found"),
        },
    )
)
class NodeDeleteView(FilterByUserNodesMixin, DestroyAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"

    def perform_destroy(self, instance):
        parent = instance.parent
        children = copy.copy(instance.children)
        instance.disinherit()
        if parent is not None:
            instance.parent.remove(instance)
            for child in children:
                parent.append(child)
        instance.delete()


@extend_schema_view(
    post=extend_schema(
        operation_id="Connect Flow Nodes",
        summary="Connects two Nodes within a Flow",
        responses={
            200: OpenApiResponse(description="Nodes connected successfully"),
            404: OpenApiResponse(description="Node not found"),
        },
    )
)
class NodeConnectView(FilterByUserNodesMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, source_node_uuid, target_node_uuid):
        queryset = self.get_queryset()

        source_node = get_object_or_404(queryset, uuid=source_node_uuid)
        target_node = get_object_or_404(
            queryset, flow=source_node.flow, uuid=target_node_uuid
        )

        source_node.append(target_node)

        return Response(status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        operation_id="Disconnect Flow Nodes",
        summary="Disconnects two Nodes within a Flow",
        responses={
            200: OpenApiResponse(description="Nodes disconnected successfully"),
            404: OpenApiResponse(description="Node not found"),
        },
    )
)
class NodeDisconnectView(FilterByUserNodesMixin, GenericAPIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, source_node_uuid, target_node_uuid):
        queryset = self.get_queryset()

        source_node = get_object_or_404(queryset, uuid=source_node_uuid)
        target_node = get_object_or_404(
            queryset, flow=source_node.flow, uuid=target_node_uuid
        )

        if target_node in source_node.children:
            Node.disconnect(source_node, target_node)

        return Response(status=status.HTTP_200_OK)


@extend_schema_view(
    get=extend_schema(
        operation_id="Get Flow Node resources",
        summary="Retrieves all NodeTypeResources and ActionResources",
        responses={
            200: OpenApiResponse(description="Resources retrieved successfully"),
        },
    )
)
class NodeResourcesView(GenericAPIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        node_types = NodeTypeResource.objects.all()
        actions = ActionResource.objects.select_related("node_type").all()

        node_type_serializer = NodeTypeResourceSerializer(node_types, many=True)
        action_serializer = ActionResourceSerializer(actions, many=True)

        return Response(
            {
                "node_type_resources": node_type_serializer.data,
                "action_resources": action_serializer.data,
            }
        )


@extend_schema_view(
    post=extend_schema(
        operation_id="Set Node type",
        summary="Sets the NodeTypeResource for a Node",
        responses={
            200: OpenApiResponse(description="Node type set successfully"),
            404: OpenApiResponse(description="Node or NodeTypeResource not found"),
        },
    )
)
class NodeSetTypeView(FilterByUserNodesMixin, UpdateAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"

    def update(self, request, node_type_uuid, *args, **kwargs):
        instance = self.get_object()
        node_type = get_object_or_404(NodeTypeResource, uuid=node_type_uuid)

        instance.node_type_resource = node_type
        instance.action_resource = None
        instance.connection = None
        instance.save()

        return Response(status=status.HTTP_200_OK)


@extend_schema_view(
    post=extend_schema(
        operation_id="Set Node action",
        summary="Sets the ActionResource for a Node",
        responses={
            200: OpenApiResponse(description="Node action set successfully"),
            404: OpenApiResponse(description="Node or ActionResource not found"),
        },
    )
)
class NodeSetActionView(FilterByUserNodesMixin, UpdateAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"

    def update(self, request, action_uuid, *args, **kwargs):
        instance = self.get_object()
        action = get_object_or_404(
            ActionResource,
            node_type=instance.node_type_resource,
            uuid=action_uuid,
        )

        instance.action_resource = action
        instance.save()

        return Response(status=status.HTTP_200_OK)


# @extend_schema_view(
#     post=extend_schema(
#         operation_id="Set Node connection",
#         summary="Sets the Connection for a Node",
#         responses={
#             200: OpenApiResponse(description="Node connection set successfully"),
#             404: OpenApiResponse(description="Node or Connection not found"),
#         },
#     )
# )
# class NodeSetConnectionView(FilterByUserNodesMixin, UpdateAPIView):
#     permission_classes = [IsAuthenticated]
#     lookup_field = "uuid"

#     def update(self, request, connection_uuid, *args, **kwargs):
#         instance = self.get_object()
#         connection = get_object_or_404(
#             Connection,
#             nodetype=instance.node_type_resource,
#             uuid=connection_uuid,
#         )

#         instance.connection = connection
#         instance.save()

#         return Response(status=status.HTTP_200_OK)

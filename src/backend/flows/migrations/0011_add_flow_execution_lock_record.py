# Generated manually for flow execution lock record

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('flows', '0010_remove_flow_interval'),
    ]

    operations = [
        migrations.CreateModel(
            name='FlowExecutionLockRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created')),
                ('updated', models.DateTimeField(auto_now=True, verbose_name='Updated')),
                ('mongo_flow_id', models.CharField(help_text='MongoDB ObjectId of the flow execution log', max_length=24, verbose_name='MongoDB Flow ID')),
                ('locked_at', models.DateTimeField(default=django.utils.timezone.now, help_text='When the lock was acquired', verbose_name='Locked At')),
                ('locked_by_task_id', models.CharField(help_text='Celery task ID that acquired the lock', max_length=255, verbose_name='Locked By Task ID')),
                ('redis_lock_key', models.CharField(help_text='Redis key used for the lock', max_length=255, verbose_name='Redis Lock Key')),
                ('timeout_at', models.DateTimeField(help_text='When the lock will timeout', verbose_name='Timeout At')),
                ('flow', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='execution_lock_record', to='flows.flow', verbose_name='Flow')),
            ],
            options={
                'verbose_name': 'Flow Execution Lock Record',
                'verbose_name_plural': 'Flow Execution Lock Records',
            },
        ),
        migrations.AddIndex(
            model_name='flowexecutionlockrecord',
            index=models.Index(fields=['locked_at'], name='flows_flowe_locked__b8e8a5_idx'),
        ),
        migrations.AddIndex(
            model_name='flowexecutionlockrecord',
            index=models.Index(fields=['timeout_at'], name='flows_flowe_timeout_a9c7b2_idx'),
        ),
    ]

# Generated by Django 4.2.2 on 2025-05-26 15:28

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_alter_project_options'),
        ('flows', '0011_add_flow_execution_lock_record'),
    ]

    operations = [
        migrations.RenameIndex(
            model_name='flowexecutionlockrecord',
            new_name='flows_flowe_locked__7019ee_idx',
            old_name='flows_flowe_locked__b8e8a5_idx',
        ),
        migrations.RenameIndex(
            model_name='flowexecutionlockrecord',
            new_name='flows_flowe_timeout_be32d9_idx',
            old_name='flows_flowe_timeout_a9c7b2_idx',
        ),
        migrations.RemoveField(
            model_name='flowexecutionlockrecord',
            name='created',
        ),
        migrations.RemoveField(
            model_name='flowexecutionlockrecord',
            name='updated',
        ),
        migrations.AddField(
            model_name='flowexecutionlockrecord',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now, verbose_name='Created At'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='flowexecutionlockrecord',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Updated At'),
        ),
        migrations.AlterField(
            model_name='flow',
            name='name',
            field=models.CharField(max_length=255, verbose_name='Name'),
        ),
        migrations.AlterField(
            model_name='flow',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='flows', to='core.project', verbose_name='Project'),
        ),
        migrations.AlterField(
            model_name='flowexecutionlockrecord',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
    ]

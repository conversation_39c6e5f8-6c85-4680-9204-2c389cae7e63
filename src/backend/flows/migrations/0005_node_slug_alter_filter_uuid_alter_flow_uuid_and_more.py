# Generated by Django 4.2.2 on 2024-03-26 09:38

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('flows', '0004_node_connection'),
    ]

    operations = [
        migrations.AddField(
            model_name='node',
            name='slug',
            field=models.SlugField(max_length=255, null=True, unique=True, verbose_name='Input'),
        ),
        migrations.AlterField(
            model_name='filter',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AlterField(
            model_name='flow',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.AlterField(
            model_name='node',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='noderelation',
            name='uuid',
            field=models.UUI<PERSON>ield(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
    ]

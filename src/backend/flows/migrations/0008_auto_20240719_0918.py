# Generated by Django 4.2.2 on 2024-07-19 09:18

import json

from django.db import migrations
from django_celery_beat.models import CrontabSchedule, PeriodicTask

from flows.models import Flow
from flows.utils import get_flow_periodic_task_defaults


def delete_all_flow_periodic_tasks(apps, schema_editor) -> None:
    uuid = r"[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"
    tasks = PeriodicTask.objects.filter(name__iregex=uuid)
    tasks.delete()


def create_default_periodic_tasks(apps, schema_editor) -> None:
    crontab, _ = CrontabSchedule.objects.get_or_create(
        minute="0",
        hour="*",
        day_of_week="*",
        day_of_month="*",
        month_of_year="*",
    )

    flows = list(Flow.objects.all())

    for flow in flows:
        defaults = get_flow_periodic_task_defaults(flow)
        defaults.update(
            {
                "crontab": crontab,
                "enabled": False,
                "args": json.dumps([flow.pk]),
            }
        )

        flow.periodic_task = PeriodicTask.objects.create(**defaults)

    Flow.objects.bulk_update(flows, ["periodic_task"])


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        ("flows", "0007_flow_periodic_task_alter_flow_enabled"),
    ]

    operations = [
        migrations.RunPython(delete_all_flow_periodic_tasks, migrations.RunPython.noop),
        migrations.RunPython(
            create_default_periodic_tasks, migrations.RunPython.noop, atomic=True
        ),
    ]

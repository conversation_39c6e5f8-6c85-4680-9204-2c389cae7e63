# Generated by Django 4.2.2 on 2024-07-19 09:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('django_celery_beat', '0018_improve_crontab_helptext'),
        ('flows', '0006_alter_node_slug'),
    ]

    operations = [
        migrations.AddField(
            model_name='flow',
            name='periodic_task',
            field=models.OneToOneField(default=None, editable=False, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='flow', to='django_celery_beat.periodictask', verbose_name='Periodic Task'),
        ),
        migrations.AlterField(
            model_name='flow',
            name='enabled',
            field=models.BooleanField(default=False, verbose_name='Enabled'),
        ),
    ]

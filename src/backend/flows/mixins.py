from django.conf import settings

from flows.models import Flow, Node

from symmy.mixins import FilterByUserMixin


class FilterByUserFlowsMixin(FilterByUserMixin):
    model = Flow

    @staticmethod
    def filter_queryset_regular_user(queryset, user):
        return queryset.filter(project__organization__organizationuser__user=user)

    @staticmethod
    def filter_queryset_superuser(queryset):
        return queryset.exclude(
            project__organization__name=settings.DEFAULT_ORGANIZATION_NAME
        ).exclude(project__name=settings.DEFAULT_PROJECT_NAME)


class FilterByUserNodesMixin(FilterByUserMixin):
    model = Node

    @staticmethod
    def filter_queryset_regular_user(queryset, user):
        return queryset.filter(flow__project__organization__organizationuser__user=user)

    @staticmethod
    def filter_queryset_superuser(queryset):
        return queryset.exclude(
            flow__project__organization__name=settings.DEFAULT_ORGANIZATION_NAME
        ).exclude(flow__project__name=settings.DEFAULT_PROJECT_NAME)
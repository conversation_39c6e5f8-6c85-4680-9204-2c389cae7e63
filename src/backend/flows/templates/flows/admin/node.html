<div class="entry">
    <span style="{% if node == original %}background-color: #fffb8b;{% endif %} display: flex;">
        <div style="display: flex; flex-wrap: wrap; align-content: center; flex: 1; overflow: hidden;">
            {% if node == original %}
              <a href="{% url 'admin:flows_node_change' node.pk %}" style="color:blue;" >{{ node.str_for_template }}</a>
            {% else %}
              <a href="{% url 'admin:flows_node_change' node.pk %}">{{ node.str_for_template }}</a>
            {% endif %}
        </div>
    
        <div style="display: flex; flex-direction: column; justify-content: center; margin: 5px;">
            <a hx-get="{% url 'flows:create-and-append-node' node.uuid %}"
                hx-trigger="click"
                hx-target="#tree"
            >
                <i class="fa fa-plus-square" aria-hidden="true" title="Create and append a new node"></i>
            </a>

            {% if not node.is_root %}
                {% if node == original %}
                    <a href="{% url 'flows:delete-node' node.uuid %}">
                        <i class="fa fa-trash" aria-hidden="true" title="Delete node"></i>
                    </a>
                {% else %}
                    <a hx-get="{% url 'flows:delete-node' node.uuid %}"
                        hx-trigger="click"
                        hx-target="#tree">
                        <i class="fa fa-trash" aria-hidden="true" title="Delete node"></i>
                    </a>
                {% endif %}
            {% endif %}

        </div>
    </span>
    {% if node.children %}
        <div class="branch">
            {% for child in node.children %}
                {% with node=child template_name="flows/admin/node.html" %}
                    {% include template_name %}
                {% endwith %}
            {% endfor %}
        </div>
    {% endif %}
</div>
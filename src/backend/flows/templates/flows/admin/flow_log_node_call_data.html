{% if error %}
    {{ error }}
{% else %}
    <button type="button"
        style="align-self: flex-end;"
        class="collapse-button"
        onclick="toggleCollapse(this.nextElementSibling)">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-chevron-down" viewBox="0 0 18 18">
            <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
        </svg>
    </button>
    <div class="block">
        {{ data }}
    </div>
{% endif %}

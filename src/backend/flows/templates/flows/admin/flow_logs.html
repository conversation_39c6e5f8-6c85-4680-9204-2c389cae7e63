{% load i18n %}
{% if error %}
    <li style="background-color: #ff000030;">
        {% trans "Error msg: " %}
        {{ error }}
    </li>
{% else %}
    {% if logs %}
        <div class="odd"
            hx-target="#load_logs"
            hx-get="{% url 'flows:flow-logs-view' %}?flow_id={{ flow_id }}&date_to={{ date_to }}"
            hx-trigger="intersect once"
            hx-swap="outerHTML">
        </div>
    {% endif %}

    {% for log in logs %}
    <li style="background-color: rgb({{ log_color|get_item:log.status.code  }} / {% cycle '5%' '10%' %});">
            <div class="expanded"
                 style="display: flex;
                    flex-direction: column;">
                <button type="button"
                    style="align-self: flex-end;"
                    class="collapse-button"
                    onclick="toggleCollapse(this.parentNode)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-chevron-down" viewBox="0 0 18 18">
                        <path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708"/>
                    </svg>
                </button>
                <table>
                    <tr>
                      <th>{% trans "ID" %}</th>
                      <th>{% trans "UUID" %}</th>
                      <th>{% trans "Name" %}</th>
                      <th>{% trans "Start" %}</th>
                      <th>{% trans "Status" %}</th>
                      <th>{% trans "Executed Node Count" %}</th>
                      <th>{% trans "Total Node Count" %}</th>
                    </tr>
                    <tr>
                      <td>{{ log|get_item:"_id"  }}</td>
                      <td>{{ log.uuid }}</td>
                      <td>{{ log.name }}</td>
                      <td>{{ log.start }}</td>
                      <td>{{ log.status.name }}</td>
                      <td>{{ log.executed_node_count }}</td>
                      <td>{{ log.total_node_count }}</td>
                    </tr>
                    <tr>
                        <table>
                            <tr>
                                <th style="text-align: center;" colspan="7">{% trans "Node Calls" %}</th>
                            </tr>
                            <tr id="node-call-{{ log|get_item:"_id"  }}">
                                <td style="text-align: center;" colspan="7">
                                    <button type="button"
                                        hx-target="#node-call-{{ log|get_item:"_id"  }}"
                                        hx-get="{% url 'flows:flow-log-node-calls' %}?log_id={{ log|get_item:"_id" }}&status={{ log.status.code }}"
                                        hx-trigger="click"
                                        hx-swap="outerHTML"
                                        style="align-self: flex-end;"
                                        class="collapse-button">
                                        ...
                                    </button>
                                </td>
                            </tr>
                        </table>
                    </tr>
                  </table>
        </div>
    </li>
    {% empty %}
    <li class="odd">
        <b>{% trans "No logs yet" %}</b>
    </li>
    {% endfor %}

    {% if logs %}
    <li id="load_logs" style="display: flex;justify-content: center;">
        <div class="lds-spinner"
            hx-get="/debug/flows/logs/?flow_id={{ flow_id }}&date_to={{ date_to }}"
            hx-trigger="click"
            hx-swap="outerHTML">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </li>
    {% endif %}
{% endif %}
{% extends "admin/change_form.html" %}
{% load static %}
{% load i18n admin_urls %}
{% block extrastyle %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="{% static 'css/tree.css' %}">
<script src="{% static 'js/htmx.min.1.9.10.js' %}"></script>
{% endblock %}

{% block content %}
  {% if original.pk %}
  <div style="display: grid">
    <div style="display: inline; font-weight: 700!important; font-size: 2rem!important;">
      Flow:
      <a href="{% url 'admin:flows_flow_change' original.flow.pk %}">{{ original.flow }}</a>
    </div>
    <div style="display: inline; font-weight: 700!important; font-size: 2rem!important;">
      Node:
      <a href="{% url 'admin:flows_node_change' original.pk %}">{{ original }}</a>
    </div>
  </div>
  {% endif %}
  <div id="scrollable-container" 
        style=" 
          /* max-height: 400px; */
          /* overflow: auto; */
          margin: 20px 0px 20px 0px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
          border-radius: 0.28571rem;
          padding: 10px;">
    <div id="tree">
      {% include "flows/admin/root_node.html" with node=original.root flow=original.flow  original=original%}
    </div>
  </div>
  <!-- Display other fields and data here as needed -->
  {{ block.super }}
{% endblock %}

{% extends "admin/change_form.html" %}
{% load static %}
{% load i18n admin_urls %}
{% block extrastyle %}
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <link rel="stylesheet" href="{% static 'css/tree.css' %}">
  <link rel="stylesheet" href="{% static 'css/spiner.css' %}">
  <script src="{% static 'js/htmx.min.1.9.10.js' %}"></script>
  <script>
    function toggleCollapse(block) {
      block.classList.toggle('block');
      block.classList.toggle('expanded');
    }
  </script>
  <style>
      #log {
          padding: 30px;
          max-height: 700px;
          overflow-y: scroll;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      }

      #log ul {
          margin: 0;
          padding: 0;
      }

      #log li {
          list-style-type: none;
          margin: 30px 0;
          padding: 10px;
          border-radius: 5px;
          word-break: break-all;
      }

      #log li.odd {
          background-color: rgba(0, 0, 0, 0.1);
      }

      #log li.even {
          background-color: rgba(0, 0, 0, 0.15);
      }

      .collapse-button {
        display: inline-block;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        text-decoration: none;
        background-color: #ffffff00;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s ease;
      }
      
      .collapse-button:hover {
          background-color: #5b82a938;
      }
      
      .collapse-button:active {
          background-color: #5b82a938;
      }

      .block {
        max-height: 160px !important;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .expanded {
          height: auto;
      }
  </style>
{% endblock %}

{% block object-tools-items %}
  {{ block.super }}
  {% include "admin_extra_buttons/includes/change_form_buttons.html" %}
{% endblock %}

{% block content %}
  {% if original.pk %}
    <div style="display: inline; font-weight: 700!important; font-size: 2rem!important;">
      Flow:
      <a href="{% url 'admin:flows_flow_change' original.pk %}">{{ original }}</a>
    </div>
    <div id="scrollable-container" 
        style=" 
          /* max-height: 400px; */
          /* overflow: auto; */
          margin: 20px 0px 20px 0px;
          box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
          border-radius: 0.28571rem;
          padding: 10px;">
      <div id="tree">
        {% for root in original.roots.all %}
        {% include "flows/admin/root_node.html" with node=root flow=original original=original%}
        {% endfor %}
        {% if not original.roots.all%}
          <a hx-get="{% url 'flows:create-node' original.uuid %}"
              hx-trigger="click"
              hx-target="#tree"
              >
              <div style="
                color: #ffffff;
                background-color: #47bac1;
                width: fit-content;
                border-radius: 0.28571rem;
                padding: 7px;
                cursor: pointer;
                transition: background-color 0.3s ease;
              "
              onmouseover="this.style.backgroundColor='#2e8b8e'"
              onmouseout="this.style.backgroundColor='#47bac1'">
              {% trans "Create Root Node" %}
              </div>
          </a>
        {% endif %}
      </div>
      </div>
    {% endif %}
    <!-- Display other fields and data here as needed -->
  {{ block.super }}
{% endblock %}

{% block after_related_objects %}
  {{ block.super }}
  <div id="log">
    <ul>
      {% include "flows/admin/flow_logs.html" with flow_id=original.pk date_to=date_to  logs=logs %}
    </ul>
  </div>
{% endblock %}

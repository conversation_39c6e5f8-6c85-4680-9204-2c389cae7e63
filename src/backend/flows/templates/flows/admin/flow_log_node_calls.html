{% load i18n %}
{% if error %}
    {{ error }}
{% else %}
    <tr>
        <th>{% trans "UUID" %}</th>
        <th>{% trans "Start" %}</th>
        <th>{% trans "Status" %}</th>
        <th>{% trans "Request Count" %}</th>
        <th>{% trans "Errors" %}</th>
        <th>{% trans "Retries" %}</th>

    </tr>
    {% for item in node_calls %}
        <tr style="background-color: rgb({{ log_color|get_item:item.status.code  }} / 7%);">
            <td>{{ item.uuid }}</td>
            <td>{{ item.start }}</td>
            <td>{{ item.status.name }}</td>
            <td>{{ item.request_count }}</td>
            <td>
                <div style="max-width: 400px;max-height: 300px;overflow: auto;" >
                    {{ item.errors }}
                </div>
            </td>
            <td>{{ item.retries }}</td>
        </tr>
            <tr style="background-color: rgb({{ log_color|get_item:item.status.code  }} / 3%);">
                <td style="text-align: center;" colspan="7">
                    <div class="expanded">
                        <div style="border-bottom: 0.07143rem solid #f4f4f4;padding-bottom: 5px;">
                            <b>{% trans "Input Data" %}:</b>
                            {% if item.input_data %}
                                <button type="button"
                                    hx-get="{% url 'flows:flow-log-node-call-data' %}?ids={{ item.input_data }}&status={{ item.status.code }}"
                                    hx-trigger="click"
                                    hx-swap="outerHTML"
                                    style="align-self: flex-end;"
                                    class="collapse-button">
                                    ...
                                </button>
                            {% else %}
                                {{ item.input_data }}
                            {% endif %}
                        </div>
                        <div>
                            <b>{% trans "Output Data" %}:</b>
                            {% if item.output_data %}
                                <button type="button"
                                    hx-get="{% url 'flows:flow-log-node-call-data' %}?ids={{ item.output_data }}&status={{ item.status.code }}"
                                    hx-trigger="click"
                                    hx-swap="outerHTML"
                                    style="align-self: flex-end;"
                                    class="collapse-button">
                                    ...
                                </button>
                            {% else %}
                                {{ item.output_data }}
                            {% endif %}
                        </div>
                    </div>
                </td>
            </tr>
    {% empty %}
        <tr>
            <td style="text-align: center;" colspan="7">{% trans "No logs yet" %}</td>
        </tr>
    {% endfor %}
{% endif %}

# Flow Execution Locking Implementation

## Overview

The Flow Execution Locking system provides mutual exclusion for flow execution, ensuring that only one instance of a flow can run at a time. This system includes enhanced admin interfaces, automatic monitoring, and comprehensive safety features to prevent production data loss.

## Key Features

### 🔒 **Enhanced Flow Locking**
- **Atomic Lock Acquisition**: Race condition-free lock acquisition using Redis + database persistence
- **Dual Persistence**: Redis for performance + PostgreSQL for reliability and admin visibility
- **Flow Completion Callbacks**: Automatic lock release when flows complete
- **Timezone-Safe Implementation**: Proper handling of MongoDB's naive datetimes

### 🛠️ **Admin Interface Enhancements**
- **Deletable Locks**: Locks can be deleted for debugging purposes
- **Redis Cleanup**: Deleting locks also clears corresponding Redis locks
- **Visual Status Indicators**: Color-coded lock status (🟢 active, 🔴 expired)
- **Comprehensive Filtering**: By status, organization, flow name, and date
- **Clickable Records**: Full view and edit capabilities for lock records

### 📊 **Automatic Monitoring**
- **Stuck Flow Detection**: Automatically finds flows stuck > 30 minutes
- **Timeout Status**: Sets stuck flows to `timeout` status (not `failed`)
- **Orphaned Lock Cleanup**: Removes database locks without corresponding flows
- **Periodic Tasks**: Configurable monitoring intervals

### 🌐 **Frontend Integration**
- **Timeout Status Support**: New orange timeout status in UI
- **Multilingual**: English/Czech translations for timeout status
- **Theme Integration**: Consistent color scheme across the application

## Implementation Details

### Atomic Lock Acquisition

The locking mechanism uses a two-phase approach to prevent race conditions:

```python
def acquire(self, blocking: bool = False) -> bool:
    # Phase 1: Acquire Redis lock (outside database transaction)
    self._lock = redis_client.lock(self.lock_key, timeout=self.timeout, blocking=blocking)
    acquired = self._lock.acquire(blocking=blocking)
    
    if not acquired:
        return False
    
    # Phase 2: Create database record atomically
    try:
        with transaction.atomic():
            if self._check_existing_db_lock():
                self._lock.release()
                return False
            
            if not self._create_db_record_atomic():
                self._lock.release()
                return False
            
            return True
    except IntegrityError:
        self._lock.release()
        return False
```

### Dual Persistence Architecture

**Redis Layer:**
- Primary locking mechanism for performance
- TTL-based automatic expiration
- Atomic operations for race condition prevention

**Database Layer:**
- Persistent lock records for admin visibility
- Metadata storage (flow_id, mongo_flow_id, task_id)
- Audit trail and debugging capabilities

### Flow Completion Callbacks

```python
def handle_flow_completion(mongo_flow_id: str, flow_status: str):
    """Release locks when flows complete."""
    lock_record = FlowExecutionLockRecord.objects.filter(
        mongo_flow_id=mongo_flow_id
    ).first()
    
    if lock_record:
        # Release Redis lock
        redis_client.delete(lock_record.redis_lock_key)
        # Delete database record
        lock_record.delete()
```

## Status System Enhancement

### New Timeout Status

Added `timeout = (6, "Timeout")` to the Status enum:

**Backend (Python):**
```python
class Status(Enum):
    success = (0, "Success")
    failed = (1, "Failed")
    retrying = (2, "Retrying")
    in_progress = (3, "In progress")
    in_queue = (4, "In queue")
    skipped = (5, "Skipped")
    timeout = (6, "Timeout")  # NEW
```

**Frontend (TypeScript):**
```typescript
export enum CodeByStatusName {
    success = 0,
    failed = 1,
    retrying = 2,
    in_progress = 3,
    in_queue = 4,
    skipped = 5,
    timeout = 6,  // NEW
}
```

**Visual Representation:**
- **Color**: Orange (#ff8c00)
- **Translations**: "Timeout" (EN), "Časový limit" (CZ)

## Configuration and Management

### Setup Command

```bash
# Set up with default intervals (10min check, 30min cleanup)
docker-compose exec web python manage.py setup_flow_monitoring

# Custom intervals
docker-compose exec web python manage.py setup_flow_monitoring \
    --check-interval 5 \
    --cleanup-interval 15

# List existing tasks
docker-compose exec web python manage.py setup_flow_monitoring --list-only

# Remove existing tasks
docker-compose exec web python manage.py setup_flow_monitoring --remove
```

### Periodic Tasks Configuration

**Stuck Flow Check Task:**
- **Task**: `flows.tasks.check_stuck_flows`
- **Default Interval**: 10 minutes
- **Function**: Detects flows stuck > 30 minutes, sets to timeout status

**Lock Cleanup Task:**
- **Task**: `flows.tasks.clear_expired_flow_locks`
- **Default Interval**: 30 minutes
- **Function**: Backup cleanup for expired locks

### Admin Interface Features

**Access:** Django Admin → Flows → Flow Execution Lock Records

**Capabilities:**
- **View**: All active and expired locks
- **Filter**: By status (active/expired), organization, date
- **Search**: By flow name, task ID, MongoDB flow ID
- **Delete**: Individual or bulk deletion with Redis cleanup
- **Actions**: Clear expired locks, force clear selected locks

**Display Fields:**
- Flow name (clickable)
- Lock status with color indicators
- Time remaining/expired duration
- MongoDB flow ID and task ID
- Lock creation and timeout times

## Safety and Production Guidelines

### ⚠️ CRITICAL SAFETY FIXES APPLIED

The following dangerous operations were removed from tests to prevent production data loss:

**NEVER USE IN TESTS:**
```python
# CATASTROPHIC - Drops entire flows collection!
clear_flows_collection()

# CATASTROPHIC - Clears all GridFS data!
clear_gridfs()

# DANGEROUS - Deletes all flow locks!
FlowExecutionLockRecord.objects.all().delete()

# DANGEROUS - Clears all Redis locks!
redis_keys = redis_client.keys("flow_execution_lock_*")
redis_client.delete(*redis_keys)
```

**SAFE ALTERNATIVES:**
```python
# SAFE - Only delete specific test flow locks
FlowExecutionLockRecord.objects.filter(flow_id=test_flow_id).delete()

# SAFE - Only delete specific Redis lock
redis_client.delete(f"flow_execution_lock_{test_flow_id}")
```

### Test Isolation Best Practices

```python
class SafeFlowTestCase(TestCase):
    def setUp(self):
        self.test_flow = Flow.objects.create(name="test_flow")
        
    def tearDown(self):
        # SAFE - Only clean up this test's data
        FlowExecutionLockRecord.objects.filter(flow=self.test_flow).delete()
        redis_client.delete(f"flow_execution_lock_{self.test_flow.id}")
        
    def test_something(self):
        lock = FlowExecutionLock(
            flow_id=self.test_flow.id,
            mongo_flow_id="test_mongo_id",
            task_id="test_task_id"
        )
        self.assertTrue(lock.acquire())
        lock.release()
```

## API Reference

### FlowExecutionLock Class

```python
class FlowExecutionLock:
    def __init__(self, flow_id: int, timeout: int = 1200, 
                 mongo_flow_id: str = None, task_id: str = None):
        """
        Initialize flow execution lock.
        
        Args:
            flow_id: Django Flow model ID
            timeout: Lock timeout in seconds (default: 20 minutes)
            mongo_flow_id: MongoDB flow document ID
            task_id: Celery task ID
        """
    
    def acquire(self, blocking: bool = False) -> bool:
        """Acquire the lock atomically."""
    
    def release(self) -> None:
        """Release the lock and clean up database record."""
    
    def __enter__(self) -> 'FlowExecutionLock':
        """Context manager entry."""
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Context manager exit."""
```

### Utility Functions

```python
def handle_flow_completion(mongo_flow_id: str, flow_status: str) -> bool:
    """Handle flow completion by releasing associated lock."""

def clear_stale_flow_locks() -> int:
    """Clear expired locks (use with caution in production)."""

def get_active_flow_locks() -> dict:
    """Get information about currently active locks."""
```

## Troubleshooting and Monitoring

### Common Issues

**Lock Acquisition Failures:**
```python
# Check for existing locks
active_locks = get_active_flow_locks()
print(f"Active locks: {active_locks}")

# Check specific flow
lock_records = FlowExecutionLockRecord.objects.filter(flow_id=flow_id)
for record in lock_records:
    print(f"Lock: {record.flow.name}, Status: {'Active' if not record.is_expired else 'Expired'}")
```

**Stuck Flows:**
- Check Django Admin → Flow Execution Lock Records
- Look for locks with long durations
- Use "Force clear selected locks" action if needed
- Check periodic task logs for automatic cleanup

**Redis vs Database Inconsistencies:**
```bash
# List active locks with status
docker-compose exec web python manage.py clear_flow_locks --list
```

### Monitoring Commands

```bash
# Check periodic task status
docker-compose exec web python manage.py shell -c "
from django_celery_beat.models import PeriodicTask
tasks = PeriodicTask.objects.filter(name__contains='Flow')
for task in tasks:
    print(f'{task.name}: {\"Enabled\" if task.enabled else \"Disabled\"}')"

# Manual stuck flow check
docker-compose exec web python manage.py shell -c "
from flows.tasks import check_stuck_flows
result = check_stuck_flows()
print(f'Result: {result}')"
```

### Performance Monitoring

**Lock Acquisition Metrics:**
- Monitor Redis lock acquisition times
- Track database lock record creation
- Watch for lock acquisition failures

**Flow Execution Health:**
- Monitor timeout status frequency
- Track stuck flow detection rates
- Review periodic task execution logs

## Recent Changes and Improvements

### Version History

**Latest (Current):**
- ✅ Fixed race conditions in lock acquisition
- ✅ Added timezone-safe datetime handling
- ✅ Implemented timeout status across frontend/backend
- ✅ Enhanced admin interface with deletion capabilities
- ✅ Added comprehensive safety guidelines
- ✅ Fixed critical production data loss issues in tests

**Previous Issues Fixed:**
- ❌ Race conditions in concurrent lock acquisition
- ❌ Timezone-naive datetime comparisons causing errors
- ❌ Tests deleting production data (CRITICAL)
- ❌ Missing timeout status in frontend
- ❌ Inadequate admin interface for debugging

### Migration Notes

**From Previous Versions:**
1. Update frontend to handle new timeout status
2. Run database migrations for lock record model
3. Set up periodic tasks using management command
4. Review and update any custom test cleanup code

**Breaking Changes:**
- Timeout flows now have `timeout` status instead of `failed`
- Lock acquisition requires `mongo_flow_id` and `task_id` parameters
- Test cleanup functions removed (safety improvement)

## Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly**: Review stuck flow rates and timeout frequency
2. **Monthly**: Analyze lock acquisition performance metrics
3. **Quarterly**: Review and update timeout thresholds if needed

### Emergency Procedures

**System-Wide Lock Clearing (DANGEROUS):**
```bash
# Only in extreme emergencies with proper authorization
docker-compose exec web python manage.py clear_flow_locks --force
```

**Manual Flow Timeout:**
```python
# In Django shell
from flows.tasks import handle_flow_completion
handle_flow_completion("mongo_flow_id", "timeout")
```

For additional support, refer to the test safety guidelines in `TEST_SAFETY_GUIDELINES.md` and ensure all team members are aware of the critical safety requirements when working with flow execution locks.

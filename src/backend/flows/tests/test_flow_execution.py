from unittest import TestCase

from django.conf import settings

from mongo.analytics import get_node_calls, get_node_calls_count, get_request_count
from mongo.types import Status

from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    wait_flow_end,
    # REMOVED DANGEROUS IMPORTS:
    # clear_flows_collection,  # DROPS ENTIRE FLOWS COLLECTION!
    # clear_gridfs,           # CLEARS ALL GRIDFS DATA!
)
from symmy.test_utils.model_mixins import (
    SimpleFlowData,
    NestedActionsFlowData,
    SimpleExceptionFlowData,
    ComplexExceptionFlowData,
    SimpleRetryFlowData,
)

RETRY_BACKOFF = 1


class FlowExecutionMixin(CheckTestsAllowedMixin):
    def execute_normal_flow(self, flow_wait_timeout=60):
        flow = self.flow_data.flow
        node_count = self.flow_data.node_count
        supposed_request_count = self.flow_data.get_supposed_request_count()

        mongo_flow_id = flow.execute()
        s = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=flow_wait_timeout)

        self.assertNotEqual(s, "Failed", "Flow failed!")

        self.assertEqual(
            get_node_calls_count(
                mongo_flow_ids=[mongo_flow_id], node_status=Status.success
            ),
            node_count,
            "Successful node call count doesn't match the expected node call count",
        )
        self.assertEqual(
            get_request_count(mongo_flow_ids=[mongo_flow_id]),
            supposed_request_count,
            "Actual request count doesn't match the expected request count",
        )
        return mongo_flow_id

    def execute_exception_flow(self, flow_wait_timeout=60):
        flow = self.flow_data.flow
        exception_node_count = self.flow_data.exception_node_count
        supposed_request_count = self.flow_data.get_supposed_request_count()

        mongo_flow_id = flow.execute(retry_backoff=RETRY_BACKOFF)
        s = wait_flow_end(mongo_flow_id=mongo_flow_id, timeout=flow_wait_timeout)
        self.assertEqual(s, "Failed", "Flow did not fail but should have!")

        self.assertEqual(
            get_node_calls_count(
                mongo_flow_ids=[mongo_flow_id], node_status=Status.failed
            ),
            exception_node_count,
            "Failed node call count doesn't match the expected failed node call count",
        )
        self.assertEqual(
            get_request_count(mongo_flow_ids=[mongo_flow_id]),
            supposed_request_count,
            "Actual request count doesn't match the expected request count",
        )

        return mongo_flow_id

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        # REMOVED DANGEROUS CALLS:
        # clear_flows_collection() - DROPS ENTIRE FLOWS COLLECTION FROM PRODUCTION!
        # clear_gridfs() - CLEARS ALL GRIDFS DATA FROM PRODUCTION!
        # These functions are extremely dangerous and should NEVER be called in tests!


class TestSimpleFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()

    def test_simple_flow(self):
        self.execute_normal_flow()


class TestNestedActionsExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = NestedActionsFlowData()

    def test_complex_flow(self):
        self.execute_normal_flow()


class TestSimpleExceptionFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleExceptionFlowData()

    def test_simple_exception_flow(self):
        self.execute_exception_flow()


class TestComplexExceptionFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = ComplexExceptionFlowData()

    def test_complex_exception_flow(self):
        self.execute_exception_flow()


class TestRetryFlowExecution(FlowExecutionMixin, TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleRetryFlowData()

    def test_simple_retry_flow(self):
        mongo_flow_id = self.execute_exception_flow(
            flow_wait_timeout=sum(
                (
                    RETRY_BACKOFF * retry
                    for retry in range(1, settings.CELERY_TASK_MAX_RETRIES + 1)
                )
            )
            * 3
        )
        self.assertEqual(
            get_node_calls(mongo_flow_ids=[mongo_flow_id])["node_calls"][0]["retries"],
            settings.CELERY_TASK_MAX_RETRIES,
        )

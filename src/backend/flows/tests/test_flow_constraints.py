import threading
import time
from unittest import TestCase
from unittest.mock import patch

from django.conf import settings

from flows.utils import FlowExecutionLock, FlowExecutionLockError
from flows.tasks import FlowTimeoutError
from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    clear_flows_collection,
    clear_gridfs,
)
from symmy.test_utils.model_mixins import SimpleFlowData


class TestFlowExecutionConstraints(CheckTestsAllowedMixin, TestCase):
    """Test the new flow execution constraints: time limits and single instance."""
    
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        clear_flows_collection()
        clear_gridfs()

    def test_flow_execution_lock_basic(self):
        """Test that FlowExecutionLock works correctly."""
        flow_id = self.flow_data.flow.id
        
        # Test acquiring lock
        lock1 = FlowExecutionLock(flow_id, timeout=10)
        self.assertTrue(lock1.acquire())
        
        # Test that second lock cannot be acquired
        lock2 = FlowExecutionLock(flow_id, timeout=10)
        self.assertFalse(lock2.acquire())
        
        # Test releasing lock
        lock1.release()
        
        # Test that lock can be acquired again after release
        self.assertTrue(lock2.acquire())
        lock2.release()

    def test_flow_execution_lock_context_manager(self):
        """Test FlowExecutionLock as context manager."""
        flow_id = self.flow_data.flow.id
        
        # Test successful context manager usage
        with FlowExecutionLock(flow_id, timeout=10) as lock:
            self.assertIsNotNone(lock)
            
            # Test that another lock cannot be acquired while in context
            lock2 = FlowExecutionLock(flow_id, timeout=10)
            self.assertFalse(lock2.acquire())
        
        # Test that lock is released after context
        lock3 = FlowExecutionLock(flow_id, timeout=10)
        self.assertTrue(lock3.acquire())
        lock3.release()

    def test_flow_execution_lock_context_manager_exception(self):
        """Test FlowExecutionLock context manager when lock cannot be acquired."""
        flow_id = self.flow_data.flow.id
        
        # Acquire lock first
        lock1 = FlowExecutionLock(flow_id, timeout=10)
        self.assertTrue(lock1.acquire())
        
        try:
            # Test that context manager raises exception when lock cannot be acquired
            with self.assertRaises(FlowExecutionLockError):
                with FlowExecutionLock(flow_id, timeout=10):
                    pass
        finally:
            lock1.release()

    def test_single_instance_constraint_in_flow_execute(self):
        """Test that Flow.execute() respects single instance constraint."""
        flow = self.flow_data.flow
        
        # Mock the execute_flow task to simulate a long-running task
        with patch('flows.tasks.execute_flow.delay') as mock_delay:
            # First call should succeed
            mock_delay.return_value.get.return_value = "mock_mongo_id_1"
            result1 = flow.execute()
            self.assertIn("mock_mongo_id_1", result1)
            
            # Second call should fail with lock error message
            result2 = flow.execute()
            self.assertIn("another instance is already running", result2)

    def test_flow_execution_time_limit_configuration(self):
        """Test that flow execution tasks have correct time limits configured."""
        from flows.tasks import execute_tasks
        
        # Check that the task has the correct time limits
        self.assertEqual(execute_tasks.time_limit, 1200)  # 20 minutes
        self.assertEqual(execute_tasks.soft_time_limit, 1180)  # 19 minutes 40 seconds

    def test_flow_execution_expires_configuration(self):
        """Test that flow execution has correct expiration time."""
        flow = self.flow_data.flow
        
        with patch('flows.tasks.execute_tasks.apply_async') as mock_apply_async:
            with patch('flows.tasks.FlowExecutionLock') as mock_lock_class:
                # Mock the lock to allow execution
                mock_lock = mock_lock_class.return_value
                mock_lock.acquire.return_value = True
                
                # Execute flow
                flow.execute()
                
                # Check that apply_async was called with correct expires parameter
                mock_apply_async.assert_called_once()
                call_kwargs = mock_apply_async.call_args[1]
                self.assertEqual(call_kwargs['expires'], 1200)  # 20 minutes

    def test_concurrent_flow_execution_prevention(self):
        """Test that concurrent executions of the same flow are prevented."""
        flow_id = self.flow_data.flow.id
        results = []
        
        def try_acquire_lock():
            lock = FlowExecutionLock(flow_id, timeout=1)
            result = lock.acquire(blocking=False)
            results.append(result)
            if result:
                time.sleep(0.1)  # Hold lock briefly
                lock.release()
        
        # Start multiple threads trying to acquire the same lock
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=try_acquire_lock)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Only one thread should have successfully acquired the lock
        successful_acquisitions = sum(results)
        self.assertEqual(successful_acquisitions, 1)

    def test_flow_lock_timeout_parameter(self):
        """Test that flow lock respects timeout parameter."""
        flow_id = self.flow_data.flow.id
        
        # Test with custom timeout
        lock = FlowExecutionLock(flow_id, timeout=5)
        self.assertEqual(lock.timeout, 5)
        self.assertIn("flow_execution_lock_", lock.lock_key)
        self.assertIn(str(flow_id), lock.lock_key)

    def test_flow_execution_error_handling(self):
        """Test that flow execution errors are properly handled."""
        from flows.tasks import FlowExecutionError, FlowTimeoutError
        
        # Test that our custom exceptions exist and inherit correctly
        self.assertTrue(issubclass(FlowTimeoutError, FlowExecutionError))
        self.assertTrue(issubclass(FlowExecutionError, Exception))
        
        # Test exception instantiation
        timeout_error = FlowTimeoutError("Test timeout")
        self.assertEqual(str(timeout_error), "Test timeout")
        
        execution_error = FlowExecutionError("Test execution error")
        self.assertEqual(str(execution_error), "Test execution error")

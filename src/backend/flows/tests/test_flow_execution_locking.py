"""
Tests for the enhanced flow execution locking mechanism.
"""
import time
from unittest.mock import patch, MagicMock
from django.test import TestCase, TransactionTestCase
from django.utils import timezone
from django.db import transaction

from flows.models import Flow, FlowExecutionLockRecord
from flows.utils import FlowExecutionLock, handle_flow_completion, clear_stale_flow_locks
from symmy.test_utils.utils import CheckTestsAllowedMixin
from symmy.test_utils.model_mixins import SimpleFlowData


class FlowExecutionLockingTestCase(CheckTestsAllowedMixin, TransactionTestCase):
    """Test the enhanced flow execution locking mechanism."""

    def setUp(self):
        """Set up test data."""
        self.flow_data = SimpleFlowData()
        self.flow = self.flow_data.flow

    def tearDown(self):
        """Clean up after tests - ONLY delete test flow locks, not all locks!"""
        # Clear any remaining locks ONLY for this test flow
        FlowExecutionLockRecord.objects.filter(flow=self.flow).delete()
        # DO NOT call clear_stale_flow_locks() or delete all records - affects production!

    def test_basic_lock_acquisition_and_release(self):
        """Test basic lock acquisition and release."""
        lock = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439011",
            task_id="test-task-123"
        )

        # Should be able to acquire lock
        self.assertTrue(lock.acquire())

        # Should create database record
        db_record = FlowExecutionLockRecord.objects.filter(flow=self.flow).first()
        self.assertIsNotNone(db_record)
        self.assertEqual(db_record.mongo_flow_id, "507f1f77bcf86cd799439011")
        self.assertEqual(db_record.locked_by_task_id, "test-task-123")

        # Release lock
        lock.release()

        # Database record should be cleaned up
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

    def test_concurrent_lock_acquisition_prevention(self):
        """Test that concurrent lock acquisition is prevented."""
        lock1 = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439011",
            task_id="test-task-1"
        )

        lock2 = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439012",
            task_id="test-task-2"
        )

        # First lock should succeed
        self.assertTrue(lock1.acquire())

        # Second lock should fail
        self.assertFalse(lock2.acquire())

        # Only one database record should exist
        self.assertEqual(FlowExecutionLockRecord.objects.filter(flow=self.flow).count(), 1)

        # Release first lock
        lock1.release()

        # Now second lock should succeed
        self.assertTrue(lock2.acquire())

        # Clean up
        lock2.release()

    def test_flow_completion_callback(self):
        """Test that flow completion callback releases locks."""
        lock = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439011",
            task_id="test-task-123"
        )

        # Acquire lock
        self.assertTrue(lock.acquire())

        # Verify database record exists
        self.assertTrue(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

        # Simulate flow completion
        handle_flow_completion("507f1f77bcf86cd799439011", "success")

        # Database record should be cleaned up
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

    def test_expired_lock_cleanup(self):
        """Test cleanup of expired locks - SAFE VERSION that doesn't affect production."""
        # Create an expired lock record manually
        expired_time = timezone.now() - timezone.timedelta(hours=1)
        expired_record = FlowExecutionLockRecord.objects.create(
            flow=self.flow,
            mongo_flow_id="507f1f77bcf86cd799439011",
            locked_by_task_id="expired-task",
            redis_lock_key=f"flow_execution_lock_{self.flow.id}",
            locked_at=expired_time,
            timeout_at=expired_time + timezone.timedelta(minutes=20)
        )

        # Verify record exists and is expired
        record = FlowExecutionLockRecord.objects.get(flow=self.flow)
        self.assertTrue(record.is_expired)

        # Test the is_expired property instead of calling clear_stale_flow_locks
        # which could affect production data
        self.assertTrue(expired_record.is_expired)

        # Manually clean up the test record (safe)
        expired_record.delete()
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

    def test_lock_without_mongo_flow_id_fails(self):
        """Test that lock acquisition fails without mongo_flow_id."""
        lock = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id=None,  # Missing
            task_id="test-task-123"
        )

        # Should fail to acquire lock
        self.assertFalse(lock.acquire())

        # No database record should be created
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

    def test_lock_context_manager(self):
        """Test lock context manager functionality."""
        from flows.utils import FlowExecutionLockError

        # Test successful context manager usage
        with FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439011",
            task_id="test-task-123"
        ) as lock:
            # Lock should be acquired
            self.assertTrue(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

        # Lock should be released after context
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

        # Test failed acquisition
        lock1 = FlowExecutionLock(
            flow_id=self.flow.id,
            mongo_flow_id="507f1f77bcf86cd799439011",
            task_id="test-task-1"
        )
        lock1.acquire()

        try:
            with self.assertRaises(FlowExecutionLockError):
                with FlowExecutionLock(
                    flow_id=self.flow.id,
                    mongo_flow_id="507f1f77bcf86cd799439012",
                    task_id="test-task-2"
                ):
                    pass
        finally:
            lock1.release()


class FlowExecutionIntegrationTestCase(CheckTestsAllowedMixin, TransactionTestCase):
    """Integration tests for flow execution with locking."""

    def setUp(self):
        """Set up test data."""
        self.flow_data = SimpleFlowData()
        self.flow = self.flow_data.flow

    def tearDown(self):
        """Clean up after tests - ONLY delete test flow locks, not all locks!"""
        # Clear any remaining locks ONLY for this test flow
        FlowExecutionLockRecord.objects.filter(flow=self.flow).delete()
        # DO NOT call clear_stale_flow_locks() or delete all records - affects production!

    @patch('flows.tasks.execute_tasks.apply_async')
    def test_flow_execution_with_locking(self, mock_apply_async):
        """Test that flow execution properly uses the locking mechanism."""
        # Mock the async task to avoid actual execution
        mock_apply_async.return_value = MagicMock()

        # Execute flow
        result = self.flow.execute()

        # Should return a mongo_flow_id (not an error)
        self.assertIsInstance(result, str)
        self.assertEqual(len(result), 24)  # MongoDB ObjectId length

        # Should have created a lock record
        lock_records = FlowExecutionLockRecord.objects.filter(flow=self.flow)
        self.assertEqual(lock_records.count(), 1)

        lock_record = lock_records.first()
        self.assertEqual(lock_record.mongo_flow_id, result)

        # Simulate flow completion to clean up
        handle_flow_completion(result, "success")

        # Lock should be cleaned up
        self.assertFalse(FlowExecutionLockRecord.objects.filter(flow=self.flow).exists())

    @patch('flows.tasks.execute_tasks.apply_async')
    def test_concurrent_flow_execution_prevention(self, mock_apply_async):
        """Test that concurrent execution of the same flow is prevented."""
        mock_apply_async.return_value = MagicMock()

        # First execution should succeed
        result1 = self.flow.execute()
        self.assertIsInstance(result1, str)

        # Second execution should be blocked
        result2 = self.flow.execute()
        self.assertIn("already running", result2)

        # Clean up
        handle_flow_completion(result1, "success")

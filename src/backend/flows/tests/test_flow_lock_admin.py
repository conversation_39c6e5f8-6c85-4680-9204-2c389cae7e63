"""
Tests for the FlowExecutionLockRecord admin interface.
"""
from django.test import TestCase
from django.contrib.admin.sites import AdminSite
from django.utils import timezone
from datetime import timedelta

from flows.admin import FlowExecution<PERSON>ock<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>us<PERSON>ilter, LockOrganizationFilter
from flows.models import Flow, FlowExecutionLockRecord
from symmy.test_utils.model_mixins import SimpleFlowData


class MockRequest:
    """Mock request object for testing admin filters."""
    def __init__(self, user=None):
        self.user = user


class FlowExecutionLockRecordAdminTest(TestCase):
    """Test cases for FlowExecutionLockRecord admin interface."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()
        cls.site = AdminSite()
        cls.admin = FlowExecutionLockRecordAdmin(FlowExecutionLockRecord, cls.site)

    def setUp(self):
        super().setUp()
        # Create a lock record for testing
        self.lock_record = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439011",
            locked_by_task_id="test-task-123",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_admin_registration(self):
        """Test that FlowExecutionLockRecord is properly registered with admin."""
        from django.contrib import admin
        self.assertIn(FlowExecutionLockRecord, admin.site._registry)

    def test_list_display_fields(self):
        """Test that all expected fields are in list_display."""
        expected_fields = [
            "flow_name",
            "lock_status_display",
            "locked_at",
            "time_remaining_display",
            "locked_by_task_id",
            "mongo_flow_id",
        ]
        self.assertEqual(self.admin.list_display, tuple(expected_fields))

    def test_readonly_fields(self):
        """Test that all fields are readonly (locks shouldn't be manually edited)."""
        expected_readonly = [
            "uuid",
            "flow",
            "mongo_flow_id",
            "locked_at",
            "locked_by_task_id",
            "redis_lock_key",
            "timeout_at",
            "lock_status_display",
            "time_remaining_display",
            "lock_duration_display",
        ]
        self.assertEqual(self.admin.readonly_fields, tuple(expected_readonly))

    def test_no_add_permission(self):
        """Test that manual creation of lock records is prevented."""
        request = MockRequest()
        self.assertFalse(self.admin.has_add_permission(request))

    def test_has_change_permission_but_readonly(self):
        """Test that change permission is allowed but fields are readonly."""
        request = MockRequest()
        self.assertTrue(self.admin.has_change_permission(request))

        # Test that all fields become readonly when editing an existing object
        readonly_fields = self.admin.get_readonly_fields(request, self.lock_record)
        model_field_names = [field.name for field in self.lock_record._meta.fields]
        for field_name in model_field_names:
            self.assertIn(field_name, readonly_fields)

    def test_save_model_prevents_changes(self):
        """Test that save_model prevents actual changes to lock records."""
        request = MockRequest()
        original_task_id = self.lock_record.locked_by_task_id

        # Try to change the task ID
        self.lock_record.locked_by_task_id = "modified-task-id"

        # Call save_model (simulating admin save)
        self.admin.save_model(request, self.lock_record, None, change=True)

        # Refresh from database and verify no change was saved
        self.lock_record.refresh_from_db()
        self.assertEqual(self.lock_record.locked_by_task_id, original_task_id)

    def test_flow_name_display(self):
        """Test the flow_name display method."""
        result = self.admin.flow_name(self.lock_record)
        self.assertEqual(result, self.flow_data.flow.name)

    def test_lock_status_display_active(self):
        """Test lock status display for active locks."""
        result = self.admin.lock_status_display(self.lock_record)
        self.assertIn("🟢", result)
        self.assertIn("Active", result)

    def test_lock_status_display_expired(self):
        """Test lock status display for expired locks."""
        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 2", project=self.flow_data.project)

        # Create an expired lock
        expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439012",
            locked_by_task_id="expired-task-456",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

        result = self.admin.lock_status_display(expired_lock)
        self.assertIn("🔴", result)
        self.assertIn("Expired", result)

    def test_time_remaining_display_active(self):
        """Test time remaining display for active locks."""
        result = self.admin.time_remaining_display(self.lock_record)
        # Should show time in format like "19m 59s" or similar
        self.assertRegex(result, r'\d+[mh]')

    def test_time_remaining_display_expired(self):
        """Test time remaining display for expired locks."""
        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 3", project=self.flow_data.project)

        expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439013",
            locked_by_task_id="expired-task-789",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

        result = self.admin.time_remaining_display(expired_lock)
        self.assertIn("-", result)

    def test_lock_duration_display(self):
        """Test lock duration display method."""
        result = self.admin.lock_duration_display(self.lock_record)
        # Should show duration in format like "20m"
        self.assertRegex(result, r'\d+m')


class LockStatusFilterTest(TestCase):
    """Test cases for LockStatusFilter."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    def setUp(self):
        super().setUp()
        self.filter = LockStatusFilter(None, {}, FlowExecutionLockRecord, None)

        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 4", project=self.flow_data.project)

        # Create active and expired locks
        self.active_lock = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439014",
            locked_by_task_id="active-task",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        self.expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439015",
            locked_by_task_id="expired-task",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_filter_lookups(self):
        """Test that filter provides correct lookup options."""
        lookups = self.filter.lookups(None, None)
        expected = [("active", "Active"), ("expired", "Expired")]
        self.assertEqual(lookups, expected)

    def test_filter_active_locks(self):
        """Test filtering for active locks."""
        self.filter.value = lambda: "active"
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        self.assertIn(self.active_lock, filtered)
        self.assertNotIn(self.expired_lock, filtered)

    def test_filter_expired_locks(self):
        """Test filtering for expired locks."""
        self.filter.value = lambda: "expired"
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        self.assertIn(self.expired_lock, filtered)
        self.assertNotIn(self.active_lock, filtered)


class LockOrganizationFilterTest(TestCase):
    """Test cases for LockOrganizationFilter."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    def setUp(self):
        super().setUp()

        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 5", project=self.flow_data.project)

        # Create lock records first
        self.lock1 = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439016",
            locked_by_task_id="task-org1",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        self.lock2 = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439017",
            locked_by_task_id="task-org2",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        # Create a mock model admin for the filter
        class MockModelAdmin:
            model = FlowExecutionLockRecord

        # Now create the filter with the mock model admin
        self.filter = LockOrganizationFilter(None, {}, FlowExecutionLockRecord, MockModelAdmin())

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_filter_by_organization(self):
        """Test filtering locks by organization."""
        self.filter.value = lambda: str(self.flow_data.org.id)
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        # Both flows belong to the same organization in SimpleFlowData
        self.assertIn(self.lock1, filtered)
        self.assertIn(self.lock2, filtered)


class FlowExecutionLockRecordAdminIntegrationTest(TestCase):
    """Integration tests for the admin interface."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    def setUp(self):
        super().setUp()
        # Create a superuser for admin access
        from users.models import SymmyUser
        self.admin_user = SymmyUser.objects.create_superuser(
            email='<EMAIL>',
            password='testpass123'
        )

        # Create a lock record for testing
        self.lock_record = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439018",
            locked_by_task_id="integration-test-task",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_admin_changelist_accessible(self):
        """Test that the admin changelist is accessible."""
        from django.test import Client
        from django.urls import reverse

        client = Client()
        client.force_login(self.admin_user)

        # Get the admin changelist URL
        url = reverse('admin:flows_flowexecutionlockrecord_changelist')
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.flow_data.flow.name)
        self.assertContains(response, "integration-test-task")

    def test_admin_change_view_accessible(self):
        """Test that individual lock records are viewable (clickable)."""
        from django.test import Client
        from django.urls import reverse

        client = Client()
        client.force_login(self.admin_user)

        # Get the admin change URL for the specific lock record
        url = reverse('admin:flows_flowexecutionlockrecord_change', args=[self.lock_record.pk])
        response = client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.flow_data.flow.name)
        self.assertContains(response, "integration-test-task")
        self.assertContains(response, "507f1f77bcf86cd799439018")

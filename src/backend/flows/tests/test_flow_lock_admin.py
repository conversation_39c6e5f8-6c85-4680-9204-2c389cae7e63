"""
Tests for the FlowExecutionLockRecord admin interface.
"""
from django.test import TestCase
from django.contrib.admin.sites import AdminSite
from django.utils import timezone
from datetime import timedelta

from flows.admin import FlowExecution<PERSON>ock<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>us<PERSON>ilter, LockOrganizationFilter
from flows.models import Flow, FlowExecutionLockRecord
from symmy.test_utils.model_mixins import SimpleFlowData


class MockRequest:
    """Mock request object for testing admin filters."""
    def __init__(self, user=None):
        self.user = user


class FlowExecutionLockRecordAdminTest(TestCase):
    """Test cases for FlowExecutionLockRecord admin interface."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()
        cls.site = AdminSite()
        cls.admin = FlowExecutionLockRecordAdmin(FlowExecutionLockRecord, cls.site)

    def setUp(self):
        super().setUp()
        # Create a lock record for testing
        self.lock_record = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439011",
            locked_by_task_id="test-task-123",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_admin_registration(self):
        """Test that FlowExecutionLockRecord is properly registered with admin."""
        from django.contrib import admin
        self.assertIn(FlowExecutionLockRecord, admin.site._registry)

    def test_list_display_fields(self):
        """Test that all expected fields are in list_display."""
        expected_fields = [
            "flow_name",
            "lock_status_display",
            "locked_at",
            "time_remaining_display",
            "locked_by_task_id",
            "mongo_flow_id",
        ]
        self.assertEqual(self.admin.list_display, tuple(expected_fields))

    def test_readonly_fields(self):
        """Test that all fields are readonly (locks shouldn't be manually edited)."""
        expected_readonly = [
            "uuid",
            "flow",
            "mongo_flow_id",
            "locked_at",
            "locked_by_task_id",
            "redis_lock_key",
            "timeout_at",
            "lock_status_display",
            "time_remaining_display",
            "lock_duration_display",
        ]
        self.assertEqual(self.admin.readonly_fields, tuple(expected_readonly))

    def test_no_add_permission(self):
        """Test that manual creation of lock records is prevented."""
        request = MockRequest()
        self.assertFalse(self.admin.has_add_permission(request))

    def test_no_change_permission(self):
        """Test that manual editing of lock records is prevented."""
        request = MockRequest()
        self.assertFalse(self.admin.has_change_permission(request))

    def test_flow_name_display(self):
        """Test the flow_name display method."""
        result = self.admin.flow_name(self.lock_record)
        self.assertEqual(result, self.flow_data.flow.name)

    def test_lock_status_display_active(self):
        """Test lock status display for active locks."""
        result = self.admin.lock_status_display(self.lock_record)
        self.assertIn("🟢", result)
        self.assertIn("Active", result)

    def test_lock_status_display_expired(self):
        """Test lock status display for expired locks."""
        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 2", project=self.flow_data.project)

        # Create an expired lock
        expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439012",
            locked_by_task_id="expired-task-456",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

        result = self.admin.lock_status_display(expired_lock)
        self.assertIn("🔴", result)
        self.assertIn("Expired", result)

    def test_time_remaining_display_active(self):
        """Test time remaining display for active locks."""
        result = self.admin.time_remaining_display(self.lock_record)
        # Should show time in format like "19m 59s" or similar
        self.assertRegex(result, r'\d+[mh]')

    def test_time_remaining_display_expired(self):
        """Test time remaining display for expired locks."""
        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 3", project=self.flow_data.project)

        expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439013",
            locked_by_task_id="expired-task-789",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

        result = self.admin.time_remaining_display(expired_lock)
        self.assertIn("-", result)

    def test_lock_duration_display(self):
        """Test lock duration display method."""
        result = self.admin.lock_duration_display(self.lock_record)
        # Should show duration in format like "20m"
        self.assertRegex(result, r'\d+m')


class LockStatusFilterTest(TestCase):
    """Test cases for LockStatusFilter."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    def setUp(self):
        super().setUp()
        self.filter = LockStatusFilter(None, {}, FlowExecutionLockRecord, None)

        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 4", project=self.flow_data.project)

        # Create active and expired locks
        self.active_lock = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439014",
            locked_by_task_id="active-task",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        self.expired_lock = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439015",
            locked_by_task_id="expired-task",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_filter_lookups(self):
        """Test that filter provides correct lookup options."""
        lookups = self.filter.lookups(None, None)
        expected = [("active", "Active"), ("expired", "Expired")]
        self.assertEqual(lookups, expected)

    def test_filter_active_locks(self):
        """Test filtering for active locks."""
        self.filter.value = lambda: "active"
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        self.assertIn(self.active_lock, filtered)
        self.assertNotIn(self.expired_lock, filtered)

    def test_filter_expired_locks(self):
        """Test filtering for expired locks."""
        self.filter.value = lambda: "expired"
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        self.assertIn(self.expired_lock, filtered)
        self.assertNotIn(self.active_lock, filtered)


class LockOrganizationFilterTest(TestCase):
    """Test cases for LockOrganizationFilter."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    def setUp(self):
        super().setUp()

        # Create a second flow for testing
        from flows.models import Flow
        flow2 = Flow.objects.create(name="Test Flow 5", project=self.flow_data.project)

        # Create lock records first
        self.lock1 = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439016",
            locked_by_task_id="task-org1",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        self.lock2 = FlowExecutionLockRecord.objects.create(
            flow=flow2,
            mongo_flow_id="507f1f77bcf86cd799439017",
            locked_by_task_id="task-org2",
            redis_lock_key=f"flow_execution_lock_{flow2.id}",
            timeout_at=timezone.now() + timedelta(minutes=20)
        )

        # Create a mock model admin for the filter
        class MockModelAdmin:
            model = FlowExecutionLockRecord

        # Now create the filter with the mock model admin
        self.filter = LockOrganizationFilter(None, {}, FlowExecutionLockRecord, MockModelAdmin())

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_filter_by_organization(self):
        """Test filtering locks by organization."""
        self.filter.value = lambda: str(self.flow_data.org.id)
        queryset = FlowExecutionLockRecord.objects.all()
        filtered = self.filter.queryset(None, queryset)

        # Both flows belong to the same organization in SimpleFlowData
        self.assertIn(self.lock1, filtered)
        self.assertIn(self.lock2, filtered)

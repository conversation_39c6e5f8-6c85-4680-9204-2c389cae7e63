# Test Safety Guidelines for Flow Tests

## ⚠️ CRITICAL WARNING ⚠️

**<PERSON>VER delete all data in tests!** Tests should only clean up their own test data, not production data.

## Dangerous Operations to AVOID

### ❌ NEVER DO THESE:

```python
# DANGEROUS - Deletes ALL flow execution locks from production!
FlowExecutionLockRecord.objects.all().delete()

# DANGEROUS - Clears ALL Redis flow locks from production!
redis_keys = redis_client.keys("flow_execution_lock_*")
if redis_keys:
    redis_client.delete(*redis_keys)

# DANGEROUS - Clears ALL stale locks from production!
clear_stale_flow_locks()

# DANGEROUS - Deletes ALL of any model
SomeModel.objects.all().delete()
```

## Safe Operations to USE

### ✅ SAFE ALTERNATIVES:

```python
# SAFE - Only delete locks for specific test flow
FlowExecutionLockRecord.objects.filter(flow=self.test_flow).delete()

# SAFE - Only delete specific Redis lock
redis_client.delete(f"flow_execution_lock_{self.test_flow.id}")

# SAFE - Only delete specific test records
SomeModel.objects.filter(id=self.test_record_id).delete()
```

## Test Cleanup Best Practices

### 1. Use Specific Filters
Always filter by test-specific identifiers:
- Flow ID
- Test record IDs
- Test-specific keys

### 2. Avoid Global Cleanup Functions
Don't call functions that clean up all data:
- `clear_stale_flow_locks()`
- Any function that operates on all records

### 3. Use Test Database Isolation
Ensure tests use test databases, not production databases.

### 4. Clean Up Only What You Create
Only clean up data that your specific test created.

## Example Safe Test Pattern

```python
class SafeFlowTestCase(TestCase):
    def setUp(self):
        """Create test data."""
        self.test_flow = Flow.objects.create(name="test_flow")
        
    def tearDown(self):
        """Clean up ONLY test data."""
        # SAFE - Only clean up this test's data
        FlowExecutionLockRecord.objects.filter(flow=self.test_flow).delete()
        
        # SAFE - Only clean up specific Redis key
        redis_client = get_redis_connection("default")
        redis_client.delete(f"flow_execution_lock_{self.test_flow.id}")
        
    def test_something(self):
        """Test something safely."""
        # Test logic here
        pass
```

## Recent Fixes Applied

The following dangerous operations were fixed:

1. **test_flow_constraints.py**: Removed `redis_client.keys("flow_execution_lock_*")` and `FlowExecutionLockRecord.objects.all().delete()`
2. **test_flow_execution_locking.py**: Removed `clear_stale_flow_locks()` and `FlowExecutionLockRecord.objects.all().delete()`

## Verification

Before committing test changes, verify:
1. No `.all().delete()` calls
2. No `clear_stale_flow_locks()` calls  
3. No `redis_client.keys("*")` patterns
4. All cleanup is scoped to test data only

## Impact of Previous Issue

The dangerous operations were:
- Deleting ALL flow execution locks from production Redis
- Deleting ALL flow execution lock records from production database
- Potentially causing production flow execution failures

This has been fixed, but serves as a reminder to be extremely careful with test cleanup operations.

from django.db.utils import IntegrityError
from django.test import TestCase

from core.models import Project, Organization
from flows.models import Flow, Node, NodeRelation
from users.models import SymmyUser

# Create your tests here.
class NodeTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        test_user = SymmyUser.objects.create_user(
            email="<EMAIL>", first_name="user", password="PASSWORD"
        )
        test_org = Organization.objects.create(name="Test Org", email="<EMAIL>")
        test_project = Project.objects.create(
            name="Test Project", organization=test_org, user=test_user
        )
        Flow.objects.create(project=test_project)

    def test_append(self):
        flow = Flow.objects.first()

        n1 = Node.objects.create(flow=flow)
        n2 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)
        n4 = Node.objects.create(flow=flow)
        n5 = Node.objects.create(flow=flow)

        # The test uses the following schema:
        # n1 -> n2 -> n3 -> n4
        #       n2 -> n5

        self.assertTrue(n1.append(n2))
        self.assertTrue(n3.append(n4))
        self.assertTrue(n2.append(n3))
        self.assertTrue(n2.append(n5))

        self.assertFalse(n2.append(n5))
        self.assertFalse(n2.append(n1))

        self.assertIn(n1, n2.ancestors)
        self.assertIn(n1, n3.ancestors)
        self.assertIn(n1, n4.ancestors)
        self.assertIn(n1, n5.ancestors)
        self.assertIn(n2, n3.ancestors)
        self.assertIn(n2, n4.ancestors)
        self.assertIn(n2, n5.ancestors)
        self.assertIn(n3, n4.ancestors)

        self.assertIn(n2, n1.descendants)
        self.assertIn(n3, n1.descendants)
        self.assertIn(n3, n2.descendants)
        self.assertIn(n4, n1.descendants)
        self.assertIn(n4, n2.descendants)
        self.assertIn(n4, n3.descendants)
        self.assertIn(n5, n1.descendants)
        self.assertIn(n5, n2.descendants)

        self.assertNotIn(n1, n2.descendants)
        self.assertNotIn(n1, n3.descendants)
        self.assertNotIn(n1, n4.descendants)
        self.assertNotIn(n1, n5.descendants)
        self.assertNotIn(n2, n3.descendants)
        self.assertNotIn(n2, n4.descendants)
        self.assertNotIn(n2, n5.descendants)
        self.assertNotIn(n3, n4.descendants)
        self.assertNotIn(n3, n5.descendants)
        self.assertNotIn(n4, n5.descendants)

        self.assertNotIn(n2, n1.ancestors)
        self.assertNotIn(n3, n1.ancestors)
        self.assertNotIn(n3, n2.ancestors)
        self.assertNotIn(n4, n1.ancestors)
        self.assertNotIn(n4, n2.ancestors)
        self.assertNotIn(n4, n3.ancestors)
        self.assertNotIn(n5, n1.ancestors)
        self.assertNotIn(n5, n2.ancestors)
        self.assertNotIn(n5, n3.ancestors)
        self.assertNotIn(n5, n4.ancestors)

        relation = NodeRelation.objects.get(ancestor=n1, descendant=n2)
        self.assertEqual(relation.depth, 1)

        relation = NodeRelation.objects.get(ancestor=n1, descendant=n3)
        self.assertEqual(relation.depth, 2)

        relation = NodeRelation.objects.get(ancestor=n1, descendant=n4)
        self.assertEqual(relation.depth, 3)

        relation = NodeRelation.objects.get(ancestor=n1, descendant=n5)
        self.assertEqual(relation.depth, 2)

        relation = NodeRelation.objects.get(ancestor=n2, descendant=n3)
        self.assertEqual(relation.depth, 1)

        relation = NodeRelation.objects.get(ancestor=n2, descendant=n4)
        self.assertEqual(relation.depth, 2)

        relation = NodeRelation.objects.get(ancestor=n2, descendant=n5)
        self.assertEqual(relation.depth, 1)

        relation = NodeRelation.objects.get(ancestor=n3, descendant=n4)
        self.assertEqual(relation.depth, 1)

    def test_one_ancestor_policy(self):
        flow = Flow.objects.first()

        n1 = Node.objects.create(flow=flow)
        n2 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)

        self.assertTrue(n2.append(n1))
        self.assertFalse(n3.append(n1))

        with self.assertRaises(IntegrityError):
            NodeRelation.objects.create(ancestor=n3, descendant=n1)

    def test_disconnect(self):
        flow = Flow.objects.first()

        n1 = Node.objects.create(flow=flow)
        n2 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)
        n4 = Node.objects.create(flow=flow)
        n5 = Node.objects.create(flow=flow)

        # The test uses the following schema:
        # n1 -> n2 -> n3 -> n4
        #       n2 -> n5

        n1.append(n2)
        n3.append(n4)
        n2.append(n3)
        n2.append(n5)

        self.assertTrue(Node.disconnect(parent=n2, child=n3))
        self.assertFalse(Node.disconnect(parent=n2, child=n3))
        self.assertFalse(Node.disconnect(parent=n1, child=n5))

        self.assertIn(n1, n2.ancestors)
        self.assertIn(n3, n4.ancestors)

        self.assertIn(n2, n1.descendants)
        self.assertIn(n4, n3.descendants)

        self.assertNotIn(n3, n2.descendants)
        self.assertNotIn(n3, n1.descendants)
        self.assertNotIn(n4, n2.descendants)
        self.assertNotIn(n4, n1.descendants)

        self.assertNotIn(n2, n3.ancestors)
        self.assertNotIn(n2, n4.ancestors)
        self.assertNotIn(n1, n3.ancestors)
        self.assertNotIn(n1, n4.ancestors)

    def test_disinherit(self):
        flow = Flow.objects.first()

        n1 = Node.objects.create(flow=flow)
        n2 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)
        n4 = Node.objects.create(flow=flow)
        n5 = Node.objects.create(flow=flow)

        # The test uses the following schema:
        # n1 -> n2 -> n3 -> n4
        #       n2 -> n5

        n1.append(n2)
        n3.append(n4)
        n2.append(n3)
        n2.append(n5)

        n2.disinherit()

        self.assertIn(n1, n2.ancestors)
        self.assertIn(n3, n4.ancestors)

        self.assertIn(n2, n1.descendants)
        self.assertIn(n4, n3.descendants)

        self.assertNotIn(n3, n2.descendants)
        self.assertNotIn(n3, n1.descendants)
        self.assertNotIn(n4, n2.descendants)
        self.assertNotIn(n4, n1.descendants)
        self.assertNotIn(n5, n2.descendants)
        self.assertNotIn(n5, n1.descendants)

        self.assertNotIn(n2, n3.ancestors)
        self.assertNotIn(n2, n4.ancestors)
        self.assertNotIn(n2, n5.ancestors)
        self.assertNotIn(n1, n3.ancestors)
        self.assertNotIn(n1, n4.ancestors)
        self.assertNotIn(n1, n5.ancestors)

    def test_insert(self):
        # The test uses the following schema:
        # n1 -> n3 ====> n1 -> n2 -> n3
        flow = Flow.objects.first()

        n1 = Node.objects.create(flow=flow)
        n3 = Node.objects.create(flow=flow)

        n1.append(n3)

        n2 = Node.objects.create(flow=flow)
        n4 = Node.objects.create(flow=flow)

        self.assertTrue(Node.insert(parent=n1, child=n3, node=n2))
        self.assertFalse(Node.insert(parent=n1, child=n2, node=n1))
        self.assertFalse(Node.insert(parent=n1, child=n3, node=n2))
        self.assertFalse(Node.insert(parent=n1, child=n3, node=n4))

        self.assertIn(n1, n3.ancestors)
        self.assertIn(n1, n2.ancestors)
        self.assertIn(n2, n3.ancestors)

        self.assertIn(n2, n1.descendants)
        self.assertIn(n3, n1.descendants)
        self.assertIn(n3, n2.descendants)

        self.assertNotIn(n1, n2.descendants)
        self.assertNotIn(n1, n3.descendants)
        self.assertNotIn(n2, n3.descendants)

        self.assertNotIn(n2, n1.ancestors)
        self.assertNotIn(n3, n1.ancestors)
        self.assertNotIn(n3, n2.ancestors)

        self.assertTrue(Node.insert(parent=n1, child=n2, node=n4))

"""
Tests for periodic tasks related to flow execution monitoring.
"""
from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch, MagicMock

from flows.tasks import check_stuck_flows, clear_expired_flow_locks
from flows.models import FlowExecutionLockRecord
from symmy.test_utils.model_mixins import SimpleFlowData


class CheckStuckFlowsTaskTest(TestCase):
    """Test cases for the check_stuck_flows periodic task."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    @patch('mongo.conf.flows')
    @patch('flows.utils.handle_flow_completion')
    def test_check_stuck_flows_no_stuck_flows(self, mock_handle_completion, mock_flows):
        """Test check_stuck_flows when no stuck flows are found."""
        # Mock MongoDB to return no stuck flows
        mock_flows.find.return_value = []

        result = check_stuck_flows()

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['stuck_flows_found'], 0)
        self.assertEqual(result['flows_timed_out'], 0)

        # Verify MongoDB query was called with correct parameters
        mock_flows.find.assert_called_once()
        call_args = mock_flows.find.call_args[0][0]
        self.assertIn('status.code', call_args)
        self.assertIn('start', call_args)

    @patch('mongo.conf.flows')
    @patch('flows.utils.handle_flow_completion')
    @patch('mongo.operations.FlowLogOperations')
    def test_check_stuck_flows_with_stuck_flows(self, mock_flow_log_ops, mock_handle_completion, mock_flows):
        """Test check_stuck_flows when stuck flows are found."""
        # Mock stuck flow data
        stuck_flow_doc = {
            '_id': '507f1f77bcf86cd799439011',
            'start': timezone.now() - timedelta(minutes=30),
            'status': {'code': 1}  # in_progress status
        }
        mock_flows.find.return_value = [stuck_flow_doc]

        # Mock FlowLogOperations
        mock_flow_log_instance = MagicMock()
        mock_flow_log_ops.return_value = mock_flow_log_instance

        result = check_stuck_flows()

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['stuck_flows_found'], 1)
        self.assertEqual(result['flows_timed_out'], 1)

        # Verify flow was updated to failed status
        mock_flow_log_instance.set_params.assert_called_once()
        mock_flow_log_instance.update_log.assert_called_once()

        # Verify completion handler was called
        mock_handle_completion.assert_called_once_with('507f1f77bcf86cd799439011', 'timeout')

    @patch('mongo.conf.flows')
    def test_check_stuck_flows_handles_exceptions(self, mock_flows):
        """Test that check_stuck_flows handles exceptions gracefully."""
        # Mock MongoDB to raise an exception
        mock_flows.find.side_effect = Exception("Database error")

        result = check_stuck_flows()

        self.assertEqual(result['status'], 'error')
        self.assertIn('error', result)
        self.assertEqual(result['stuck_flows_found'], 0)
        self.assertEqual(result['flows_timed_out'], 0)

    @patch('mongo.conf.flows')
    @patch('flows.utils.handle_flow_completion')
    def test_check_stuck_flows_orphaned_locks(self, mock_handle_completion, mock_flows):
        """Test that check_stuck_flows cleans up orphaned lock records."""
        # No stuck flows in MongoDB
        mock_flows.find.return_value = []

        # Create an expired lock record
        expired_lock = FlowExecutionLockRecord.objects.create(
            flow=self.flow_data.flow,
            mongo_flow_id="507f1f77bcf86cd799439012",
            locked_by_task_id="expired-task",
            redis_lock_key=f"flow_execution_lock_{self.flow_data.flow.id}",
            timeout_at=timezone.now() - timedelta(minutes=5)
        )

        # Mock MongoDB to return no corresponding flow
        mock_flows.find_one.return_value = None

        result = check_stuck_flows()

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['orphaned_locks_cleaned'], 1)

        # Verify completion handler was called for cleanup
        mock_handle_completion.assert_called_with('507f1f77bcf86cd799439012', 'cleanup')


class ClearExpiredFlowLocksTaskTest(TestCase):
    """Test cases for the clear_expired_flow_locks periodic task."""

    @patch('flows.utils.clear_stale_flow_locks')
    def test_clear_expired_flow_locks_success(self, mock_clear_locks):
        """Test successful execution of clear_expired_flow_locks."""
        mock_clear_locks.return_value = 5

        result = clear_expired_flow_locks()

        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['cleared_locks'], 5)
        mock_clear_locks.assert_called_once()

    @patch('flows.utils.clear_stale_flow_locks')
    def test_clear_expired_flow_locks_handles_exceptions(self, mock_clear_locks):
        """Test that clear_expired_flow_locks handles exceptions gracefully."""
        mock_clear_locks.side_effect = Exception("Redis error")

        result = clear_expired_flow_locks()

        self.assertEqual(result['status'], 'error')
        self.assertIn('error', result)
        self.assertEqual(result['cleared_locks'], 0)


class PeriodicTaskIntegrationTest(TestCase):
    """Integration tests for periodic tasks."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.flow_data = SimpleFlowData()

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        super().tearDownClass()

    def test_tasks_are_importable(self):
        """Test that the periodic tasks can be imported successfully."""
        from flows.tasks import check_stuck_flows, clear_expired_flow_locks

        # Verify tasks are callable
        self.assertTrue(callable(check_stuck_flows))
        self.assertTrue(callable(clear_expired_flow_locks))

    def test_task_signatures(self):
        """Test that tasks have the expected signatures."""
        from flows.tasks import check_stuck_flows, clear_expired_flow_locks

        # These should be able to be called without arguments
        try:
            # We won't actually run them in this test, just verify they can be called
            check_stuck_flows.__wrapped__  # Access the unwrapped function
            clear_expired_flow_locks.__wrapped__
        except AttributeError:
            # If __wrapped__ doesn't exist, the functions are not decorated, which is fine
            pass

import datetime

from admin_extra_buttons.decorators import button
from admin_extra_buttons.mixins import ExtraButtonsMixin
from django import forms
from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget
from django.utils.html import format_html

from core.admin import JSONFieldAdmin
from flows.models import Flow, Node, FlowExecutionLockRecord
from flows.views import get_context_logs_view
from mongo.analytics import (
    get_executed_flow_count,
    get_node_calls_count,
    get_request_count,
)
from mongo.operations import get_flow_store_data, store_flow_data
from mongo.types import Status


class FlowAdminForm(forms.ModelForm):
    data_store = forms.JSONField(
        label=_("Data Store"),
        required=False,
        widget=JSONEditorWidget(
            attrs={
                "style": "height:400px;width:100%;position:relative;z-index:1;display:flex;"
            }
        ),
        help_text="",
    )

    class Meta:
        model = Flow
        fields = "__all__"


class OrganizationFilter(SimpleListFilter):
    title = _("Organization")
    parameter_name = "organization"

    def lookups(self, request, model_admin):
        # Get a list of organizations to filter by
        organizations = set(
            [flow.project.organization for flow in model_admin.model.objects.all()]
        )
        return [(org.id, org.name) for org in organizations]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(project__organization__id=self.value())
        return queryset


@admin.register(Flow)
class FlowAdmin(ExtraButtonsMixin, admin.ModelAdmin):
    change_form_template = "flows/admin/flow_change_form.html"

    list_display = (
        "enabled",
        "name",
        "project",
        "schedule",
        "created_at",
        "updated_at",
    )
    list_display_links = ("name",)
    list_filter = (OrganizationFilter, "project", "enabled")

    search_fields = ("name",)

    readonly_fields = ("uuid", "statistics", "periodic_task")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "project",
                    "periodic_task",
                    "enabled",
                    "data_store",
                    "statistics",
                ),
            },
        ),
    )

    form = FlowAdminForm

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, kwargs)
        if obj:
            default_data = get_flow_store_data(obj, json_serializable=True)
            if default_data is not None:
                default_data.pop("uuid", None)
                default_data.pop("_id", None)
            form.base_fields["data_store"].initial = default_data
        else:
            form.base_fields["data_store"].initial = {}
        return form

    def save_model(self, request, obj, form, change):
        data_store = form.cleaned_data.pop("data_store", None)
        if data_store is not None:
            data_store.pop("uuid", None)
            data_store.pop("_id", None)
            store_flow_data(obj, data_store)

        super().save_model(request, obj, form, change)

    @button(
        change_form=True,
        html_attrs={"style": "background-color:#47BAC1;color:white"},
        label=_("Execute"),
    )
    def execute(self, request, pk):
        obj = self.get_object(request, pk)
        try:
            obj.execute()
            self.message_user(request, _(f"Executing flow {obj.name} ({obj.uuid})"))
        except Exception:
            self.message_error_to_user(
                request, _(f"Flow {obj.name} ({obj.uuid}) failed")
            )

    def change_view(self, request, object_id, form_url="", extra_context=None):
        return super().change_view(
            request,
            object_id,
            form_url,
            extra_context=get_context_logs_view(object_id),
        )

    @admin.display(description=_("Schedule"))
    def schedule(self, instance):
        return instance.periodic_task.scheduler

    @admin.display(description="In the last 30 days")
    def statistics(self, instance):
        context = dict()
        to = timezone.now()
        from_ = to - datetime.timedelta(days=30)
        context["executed_flows_success"] = get_executed_flow_count(
            flow_list=[instance], from_=from_, to=to, flow_status=Status.success
        )
        context["executed_flows_failed"] = get_executed_flow_count(
            flow_list=[instance], from_=from_, to=to, flow_status=Status.failed
        )
        context["node_call_count_success"] = get_node_calls_count(
            flow_list=[instance],
            from_=from_,
            to=to,
            node_status=Status.success,
        )
        context["node_call_count_failed"] = get_node_calls_count(
            flow_list=[instance],
            from_=from_,
            to=to,
            node_status=Status.failed,
        )
        context["request_count"] = get_request_count(
            flow_list=[instance], from_=from_, to=to
        )
        return render_to_string(
            template_name="flows/admin/statistics.html", context=context
        )


@admin.register(Node)
class NodeAdmin(JSONFieldAdmin):
    change_form_template = "flows/admin/node_change_form.html"

    list_display = [
        "__str__",
        "is_root",
        "ancestor_count",
        "descendant_count",
    ]

    readonly_fields = ("uuid",)

    @admin.display(description=_("Is Root?"))
    def is_root(self, instance: Node):
        return instance.is_root

    @admin.display(description=_("Ancestor Count"))
    def ancestor_count(self, instance: Node):
        return instance.ancestors.count()

    @admin.display(description=_("Descendant Count"))
    def descendant_count(self, instance: Node):
        return instance.descendants.count()


class LockStatusFilter(SimpleListFilter):
    title = _("Lock Status")
    parameter_name = "lock_status"

    def lookups(self, request, model_admin):
        return [
            ("active", _("Active")),
            ("expired", _("Expired")),
        ]

    def queryset(self, request, queryset):
        if self.value() == "active":
            return queryset.filter(timeout_at__gt=timezone.now())
        elif self.value() == "expired":
            return queryset.filter(timeout_at__lte=timezone.now())
        return queryset


class LockOrganizationFilter(SimpleListFilter):
    title = _("Organization")
    parameter_name = "organization"

    def lookups(self, request, model_admin):
        # Get a list of organizations to filter by
        organizations = set(
            [lock.flow.project.organization for lock in model_admin.model.objects.select_related(
                'flow__project__organization'
            ).all()]
        )
        return [(org.id, org.name) for org in organizations]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(flow__project__organization__id=self.value())
        return queryset


@admin.register(FlowExecutionLockRecord)
class FlowExecutionLockRecordAdmin(admin.ModelAdmin):
    list_display = (
        "flow_name",
        "lock_status_display",
        "locked_at",
        "time_remaining_display",
        "locked_by_task_id",
        "mongo_flow_id",
    )
    list_display_links = ("flow_name",)
    list_filter = (LockStatusFilter, LockOrganizationFilter, "locked_at")
    search_fields = ("flow__name", "locked_by_task_id", "mongo_flow_id")
    ordering = ("-locked_at",)  # Show newest locks first
    list_per_page = 50

    # Add help text
    change_list_template = None  # Use default template

    def changelist_view(self, request, extra_context=None):
        """Add extra context to the changelist view."""
        extra_context = extra_context or {}
        extra_context['title'] = 'Flow Execution Lock Records'
        extra_context['subtitle'] = 'Monitor and manage flow execution locks. Use admin actions to clear expired locks.'
        return super().changelist_view(request, extra_context)

    readonly_fields = (
        "uuid",
        "flow",
        "mongo_flow_id",
        "locked_at",
        "locked_by_task_id",
        "redis_lock_key",
        "timeout_at",
        "lock_status_display",
        "time_remaining_display",
        "lock_duration_display",
    )

    fieldsets = (
        (
            _("Lock Information"),
            {
                "fields": (
                    "flow",
                    "lock_status_display",
                    "mongo_flow_id",
                    "redis_lock_key",
                ),
            },
        ),
        (
            _("Timing"),
            {
                "fields": (
                    "locked_at",
                    "timeout_at",
                    "time_remaining_display",
                    "lock_duration_display",
                ),
            },
        ),
        (
            _("Task Information"),
            {
                "fields": (
                    "locked_by_task_id",
                ),
            },
        ),
    )

    def has_add_permission(self, request):
        """Prevent manual creation of lock records."""
        return False

    def has_change_permission(self, request, obj=None):
        """Allow viewing lock records but prevent actual changes via readonly fields and save override."""
        return True

    def has_view_permission(self, request, obj=None):
        """Allow viewing lock records."""
        return True

    def has_delete_permission(self, request, obj=None):
        """Allow deletion of lock records for debugging purposes."""
        return True

    def delete_model(self, request, obj):
        """Override delete to also clear Redis lock when deleting from admin."""
        try:
            from django_redis import get_redis_connection
            redis_client = get_redis_connection("default")

            # Clear Redis lock if it exists
            if redis_client.exists(obj.redis_lock_key):
                redis_client.delete(obj.redis_lock_key)

            # Delete the database record
            super().delete_model(request, obj)

            self.message_user(
                request,
                f"Successfully deleted lock for {obj.flow.name} and cleared Redis lock.",
                level="SUCCESS"
            )
        except Exception as e:
            self.message_user(
                request,
                f"Error deleting lock: {str(e)}",
                level="ERROR"
            )

    def delete_queryset(self, request, queryset):
        """Override bulk delete to also clear Redis locks."""
        try:
            from django_redis import get_redis_connection
            redis_client = get_redis_connection("default")

            deleted_count = 0
            for obj in queryset:
                # Clear Redis lock if it exists
                if redis_client.exists(obj.redis_lock_key):
                    redis_client.delete(obj.redis_lock_key)
                deleted_count += 1

            # Delete the database records
            super().delete_queryset(request, queryset)

            self.message_user(
                request,
                f"Successfully deleted {deleted_count} locks and cleared Redis locks.",
                level="SUCCESS"
            )
        except Exception as e:
            self.message_user(
                request,
                f"Error deleting locks: {str(e)}",
                level="ERROR"
            )

    def save_model(self, request, obj, form, change):
        """Override save to prevent any changes to lock records."""
        if change:
            # Don't save changes to existing lock records
            pass
        else:
            # This shouldn't happen since has_add_permission returns False
            pass

    def get_readonly_fields(self, request, obj=None):
        """Make all fields readonly to prevent editing."""
        if obj:  # Editing an existing object
            return [field.name for field in self.model._meta.fields] + [
                "lock_status_display",
                "time_remaining_display",
                "lock_duration_display",
            ]
        return self.readonly_fields

    @admin.display(description=_("Flow"))
    def flow_name(self, instance):
        return instance.flow.name

    @admin.display(description=_("Status"))
    def lock_status_display(self, instance):
        if instance.is_expired:
            return format_html(
                '<span style="color: red; font-weight: bold;">🔴 {}</span>',
                _("Expired")
            )
        else:
            return format_html(
                '<span style="color: green; font-weight: bold;">🟢 {}</span>',
                _("Active")
            )

    @admin.display(description=_("Time Remaining"))
    def time_remaining_display(self, instance):
        if instance.is_expired:
            return format_html('<span style="color: red;">-</span>')

        remaining = instance.time_remaining
        total_seconds = int(remaining.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60

        if hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"

    @admin.display(description=_("Lock Duration"))
    def lock_duration_display(self, instance):
        duration = instance.timeout_at - instance.locked_at
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60

        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"

    actions = ["clear_expired_locks", "force_clear_selected_locks"]

    @admin.action(description=_("Clear expired locks"))
    def clear_expired_locks(self, request, queryset):
        """Clear all expired locks from the system."""
        try:
            from flows.utils import clear_stale_flow_locks
            cleared_count = clear_stale_flow_locks()
            self.message_user(
                request,
                _(f"Successfully cleared {cleared_count} expired locks.")
            )
        except Exception as e:
            self.message_user(
                request,
                _(f"Error clearing expired locks: {str(e)}"),
                level="ERROR"
            )

    @admin.action(description=_("Force clear selected locks (DANGEROUS)"))
    def force_clear_selected_locks(self, request, queryset):
        """Force clear selected locks. Use with caution as this may interrupt running flows."""
        try:
            from django_redis import get_redis_connection
            redis_client = get_redis_connection("default")

            cleared_count = 0
            for lock_record in queryset:
                # Clear Redis lock
                if redis_client.exists(lock_record.redis_lock_key):
                    redis_client.delete(lock_record.redis_lock_key)

                # Delete database record
                lock_record.delete()
                cleared_count += 1

            self.message_user(
                request,
                _(f"Force cleared {cleared_count} locks. Warning: This may have interrupted running flows."),
                level="WARNING"
            )
        except Exception as e:
            self.message_user(
                request,
                _(f"Error force clearing locks: {str(e)}"),
                level="ERROR"
            )

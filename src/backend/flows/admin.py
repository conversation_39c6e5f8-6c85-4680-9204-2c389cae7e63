import datetime

from admin_extra_buttons.decorators import button
from admin_extra_buttons.mixins import ExtraButtonsMixin
from django import forms
from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget

from core.admin import JSONFieldAdmin
from flows.models import Flow, Node
from flows.views import get_context_logs_view
from mongo.analytics import (
    get_executed_flow_count,
    get_node_calls_count,
    get_request_count,
)
from mongo.operations import get_flow_store_data, store_flow_data
from mongo.types import Status


class FlowAdminForm(forms.ModelForm):
    data_store = forms.JSONField(
        label=_("Data Store"),
        required=False,
        widget=JSONEditorWidget(
            attrs={
                "style": "height:400px;width:100%;position:relative;z-index:1;display:flex;"
            }
        ),
        help_text="",
    )

    class Meta:
        model = Flow
        fields = "__all__"


class OrganizationFilter(SimpleListFilter):
    title = _("Organization")
    parameter_name = "organization"

    def lookups(self, request, model_admin):
        # Get a list of organizations to filter by
        organizations = set(
            [flow.project.organization for flow in model_admin.model.objects.all()]
        )
        return [(org.id, org.name) for org in organizations]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(project__organization__id=self.value())
        return queryset


@admin.register(Flow)
class FlowAdmin(ExtraButtonsMixin, admin.ModelAdmin):
    change_form_template = "flows/admin/flow_change_form.html"

    list_display = (
        "enabled",
        "name",
        "project",
        "schedule",
        "created_at",
        "updated_at",
    )
    list_display_links = ("name",)
    list_filter = (OrganizationFilter, "project", "enabled")

    search_fields = ("name",)

    readonly_fields = ("uuid", "statistics", "periodic_task")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "name",
                    "project",
                    "periodic_task",
                    "enabled",
                    "data_store",
                    "statistics",
                ),
            },
        ),
    )

    form = FlowAdminForm

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, kwargs)
        if obj:
            default_data = get_flow_store_data(obj, json_serializable=True)
            if default_data is not None:
                default_data.pop("uuid", None)
                default_data.pop("_id", None)
            form.base_fields["data_store"].initial = default_data
        else:
            form.base_fields["data_store"].initial = {}
        return form

    def save_model(self, request, obj, form, change):
        data_store = form.cleaned_data.pop("data_store", None)
        if data_store is not None:
            data_store.pop("uuid", None)
            data_store.pop("_id", None)
            store_flow_data(obj, data_store)

        super().save_model(request, obj, form, change)

    @button(
        change_form=True,
        html_attrs={"style": "background-color:#47BAC1;color:white"},
        label=_("Execute"),
    )
    def execute(self, request, pk):
        obj = self.get_object(request, pk)
        try:
            obj.execute()
            self.message_user(request, _(f"Executing flow {obj.name} ({obj.uuid})"))
        except Exception:
            self.message_error_to_user(
                request, _(f"Flow {obj.name} ({obj.uuid}) failed")
            )

    def change_view(self, request, object_id, form_url="", extra_context=None):
        return super().change_view(
            request,
            object_id,
            form_url,
            extra_context=get_context_logs_view(object_id),
        )

    @admin.display(description=_("Schedule"))
    def schedule(self, instance):
        return instance.periodic_task.scheduler

    @admin.display(description="In the last 30 days")
    def statistics(self, instance):
        context = dict()
        to = timezone.now()
        from_ = to - datetime.timedelta(days=30)
        context["executed_flows_success"] = get_executed_flow_count(
            flow_list=[instance], from_=from_, to=to, flow_status=Status.success
        )
        context["executed_flows_failed"] = get_executed_flow_count(
            flow_list=[instance], from_=from_, to=to, flow_status=Status.failed
        )
        context["node_call_count_success"] = get_node_calls_count(
            flow_list=[instance],
            from_=from_,
            to=to,
            node_status=Status.success,
        )
        context["node_call_count_failed"] = get_node_calls_count(
            flow_list=[instance],
            from_=from_,
            to=to,
            node_status=Status.failed,
        )
        context["request_count"] = get_request_count(
            flow_list=[instance], from_=from_, to=to
        )
        return render_to_string(
            template_name="flows/admin/statistics.html", context=context
        )


@admin.register(Node)
class NodeAdmin(JSONFieldAdmin):
    change_form_template = "flows/admin/node_change_form.html"

    list_display = [
        "__str__",
        "is_root",
        "ancestor_count",
        "descendant_count",
    ]

    readonly_fields = ("uuid",)

    @admin.display(description=_("Is Root?"))
    def is_root(self, instance: Node):
        return instance.is_root

    @admin.display(description=_("Ancestor Count"))
    def ancestor_count(self, instance: Node):
        return instance.ancestors.count()

    @admin.display(description=_("Descendant Count"))
    def descendant_count(self, instance: Node):
        return instance.descendants.count()

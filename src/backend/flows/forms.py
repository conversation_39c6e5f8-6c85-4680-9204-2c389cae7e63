import django.forms as forms

from flows.models import Node, NodeRelation


class NodeAppendInlineForm(forms.ModelForm):
    depth = forms.IntegerField(initial=1, widget=forms.HiddenInput())

    class Meta:
        model = NodeRelation
        fields = ["descendant", "depth"]

    def __init__(self, *args, node, **kwargs):
        super().__init__(*args, **kwargs)

        if "descendant" in self.initial:
            qs = Node.objects.filter(pk=self.initial["descendant"])
        else:
            qs = Node.objects.roots().exclude(pk=node.pk)

        self.fields["descendant"].queryset = qs


class NodeRelationInlineFormSet(forms.BaseInlineFormSet):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        instance = kwargs.get("instance")
        self.form_kwargs.update({"node": instance})

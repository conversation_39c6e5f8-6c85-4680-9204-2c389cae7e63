from django.db.models import Manager, QuerySet, Count, Q

import flows.models


class NodeQuerySet(QuerySet):
    def roots(self):
        """
        Returns a `NodeQuerySet` of all `Node` within that `NodeQuerySet` without a parent.
        That means that the only parent the `Node` has is itself.
        """

        annotated = self.annotate(ancestors_count=Count("ancestor_relations"))
        return annotated.filter(ancestors_count=1)


class NodeManager(Manager):
    def get_queryset(self) -> NodeQuerySet:
        return NodeQuerySet(self.model, using=self._db)

    def roots(self):
        """
        Returns a `NodeQuerySet` of all `Node` within that `NodeQuerySet` without a parent.
        That means that the only parent the `Node` has is itself.
        """

        return self.get_queryset().roots()


class NodeRelationQuerySet(QuerySet):
    def ancestors(self, include_self: bool = False) -> NodeQuerySet:
        """
        Returns a `NodeQuerySet` of all ancestor objects in the referenced `NodeRelationQuerySet`.

        Args:
            include_self (bool, optional): Whether to include self in the results. Defaults to False.
        """

        filtered_relations = self.filter(~Q(depth=0)) if not include_self else self
        return flows.models.Node.objects.filter(
            pk__in=filtered_relations.values("ancestor_id")
        )

    def descendants(self, include_self: bool = False) -> NodeQuerySet:
        """
        Returns a `NodeQuerySet` of all descendant objects in the referenced `NodeRelationQuerySet`.

        Args:
            include_self (bool, optional): Whether to include self in the results. Defaults to False.
        """

        filtered_relations = self.filter(~Q(depth=0)) if not include_self else self
        return flows.models.Node.objects.filter(
            pk__in=filtered_relations.values("descendant_id")
        )


class NodeRelationManager(Manager):
    def get_queryset(self) -> NodeRelationQuerySet:
        return NodeRelationQuerySet(self.model, using=self._db)

    def ancestors(self, include_self: bool = False) -> NodeQuerySet:
        """
        Returns a `NodeQuerySet` of all ancestor objects in the referenced `NodeRelationQuerySet`.

        Args:
            include_self (bool, optional): Whether to include self in the results. Defaults to False.
        """

        return self.get_queryset().ancestors(include_self=include_self)

    def descendants(self, include_self: bool = False) -> NodeQuerySet:
        """
        Returns a `NodeQuerySet` of all descendant objects in the referenced `NodeRelationQuerySet`.

        Args:
            include_self (bool, optional): Whether to include self in the results. Defaults to False.
        """

        return self.get_queryset().descendants(include_self=include_self)

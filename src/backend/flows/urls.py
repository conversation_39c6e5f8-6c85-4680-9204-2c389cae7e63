from django.urls import path

from flows.views import (
    create,
    create_and_append,
    delete,
    flow_short_logs_view,
    flow_log_node_calls,
    flow_log_node_call_data
)

app_name = "flows"

urlpatterns = [
    path("node/create/<uuid:uuid>/", create, name="create-node"),
    path(
        "node/create-and-append/<uuid:uuid>/",
        create_and_append,
        name="create-and-append-node",
    ),
    path("node/remove/<uuid:uuid>/", delete, name="delete-node"),
    path("logs/", flow_short_logs_view, name="flow-logs-view"),
    path("log/node-calls/", flow_log_node_calls, name="flow-log-node-calls"),
    path(
        "log/node-call/data/",
        flow_log_node_call_data,
        name="flow-log-node-call-data"
    ),
]

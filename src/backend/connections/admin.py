import json
import logging

from django.contrib import admin
from django.db.models import J<PERSON><PERSON><PERSON>
from django_json_widget.widgets import JSONEditorWidget

from connections.models import Connection
from core.admin import J<PERSON><PERSON>ieldAdmin

logger = logging.getLogger("symmy")

REDACTED_VALUE = 8 * "*"


def redact_sensitive_values(json_str: str) -> str:
    """
    Redacts sensitive values from a JSON string.
    Args:
        json_str: The JSON string to redact.

    Returns: The redacted JSON string.
    """

    value_dict = json.loads(json_str)
    for key in value_dict:
        value_dict[key] = REDACTED_VALUE

    return json.dumps(value_dict)


def remove_items_containing_string(input_dict: dict, match: str) -> dict:
    """
    Removes all items from a dict whose value equals the substring.
    Args:
        input_dict: The dict to remove items from.
        match: The substring to check for.

    Returns: The dict with the items removed.
    """
    return {key: value for key, value in input_dict.items() if match != value}


class RedactedJSONWidget(JSONEditorWidget):
    """
    A JSONEditorWidget that redacts sensitive values.
    """

    def render(self, name, value, attrs=None, renderer=None):
        redacted_value = redact_sensitive_values(value)

        return super().render(name, redacted_value, attrs, renderer)


@admin.register(Connection)
class ConnectionAdmin(JSONFieldAdmin):
    list_display = ("name", "nodetype", "organization")

    formfield_overrides = {
        JSONField: {"widget": RedactedJSONWidget},
    }

    def save_model(self, request, obj, form, change):
        # Make sure we don't save the redacted values
        existing_obj = Connection.objects.get(pk=obj.pk) if change else None
        if existing_obj:
            obj_data = existing_obj.data
            new_data = remove_items_containing_string(
                form.cleaned_data["data"], REDACTED_VALUE
            )
            obj_data.update(new_data)
            obj.data = obj_data
        super().save_model(request, obj, form, change)

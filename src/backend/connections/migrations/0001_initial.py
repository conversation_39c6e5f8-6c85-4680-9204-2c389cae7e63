# Generated by Django 4.2.2 on 2023-12-19 09:20

from django.db import migrations, models
import django.db.models.deletion
import django_cryptography.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('nodetypes', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Connection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=50, verbose_name='Connection name')),
                ('data', django_cryptography.fields.encrypt(models.J<PERSON><PERSON>ield(blank=True, default=dict, verbose_name='Data'))),
                ('nodetype', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='connection', to='nodetypes.nodetyperesource', verbose_name='Node Type')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]

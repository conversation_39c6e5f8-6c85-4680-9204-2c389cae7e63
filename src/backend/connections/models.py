import logging

from django.db import models
from django.utils.translation import gettext_lazy as _
from django_cryptography.fields import encrypt

from core.models import ExtendedBaseModel
from nodetypes.exceptions import NodeTypeIsNotAvailable
from nodetypes.models import NodeTypeResource

logger = logging.getLogger("symmy")


class Connection(ExtendedBaseModel):
    name = models.CharField(_("Connection name"), max_length=50)
    data = encrypt(models.J<PERSON>NField(verbose_name=_("Data"), default=dict, blank=True))
    nodetype = models.ForeignKey(
        NodeTypeResource,
        related_name="connections",
        verbose_name=_("Node Type"),
        on_delete=models.CASCADE,
    )

    # TODO: Visibility/Permission levels

    def get_connection_class(self):
        if self.nodetype.is_loaded:
            return self.nodetype.nodetype_class.Connection

        raise NodeTypeIsNotAvailable()

    def authenticate(self):
        """
        Verifies if the connection specifications are valid by authenticating with the service.

        Returns:
            Connection: Initialized Node.Connection object ready to be used in Flows/Actions.
        """

        connection_class = self.get_connection_class()
        connection = connection_class(data=self.data)
        connection.is_valid(raise_exception=True)
        connection.authenticate()

        return connection

    def __str__(self) -> str:
        return self.name

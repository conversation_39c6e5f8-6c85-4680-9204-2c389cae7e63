from rest_framework.generics import (
    CreateAPIView,
    DestroyAPIView,
    ListAPIView,
    RetrieveAPIView,
)
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import (
    extend_schema_view,
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
)
from drf_spectacular.types import OpenApiTypes

from connections.api.v1.serializers import (
    BasicConnectionSerializer,
    # CreateConnectionSerializer,
)
from connections.mixins import FilterByUserConnectionsMixin


@extend_schema_view(
    get=extend_schema(
        operation_id="Get basic information for a single Connection",
        summary="Returns Connection, related User, Organization and the amount of Nodes using it.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                location=OpenApiParameter.PATH,
                description="Connection UUID Identifier",
            )
        ],
        responses={
            200: BasicConnectionSerializer,
            404: OpenApiResponse(description="Connection not found"),
        },
    )
)
class ConnectionDescriptionView(FilterByUserConnectionsMixin, RetrieveAPIView):
    permission_classes = [
        IsAuthenticated,
    ]
    lookup_field = "uuid"
    serializer_class = BasicConnectionSerializer


@extend_schema_view(
    delete=extend_schema(
        operation_id="Delete Connection",
        description="Deletes a Connection.",
        responses={
            204: OpenApiResponse(description="Connection deleted successfully"),
            404: OpenApiResponse(description="Connection not found"),
        },
    )
)
class ConnectionDeleteView(FilterByUserConnectionsMixin, DestroyAPIView):
    permission_classes = [IsAuthenticated]
    lookup_field = "uuid"


@extend_schema_view(
    get=extend_schema(
        operation_id="Get basic information about all Connections",
        summary="Returns a list of Connections, related Users, "
        "Organizations and the amount of Nodes using each Connection.",
    )
)
class ConnectionListView(FilterByUserConnectionsMixin, ListAPIView):
    serializer_class = BasicConnectionSerializer


# @extend_schema_view(
#     post=extend_schema(
#         operation_id="Add Connection for a NodeType",
#         summary="Creates a new Connection for a specific NodeType within an Organization",
#         request=CreateConnectionSerializer,
#         responses={
#             201: CreateConnectionSerializer,
#             400: OpenApiResponse(description="Invalid data"),
#             403: OpenApiResponse(description="Permission denied"),
#             404: OpenApiResponse(description="NodeType or Organization not found"),
#         },
#     )
# )
# class ConnectionCreateView(CreateAPIView):
#     permission_classes = [
#         IsAuthenticated,
#     ]
#     serializer_class = CreateConnectionSerializer

#     def perform_create(self, serializer):
#         serializer.save(user=self.request.user)


# @extend_schema_view(
#     get=extend_schema(
#         operation_id="List Connections for a NodeType",
#         summary="Returns a list of Connections for a specific NodeType within an Organization",
#         parameters=[
#             OpenApiParameter(
#                 name="organization_uuid",
#                 type=OpenApiTypes.UUID,
#                 location=OpenApiParameter.PATH,
#                 description="Organization UUID Identifier",
#             ),
#             OpenApiParameter(
#                 name="nodetype_uuid",
#                 type=OpenApiTypes.UUID,
#                 location=OpenApiParameter.PATH,
#                 description="NodeType UUID Identifier",
#             ),
#         ],
#         responses={
#             200: BasicConnectionSerializer(many=True),
#             404: OpenApiResponse(description="NodeType or Organization not found"),
#         },
#     )
# )
# class NodetypeConnectionListView(ListAPIView):
#     permission_classes = [
#         IsAuthenticated,
#     ]
#     serializer_class = BasicConnectionSerializer

#     def get_queryset(self):
#         nodetype_uuid = self.kwargs["nodetype_uuid"]
#         organization_uuid = self.kwargs["organization_uuid"]

#         return Connection.objects.filter(
#             nodetype__uuid=nodetype_uuid,
#             organization__uuid=organization_uuid,
#         )

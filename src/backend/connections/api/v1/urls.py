from django.urls import path
from connections.api.v1.views import (
    ConnectionDescriptionView,
    ConnectionDeleteView,
    ConnectionListView,
    # ConnectionCreateView,
    # NodetypeConnectionListView,
)

app_name = "connections"

urlpatterns = [
    path(
        "retrieve/<uuid:uuid>/",
        ConnectionDescriptionView.as_view(),
        name="connection-description",
    ),
    path(
        "delete/<uuid:uuid>/",
        ConnectionDeleteView.as_view(),
        name="connection-delete",
    ),
    path("list/", ConnectionListView.as_view(), name="connection-list"),
    # path(
    #     "create/",
    #     ConnectionCreateView.as_view(),
    #     name="connection-create",
    # ),
    # path(
    #     "nodetype/<uuid:nodetype_uuid>/organization/<uuid:organization_uuid>/list/",
    #     NodetypeConnectionListView.as_view(),
    #     name="nodetype-connection-list",
    # ),
]

from rest_framework.exceptions import PermissionDenied, NotFound
from rest_framework.serializers import (
    ModelSerializer,
    Serializer<PERSON>ethod<PERSON>ield,
    UUIDField,
)

from connections.models import Connection

from core.models import Organization, OrganizationUser

from nodetypes.models import NodeTypeResource

from users.api.v1.serializers import UserDataSerializer


class BasicConnectionSerializer(ModelSerializer):
    user = UserDataSerializer()
    node_count = SerializerMethodField()

    class Meta:
        model = Connection
        fields = [
            "uuid",
            "name",
            "created_at",
            "updated_at",
            "user",
            "node_count",
        ]

    def get_node_count(self, obj):
        return obj.node_set.count()


# class CreateConnectionSerializer(ModelSerializer):
#     nodetype = UUIDField()
#     organization = UUIDField()

#     class Meta:
#         model = Connection
#         fields = ["name", "data", "nodetype", "organization"]

#     def validate_data(self, value):
#         # No validation for 'data', allow any JSON (for now)
#         return value

#     def validate(self, data):
#         user = self.context["request"].user

#         try:
#             organization = Organization.objects.get(uuid=data["organization"])
#         except Organization.DoesNotExist:
#             raise NotFound("Organization not found")

#         # Check if the user has access to the organization
#         if not user.is_superuser:
#             try:
#                 OrganizationUser.objects.get(user=user, organization=organization)
#             except OrganizationUser.DoesNotExist:
#                 raise PermissionDenied("User doesn't have access to the organization")

#         try:
#             nodetype = NodeTypeResource.objects.get(uuid=data["nodetype"])
#         except NodeTypeResource.DoesNotExist:
#             raise NotFound("NodeTypeResource not found")

#         # Replace UUID fields with actual model instances
#         data["organization"] = organization
#         data["nodetype"] = nodetype

#         return data

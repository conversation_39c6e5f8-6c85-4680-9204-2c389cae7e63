from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIRequestFactory, force_authenticate

from symmy.test_utils.model_mixins import SimpleFlowData, fake
from connections.models import Connection
from connections.api.v1.views import ConnectionDescriptionView, ConnectionListView

from users.models import SymmyUser


class TestConnectionViews(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.factory = APIRequestFactory()
        cls.admin = SymmyUser.objects.create_superuser(
            email=fake.ascii_email(), password=fake.password()
        )
        cls.simple_flow_data = SimpleFlowData()
        cls.connections = [
            cls.simple_flow_data.conn,
            Connection.objects.create(
                name=fake.word(),
                data={},
                nodetype=cls.simple_flow_data.nodetype,
                organization=cls.simple_flow_data.org,
                user=cls.simple_flow_data.usr,
            ),
        ]

    def test_connection_description_view(self):
        uuid = str(self.connections[0].uuid)
        request = self.factory.get(
            reverse(
                "api:v1:connections:connection-description",
                kwargs={"uuid": uuid},
            )
        )
        force_authenticate(request, user=self.admin)
        response = ConnectionDescriptionView.as_view()(request, uuid=uuid)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 6)

        self.assertIn("uuid", response.data)
        self.assertIn("name", response.data)
        self.assertIn("created_at", response.data)
        self.assertIn("updated_at", response.data)
        self.assertIn("user", response.data)
        self.assertIn("node_count", response.data)

        self.assertEqual(response.data["node_count"], self.simple_flow_data.node_count)

    def test_connection_list_view(self):
        request = self.factory.get(reverse("api:v1:connections:connection-list"))
        force_authenticate(request, user=self.admin)
        response = ConnectionListView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)

        for conn in response.data:
            self.assertIn("uuid", conn)
            self.assertIn("name", conn)
            self.assertIn("created_at", conn)
            self.assertIn("updated_at", conn)
            self.assertIn("user", conn)
            self.assertIn("node_count", conn)

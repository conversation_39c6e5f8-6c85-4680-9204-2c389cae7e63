from django.conf import settings

from connections.models import Connection

from symmy.mixins import FilterByUserMixin


class FilterByUserConnectionsMixin(FilterByUserMixin):
    model = Connection

    @staticmethod
    def filter_queryset_regular_user(queryset, user):
        return queryset.filter(organization__organizationuser__user=user)

    @staticmethod
    def filter_queryset_superuser(queryset):
        return queryset.exclude(organization__name=settings.DEFAULT_ORGANIZATION_NAME)

# TODO: cache querysets
import datetime

from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models.query import QuerySet
from drf_spectacular.utils import extend_schema_view

from core.models import Project, Organization
from core.mixins import FilterByUserProjectsMixin, FilterByUserOrganizationsMixin
from flows.models import Flow
from flows.mixins import FilterByUserFlowsMixin
from analytics.api.utils import get_new_offset_urls, create_response
from mongo.analytics import (
    get_flow_logs,
    get_node_calls,
    get_executed_flow_count,
    get_node_calls_count,
    get_request_count,
    flow_operation,
    project_operation,
    organization_operation,
)
from mongo.utils import get_flow_uuids_corresponding_to_document_ids
from mongo.types import FlowLogField, Status
from .swagger_docs import (
    SWAGGER_DOCS_ORGANIZATION,
    SWAGGER_DOCS_PROJECT,
    SWAGGER_DOCS_FLOW,
)


class BaseAnalyticsViewMixin:
    permission_classes = [
        IsAuthenticated,
    ]

    def retrieve_query_params(self) -> dict:
        query_params = {}

        for key, value in {
            "mongo_flow_ids": self.request.query_params.getlist("mongo_flow_ids"),
            "flow_status": self.request.query_params.getlist("flow_status"),
            "node_status": self.request.query_params.getlist("node_status"),
            "from_": self.request.query_params.get("date_from"),
            "to": self.request.query_params.get("date_to"),
            "name_as_key": self.request.query_params.get("name_as_key"),
            "bulk_result": self.request.query_params.get("bulk_result"),
            "limit": self.request.query_params.get("limit"),
            "offset": self.request.query_params.get("offset"),
            "exclude_nodes": self.request.query_params.get("exclude_nodes"),
        }.items():
            if value is not None and value != []:
                query_params[key] = value

        return query_params

    @staticmethod
    def _prep_status(status: None | str | list[str]) -> Status | list[Status] | None:
        if status:
            if isinstance(status, list):
                return [getattr(Status, s) for s in status]
            return getattr(Status, status)

        return None

    @staticmethod
    def _prep_date(date: None | str) -> datetime.date | None:
        if date:
            try:
                return datetime.datetime.strptime(date, "%Y-%m-%dT%H:%M:%S")
            except ValueError:
                return datetime.datetime.strptime(date, "%Y-%m-%dT%H:%M:00")

        return None

    def get_query_params(self) -> dict:
        query_params = self.retrieve_query_params()
        if query_params.get("flow_status", None) is not None:
            query_params["flow_status"] = self._prep_status(query_params["flow_status"])
        if query_params.get("node_status", None) is not None:
            query_params["node_status"] = self._prep_status(query_params["node_status"])
        if query_params.get("from_", None) is not None:
            query_params["from_"] = self._prep_date(query_params["from_"])
        if query_params.get("to", None) is not None:
            query_params["to"] = self._prep_date(query_params["to"])
        if query_params.get("name_as_key", None) is not None:
            query_params["name_as_key"] = bool(query_params["name_as_key"])
        else:
            query_params["str_uuid"] = True
        if query_params.get("bulk_result") is not None:
            query_params["bulk_result"] = bool(query_params["bulk_result"])
        if query_params.get("offset") is not None:
            query_params["offset"] = int(query_params["offset"])
        if query_params.get("limit") is not None:
            query_params["limit"] = int(query_params["limit"])
        if query_params.get("exclude_nodes") is not None:
            query_params.pop("exclude_nodes")
            query_params.setdefault("exclude_fields", []).append(
                FlowLogField.node_calls
            )
        return query_params

    @create_response  # handles exceptions and returns the response
    def execute_operation(self, request, operation: callable, **kwargs):
        queryset: QuerySet = self.get_queryset()
        model_operation: callable
        match queryset.model():
            case Flow():
                model_operation = flow_operation
                if kwargs.get("mongo_flow_ids", None) is not None:
                    kwargs["mongo_flow_ids"] = (
                        FlowViewSet.remove_unavailable_mongo_flow_ids(
                            queryset, kwargs["mongo_flow_ids"]
                        )
                    )
            case Project():
                model_operation = project_operation
            case Organization():
                model_operation = organization_operation
        res = model_operation(
            operation=operation,
            queryset=queryset,
            **kwargs,
        )
        res.update(get_new_offset_urls(request, **kwargs))
        return res


class FlowCountMixin(BaseAnalyticsViewMixin):
    @action(detail=False, methods=["GET"], url_path="flow-count")
    def flow_count(self, request):
        kwargs = {
            **self.get_query_params(),
        }
        return self.execute_operation(request, get_executed_flow_count, **kwargs)


class NodeCallsCountMixin(BaseAnalyticsViewMixin):
    @action(detail=False, methods=["GET"], url_path="node-calls-count")
    def node_calls_count(self, request):
        kwargs = {
            **self.get_query_params(),
        }
        return self.execute_operation(request, get_node_calls_count, **kwargs)


class RequestCountMixin(BaseAnalyticsViewMixin):
    @action(detail=False, methods=["GET"], url_path="request-count")
    def request_count(self, request):
        kwargs = {
            **self.get_query_params(),
        }
        return self.execute_operation(request, get_request_count, **kwargs)


class LogsMixin(BaseAnalyticsViewMixin):
    @action(detail=False, methods=["GET"], url_path="logs")
    def logs(self, request):
        kwargs = {
            "json_serializable": True,
            **self.get_query_params(),
        }
        return self.execute_operation(request, get_flow_logs, **kwargs)


class NodeCallsMixin(BaseAnalyticsViewMixin):
    @action(detail=False, methods=["GET"], url_path="node-calls")
    def node_calls(self, request):
        kwargs = {
            "json_serializable": True,
            **self.get_query_params(),
        }
        return self.execute_operation(request, get_node_calls, **kwargs)


class AllActionsMixin(
    FlowCountMixin,
    NodeCallsCountMixin,
    RequestCountMixin,
    LogsMixin,
    NodeCallsMixin,
):
    pass


@extend_schema_view(**SWAGGER_DOCS_ORGANIZATION)
class OrganizationViewSet(
    AllActionsMixin,
    FilterByUserOrganizationsMixin,
    ViewSet,
):
    @staticmethod
    def filter_organization_uuids(queryset: QuerySet, request):
        if request.query_params:
            organization_uuids = request.query_params.getlist("organization_uuids")
            return queryset.filter(uuid__in=organization_uuids)
        return queryset

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_organization_uuids(queryset, self.request)


@extend_schema_view(**SWAGGER_DOCS_PROJECT)
class ProjectViewSet(
    AllActionsMixin,
    FilterByUserProjectsMixin,
    ViewSet,
):
    @staticmethod
    def filter_project_uuids(queryset: QuerySet, request):
        if request.query_params:
            project_uuids = request.query_params.getlist("project_uuids")
            if project_uuids:
                return queryset.filter(uuid__in=project_uuids)
        return queryset

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_project_uuids(queryset, self.request)


@extend_schema_view(**SWAGGER_DOCS_FLOW)
class FlowViewSet(
    AllActionsMixin,
    FilterByUserFlowsMixin,
    ViewSet,
):
    @staticmethod
    def filter_flow_uuids(queryset: QuerySet, request):
        if request.query_params:
            flow_uuids = request.query_params.getlist("flow_uuids")
            if flow_uuids:
                return queryset.filter(uuid__in=flow_uuids)
        return queryset

    @staticmethod
    def remove_unavailable_mongo_flow_ids(
        queryset: QuerySet, mongo_flow_ids: list[str]
    ):
        available_uuids = queryset.values_list("uuid", flat=True)
        _id_uuid_pairs = get_flow_uuids_corresponding_to_document_ids(mongo_flow_ids)
        valid_mongo_flow_ids = []
        for _id_uuid in _id_uuid_pairs:
            if _id_uuid["uuid"] in available_uuids:
                valid_mongo_flow_ids.append(_id_uuid["_id"])

        return valid_mongo_flow_ids

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_flow_uuids(queryset, self.request)

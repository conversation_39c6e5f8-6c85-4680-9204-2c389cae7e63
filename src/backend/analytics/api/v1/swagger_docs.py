from drf_spectacular.utils import (
    extend_schema,
    # extend_schema_view,
    OpenApiParameter,
    OpenApiResponse,
    OpenApiExample,
)
from drf_spectacular.types import OpenApiTypes

from mongo.types import FlowLogField, NodeCallField

NODE_CALL_FIELDS = ""
for ncfield in NodeCallField:
    NODE_CALL_FIELDS += f"{ncfield.name};\n"
FLOW_LOG_FIELDS = ""
for flfield in FlowLogField:
    FLOW_LOG_FIELDS += f"{flfield.name};\n"

# required is set to False by default
FLOW_UUIDS_PARAM = OpenApiParameter(
    name="flow_uuids",
    type=OpenApiTypes.INT,
    location=OpenApiParameter.QUERY,
    many=True,
    description="List of Flow uuids",
)
PROJECT_UUIDS_PARAM = OpenApiParameter(
    name="project_uuids",
    type=OpenApiTypes.INT,
    location=OpenApiParameter.QUERY,
    many=True,
    description="List of Project uuids",
)
ORGANIZATION_UUIDS_PARAM = OpenApiParameter(
    name="organization_uuids",
    type=OpenApiTypes.INT,
    location=OpenApiParameter.QUERY,
    many=True,
    description="List of Organization uuids",
)

NODE_STATUS_PARAM = OpenApiParameter(
    name="node_status",
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    many=True,
    description="Name of a Status enum attribute(e.g. if we declared Status.success -> 'success')",
)
FLOW_STATUS_PARAM = OpenApiParameter(
    name="flow_status",
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    many=True,
    description="Name of a Status enum attribute(e.g. if we declared Status.success -> 'success')",
)
MONGO_FLOW_IDS_PARAM = OpenApiParameter(
    name="mongo_flow_ids",
    type=OpenApiTypes.STR,
    location=OpenApiParameter.QUERY,
    many=True,
    description="IDs of mongo documents in the 'flows' collection",
)
AVAILABLE_PARAMETERS = [
    OpenApiParameter(
        name="bulk_result",
        type=OpenApiTypes.BOOL,
        location=OpenApiParameter.QUERY,
        description="Return values as a single list(without binding to uuids/names)",
    ),
    OpenApiParameter(
        name="limit",
        type=OpenApiTypes.INT,
        location=OpenApiParameter.QUERY,
        description="Limit the returned values to that the specified amount",
    ),
    OpenApiParameter(
        name="offset",
        type=OpenApiTypes.BOOL,
        location=OpenApiParameter.QUERY,
        description="Offset the returned values by that amount",
    ),
    OpenApiParameter(
        name="from_",
        type=OpenApiTypes.DATETIME,
        location=OpenApiParameter.QUERY,
        description="Datetime in a form of %Y-%m-%dT%H:%M(:%S)",
    ),
    OpenApiParameter(
        name="to",
        type=OpenApiTypes.DATETIME,
        location=OpenApiParameter.QUERY,
        description="Datetime in a form of %Y-%m-%dT%H:%M(:%S)",
    ),
    OpenApiParameter(
        name="name_as_key",
        type=OpenApiTypes.BOOL,
        location=OpenApiParameter.QUERY,
        description="Whether the keys should be represented as object names",
    ),
]

PROJ_ENDPOINT_DESCRIPTION = """Each key is an ID of a project object,
 each value is the same as {endpoint} 
but applied for a list of Flows related to a project.
 If no project_ids are specified,
returns the result for all existing projects."""

ORGANIZATION_ENDPOINT_DESCRIPTION = """Each key is an ID of an organization object,
each value is the same as {endpoint}
 but applied to a list of Projects related to an Organization.
If no organization_ids are specified,
 returns the result for all existing organizations."""


SWAGGER_DOCS_ORGANIZATION = {
    "flow_count": extend_schema(
        operation_id="flow-count for Organizations",
        summary="Gets flow_count for all Flows under specified Organizations",
        description="Same as /flows/flow_count/ but for entire organizations.",
        responses={
            200: OpenApiResponse(
                description=ORGANIZATION_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/flow_count/"
                ),
                response={"org": {"proj": {"flow": "res"}}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "organization_uuid": {
                                "proj_uuid": {
                                    "flow_uuid1": "/flows/flow_count/ response"
                                }
                            }
                        },
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [ORGANIZATION_UUIDS_PARAM, FLOW_STATUS_PARAM],
    ),
    "node_calls_count": extend_schema(
        operation_id="node-calls-count for Organizations",
        summary="Gets node_calls_count for all Flows under specified Organizations",
        description="Same as /flows/node_calls_count/ but for entire organizations.",
        responses={
            200: OpenApiResponse(
                description=ORGANIZATION_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/node_calls_count/"
                ),
                response={"org": {"proj": {"flow": "res"}}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "organization_uuid": {
                                "proj_uuid": {
                                    "flow_uuid1": "/flows/node_calls_count/ response"
                                }
                            }
                        },
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [ORGANIZATION_UUIDS_PARAM, FLOW_STATUS_PARAM, NODE_STATUS_PARAM],
    ),
    "request_count": extend_schema(
        operation_id="request-count for Organizations",
        summary="Gets request_count for all Flows under specified Organizations",
        description="Same as /flows/request_count/ but for entire organizations.",
        responses={
            200: OpenApiResponse(
                description=ORGANIZATION_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/request_count/"
                ),
                response={"org": {"proj": {"flow": "res"}}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "organization_uuid": {
                                "proj_uuid": {
                                    "flow_uuid1": "/flows/request_count/ response"
                                }
                            }
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [ORGANIZATION_UUIDS_PARAM],
    ),
    "logs": extend_schema(
        operation_id="logs for Organizations",
        summary="Gets logs for all Flows under specified Organizations",
        description="Same as /flows/logs/ but for entire organizations.",
        responses={
            200: OpenApiResponse(
                description=ORGANIZATION_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/logs/"
                ),
                response={"org": {"proj": {"flow": "res"}}},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={
                            "organization_uuid": {
                                "proj_uuid": {"flow_uuid1": "/flows/logs/ response"}
                            }
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [ORGANIZATION_UUIDS_PARAM, FLOW_STATUS_PARAM],
    ),
    "node_calls": extend_schema(
        operation_id="node-calls for Organizations",
        summary="Gets node_calls for all Flows under specified Organizations",
        description="Same as /flows/node_calls/ but for entire organizations.",
        responses={
            200: OpenApiResponse(
                description=ORGANIZATION_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/node_calls/"
                ),
                response={"org": {"proj": {"flow": "res"}}},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={
                            "organization_uuid": {
                                "proj_uuid": {
                                    "flow_uuid1": "/flows/node_calls/ response"
                                }
                            }
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [
            ORGANIZATION_UUIDS_PARAM,
            FLOW_STATUS_PARAM,
            NODE_STATUS_PARAM,
        ],
    ),
}


SWAGGER_DOCS_PROJECT = {
    "flow_count": extend_schema(
        operation_id="flow-count for Projects",
        summary="Gets flow_count for all Flows under specified Projects",
        description="Same as /flows/flow_count/ but for projects.",
        responses={
            200: OpenApiResponse(
                description=PROJ_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/flow_count/"
                ),
                response={"proj": {"flow": "res"}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "proj_uuid": {"flow_uuid1": "/flows/flow_count/ response"}
                        },
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [
            PROJECT_UUIDS_PARAM,
            FLOW_STATUS_PARAM,
        ],
    ),
    "node_calls_count": extend_schema(
        operation_id="node-calls-count for Projects",
        summary="Gets node_calls_count for all Flows under specified Projects",
        description="Same as /flows/node_calls_count/ but for projects.",
        responses={
            200: OpenApiResponse(
                description=PROJ_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/node_calls_count/"
                ),
                response={"proj": {"flow": "res"}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "proj_uuid": {
                                "flow_uuid1": "/flows/node_calls_count/ response"
                            }
                        },
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [PROJECT_UUIDS_PARAM, FLOW_STATUS_PARAM, NODE_STATUS_PARAM],
    ),
    "request_count": extend_schema(
        operation_id="request-count for Projects",
        summary="Gets request_count for all Flows under specified Projects",
        description="Same as /flows/request_count/ but for projects.",
        responses={
            200: OpenApiResponse(
                description=PROJ_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/request_count/"
                ),
                response={"proj": {"flow": "res"}},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={
                            "proj_uuid": {
                                "flow_uuid1": "/flows/request_count/ response"
                            }
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [PROJECT_UUIDS_PARAM],
    ),
    "logs": extend_schema(
        operation_id="logs for Projects",
        summary="Gets logs for all Flows under specified Projects",
        description="Same as /flows/logs/ but for projects.",
        responses={
            200: OpenApiResponse(
                description=PROJ_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/logs/ response"
                ),
                response={"flow_uuid": "executed_flow_count"},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={"project_uuid": {"flow_uuid": "/flows/logs/ response"}},
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [PROJECT_UUIDS_PARAM, FLOW_STATUS_PARAM],
    ),
    "node_calls": extend_schema(
        operation_id="node-calls for Projects",
        summary="Gets node_calls for all Flows under specified Projects",
        description="Same as /flows/node_calls/ but for projects.",
        responses={
            200: OpenApiResponse(
                description=PROJ_ENDPOINT_DESCRIPTION.format(
                    endpoint="/flows/node_calls/"
                ),
                response={"flow_uuid": "executed_flow_count"},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={
                            "project_uuid": {"flow_uuid": "/flows/node_calls/ response"}
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [PROJECT_UUIDS_PARAM, FLOW_STATUS_PARAM, NODE_STATUS_PARAM],
    ),
}


SWAGGER_DOCS_FLOW = {
    "flow_count": extend_schema(
        operation_id="flow-count",
        summary="Gets flow_count statistics for specified Flows",
        description="The amount of times any given Flow instance has been executed. "
        "If no flow_ids argument is specified, returns the result for all defined Flows.",
        responses={
            200: OpenApiResponse(
                description="Each key is a UUID of a flow object, "
                "each value is the amount of times it has been executed.",
                response={"flow_uuid": "executed_flow_count"},
                examples=[
                    OpenApiExample(
                        name="Generic response",
                        value={"flow_uuid": "executed_flow_count"},
                    ),
                    OpenApiExample(
                        name="Sample response",
                        value={"50e8400-e29b-41d4-a716-************": "5"},
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [FLOW_UUIDS_PARAM, FLOW_STATUS_PARAM, MONGO_FLOW_IDS_PARAM],
    ),
    "node_calls_count": extend_schema(
        operation_id="node-calls-count",
        summary="Gets node_calls_count statistics for specified Flows",
        description="The amount of executed nodes of each Flow call. "
        "If no flow_ids argument is specified, returns the result for all defined Flows.",
        responses={
            200: OpenApiResponse(
                description="Each key is a UUID of a flow object, "
                "each value is the amount of executed nodes.",
                response={"flow_uuid": "node_calls_count"},
                examples=[
                    OpenApiExample(
                        name="Generic response", value={"flow_uuid": "node_calls_count"}
                    ),
                    OpenApiExample(
                        name="Sample response",
                        value={"90e8400-e29b-41d4-a716-************": "21"},
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [
            FLOW_UUIDS_PARAM,
            FLOW_STATUS_PARAM,
            NODE_STATUS_PARAM,
            MONGO_FLOW_IDS_PARAM,
        ],
    ),
    "request_count": extend_schema(
        operation_id="request-count",
        summary="Gets request_count statistics for specified Flows",
        description="The amount of requests made by a Flow call. "
        "If no flow_ids argument is specified, returns the result for all defined Flows.",
        responses={
            200: OpenApiResponse(
                description="Each key is a UUID of a flow object, "
                "each value is the amount of requests made.",
                response={"flow_uuid": "request_count"},
                examples=[
                    OpenApiExample(
                        name="Generic response", value={"flow_uuid": "request_count"}
                    ),
                    OpenApiExample(
                        name="Sample response",
                        value={"123213-e29b-41d4-a716-************": "21"},
                    ),
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS + [FLOW_UUIDS_PARAM, MONGO_FLOW_IDS_PARAM],
    ),
    "logs": extend_schema(
        operation_id="logs",
        summary="Gets logs for specified Flows",
        description="All available logs stored in MongoDB associated with a particular Flow call."
        "If no flow_ids argument is specified, returns the result for all defined Flows.",
        responses={
            200: OpenApiResponse(
                description="Each key is a UUID of a flow object, "
                "each value is the list of its logs. "
                "Each log consists of these fields:\n" + FLOW_LOG_FIELDS,
                response={"flow_uuid": "executed_flow_count"},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={
                            "abbadc-e29b-41d4-a716-************": [
                                {
                                    "_id": "65c0e8e992f2ddd8003e78fb",
                                    "uuid": "3e2832fb-7a21-476c-9beb-4827f43ba5a7",
                                    "name": "testflow",
                                    "start": "2024-02-05T13:55:53.898Z",
                                    "status": {"code": 0, "name": "Success"},
                                    "executed_node_count": 7,
                                    "total_node_count": 7,
                                    "end": "2024-02-05T13:55:54.148Z",
                                    "node_calls": [],
                                }
                            ]
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [FLOW_UUIDS_PARAM, FLOW_STATUS_PARAM, MONGO_FLOW_IDS_PARAM],
    ),
    "node_calls": extend_schema(
        operation_id="node-calls",
        summary="Gets node_calls for specified Flows",
        description="All available node calls logs stored in MongoDB associated with a particular Flow call. "
        "If no flow_ids argument is specified, returns the result for all defined Flows.",
        responses={
            200: OpenApiResponse(
                description="Each key is a UUID of a flow object, "
                "each value is the list of node calls. "
                "Each node call consists of these fields:\n" + NODE_CALL_FIELDS,
                response={"flow_uuid": "executed_flow_count"},
                examples=[
                    OpenApiExample(
                        name="Sample response",
                        value={
                            "uuid1": [
                                {
                                    "$binary": {
                                        "base64": "joHEZu0mQymgGZAm3naNMg==",
                                        "subType": "04",
                                    },
                                    "start": "2024-02-05T16:07:48",
                                    "request_count": 1,
                                    "status": {"code": 0, "name": "Success"},
                                    "errors": [],
                                    "input_data": {},
                                    "end": "2024-02-05T16:07:49",
                                    "output_data": {
                                        "8e81c466-ed26-4329-a019-9026de768d32.activity": "Have a photo session with some friends",
                                        "8e81c466-ed26-4329-a019-9026de768d32.type": "social",
                                        "8e81c466-ed26-4329-a019-9026de768d32.participants": 4,
                                        "8e81c466-ed26-4329-a019-9026de768d32.price": 0.05,
                                        "8e81c466-ed26-4329-a019-9026de768d32.link": "",
                                        "8e81c466-ed26-4329-a019-9026de768d32.key": "3305912",
                                        "8e81c466-ed26-4329-a019-9026de768d32.accessibility": 0.8,
                                    },
                                    "raw_data": {
                                        "Simple Single Request_b56350e5": {
                                            "$binary": {
                                                "base64": "aboba",
                                                "subType": "00",
                                            }
                                        }
                                    },
                                    "retries": 0,
                                }
                            ],
                            "uuid2": [
                                {
                                    "start": "2024-02-05T16:07:49",
                                    "request_count": 0,
                                    "status": {"code": 3, "name": "In progress"},
                                    "errors": [],
                                    "input_data": ["_id1", "_id2"],
                                    "raw_data": ["_id3", "_id4"],
                                }
                            ],
                        },
                    )
                ],
            ),
            400: OpenApiResponse(description="Invalid request."),
            500: OpenApiResponse(description="Internal Server Error."),
        },
        parameters=AVAILABLE_PARAMETERS
        + [
            FLOW_UUIDS_PARAM,
            FLOW_STATUS_PARAM,
            NODE_STATUS_PARAM,
            MONGO_FLOW_IDS_PARAM,
        ],
    ),
}

import unittest

from django.urls import reverse
from rest_framework.test import APIClient
from knox.models import AuthToken
from faker import Faker

from analytics.api.v1.views import OrganizationViewSet
from users.models import SymmyUser
from symmy.test_utils.model_mixins import SimpleFlowData, NestedActionsFlowData
from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    clear_flows_collection,
    clear_gridfs,
)

fake = Faker()


class TestYourViewSet(CheckTestsAllowedMixin, unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.view = OrganizationViewSet.as_view({"get": "logs"})
        cls.client = APIClient()

        cls.org_logs = reverse("api:v1:analytics:organization-logs")
        cls.flow_logs = reverse("api:v1:analytics:flow-logs")

        cls.simple_flow_data = SimpleFlowData()
        cls.complex_flow_data = NestedActionsFlowData()

        cls.simple_flow_data.flow.execute()
        cls.complex_flow_data.flow.execute()

    def test_view_no_auth(self):
        response = self.client.get(self.org_logs)
        self.assertEqual(response.status_code, 401)

    def test_view_wrong_header(self):
        credentials = {
            "email": fake.ascii_email(),
            "password": fake.word() + str(fake.pyint(min_value=1000, max_value=10000)),
            "first_name": fake.first_name(),
        }
        self.user = SymmyUser.objects.create(**credentials)
        self.user.organizations.set([self.simple_flow_data.org])
        self.token = AuthToken.objects.create(self.user)[1]
        self.client.credentials(HTTP_AUTHORIZATION=f"Token {self.token}")
        response = self.client.get(self.org_logs)
        self.assertEqual(response.status_code, 401)

    def test_view_accepts_authenticated_user(self):
        credentials = {
            "email": fake.ascii_email(),
            "password": fake.word() + str(fake.pyint(min_value=1000, max_value=10000)),
            "first_name": fake.first_name(),
        }
        self.user = SymmyUser.objects.create(**credentials)
        self.user.organizations.set([self.simple_flow_data.org])
        self.token = AuthToken.objects.create(self.user)[1]
        self.assertTrue(self.token is not None)
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")
        response = self.client.get(self.org_logs)
        self.assertEqual(response.status_code, 200)

    def test_view_authenticated_get_flow_logs(self):
        credentials = {
            "email": fake.ascii_email(),
            "password": fake.word() + str(fake.pyint(min_value=1000, max_value=10000)),
            "first_name": fake.first_name(),
        }
        self.user = SymmyUser.objects.create(**credentials)
        self.user.organizations.set([self.simple_flow_data.org])
        self.token = AuthToken.objects.create(self.user)[1]
        self.client.credentials(HTTP_AUTHORIZATION=f"Bearer {self.token}")

        response = self.client.get(self.flow_logs)
        self.assertEqual(len(response.data), 1)
        for _, flow_log in response.data.items():
            self.assertEqual(response.status_code, 200)
            self.assertIn("logs", flow_log)
            self.assertTrue(len(flow_log["logs"]) != 0)
            self.assertIn("total_count", flow_log)
            self.assertTrue(flow_log["total_count"] == 1)

    def tearDown(self):
        self.client.credentials(HTTP_AUTHORIZATION="")
        try:
            self.user.delete()
            self.token.delete()
        except Exception:
            pass

    @classmethod
    def tearDownClass(cls):
        cls.simple_flow_data.delete_models()
        cls.complex_flow_data.delete_models()
        clear_flows_collection()
        clear_gridfs()

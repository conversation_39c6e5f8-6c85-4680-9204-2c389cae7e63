import uuid as uu_id
import unittest
from time import sleep
from django.urls import reverse
from rest_framework.test import APIRequestFactory, force_authenticate

from faker import Faker

from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    wait_flow_end,
    clear_flows_collection,
    clear_gridfs,
)
from symmy.test_utils.model_mixins import (
    SimpleFlowData,
    NestedActionsFlowData,
    SimpleExceptionFlowData,
)
from mongo.conf import flows
from mongo.analytics import get_flow_logs
from analytics.api.v1.views import FlowViewSet, OrganizationViewSet, ProjectViewSet
from users.models import SymmyUser


fake = Faker()


class TestAnalyticsViews(CheckTestsAllowedMixin, unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.factory = APIRequestFactory()
        cls.admin = SymmyUser.objects.create_superuser(
            email=fake.ascii_email(), password=fake.password()
        )
        cls.flow_data_objs = list()
        cls.successful_uuids = list()
        cls.exception_uuids = list()
        cls.uuid_flow_data_map = dict()

        cls.simple_flow_data = SimpleFlowData()
        cls.flow_data_objs.append(cls.simple_flow_data)
        cls.successful_uuids.append(cls.simple_flow_data.flow.uuid)
        cls.uuid_flow_data_map[cls.simple_flow_data.flow.uuid] = cls.simple_flow_data

        cls.complex_flow_data = NestedActionsFlowData()
        cls.flow_data_objs.append(cls.complex_flow_data)
        cls.successful_uuids.append(cls.complex_flow_data.flow.uuid)
        cls.uuid_flow_data_map[cls.complex_flow_data.flow.uuid] = cls.complex_flow_data

        cls.exception_flow_data = SimpleExceptionFlowData()
        cls.flow_data_objs.append(cls.exception_flow_data)
        cls.exception_uuids.append(cls.exception_flow_data.flow.uuid)
        cls.uuid_flow_data_map[cls.exception_flow_data.flow.uuid] = (
            cls.exception_flow_data
        )

        cls.all_uuids = cls.exception_uuids + cls.successful_uuids
        cls.str_uuids = [str(u) for u in cls.all_uuids]
        for d in cls.flow_data_objs:
            wait_flow_end(d.flow.execute())
            sleep(1)  # for timestamp filters

        cls.mongo_flow_ids = [str(d["_id"]) for d in list(flows.find({}, {"_id": 1}))]

        cls.flow_count_url = reverse("api:v1:analytics:flow-flow-count")
        cls.node_calls_count_url = reverse("api:v1:analytics:flow-node-calls-count")
        cls.request_count_url = reverse("api:v1:analytics:flow-request-count")
        cls.flow_logs_url = reverse("api:v1:analytics:flow-logs")
        cls.node_calls_url = reverse("api:v1:analytics:flow-node-calls")
        cls.node_calls_org_url = reverse("api:v1:analytics:organization-node-calls")
        cls.project_logs_url = reverse("api:v1:analytics:project-logs")

    def test_flow_count_general(self):
        request = self.factory.get(self.flow_count_url, {})
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), len(self.flow_data_objs))
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(fcount, 1)

    def test_flow_count_object_uuid_single(self):
        request = self.factory.get(
            self.flow_count_url, {"flow_uuids": [self.flow_data_objs[0].flow.uuid]}
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(fcount, 1)

    def test_flow_count_string_uuid_single(self):
        request = self.factory.get(
            self.flow_count_url, {"flow_uuids": [str(self.flow_data_objs[0].flow.uuid)]}
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(fcount, 1)

    def test_flow_count_uuid_multiple(self):
        request = self.factory.get(
            self.flow_count_url,
            {
                "flow_uuids": [
                    self.flow_data_objs[0].flow.uuid,
                    self.flow_data_objs[2].flow.uuid,
                ]
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(fcount, 1)

    def test_flow_count_status_filter(self):
        request = self.factory.get(
            self.flow_count_url,
            {
                "flow_status": [
                    "success",
                ]
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            if uu_id.UUID(uuid) in self.successful_uuids:
                self.assertEqual(fcount, 1)
            else:
                self.assertEqual(fcount, 0)

    def test_flow_count_time_uuid_filter(self):
        fdata = get_flow_logs(
            flow_list=[self.flow_data_objs[0].flow], json_serializable=True
        )["logs"]

        request = self.factory.get(
            self.flow_count_url,
            {
                "flow_uuids": [self.flow_data_objs[0].flow.uuid],
                "date_from": fdata[0]["start"],
                "date_to": fdata[0]["end"],
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 1)
        for uuid, fcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(fcount, 1)

    def test_flow_count_name_as_key(self):
        request = self.factory.get(self.flow_count_url, {"name_as_key": True})
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "flow_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), len(self.flow_data_objs))
        names = [d.flow.name for d in self.flow_data_objs]
        for name, fcount in response.data.items():
            self.assertIn(name, names)
            self.assertEqual(fcount, 1)

    def test_node_calls_count_general(self):
        request = self.factory.get(self.node_calls_count_url, {})
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), len(self.flow_data_objs))
        for uuid, nc_count in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            curr_flow_data = self.uuid_flow_data_map[uu_id.UUID(uuid)]
            self.assertEqual(nc_count, curr_flow_data.executed_node_count)

    def test_request_count(self):
        request = self.factory.get(self.request_count_url, {})
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "request_count",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, rcount in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            self.assertEqual(rcount, 0)

    def test_flow_logs_uuid(self):
        request = self.factory.get(
            self.flow_logs_url,
            {
                "flow_uuids": [dataobj.flow.uuid for dataobj in self.flow_data_objs],
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, logs in response.data.items():
            log = logs["logs"]
            self.assertIn(uuid, self.str_uuids)
            for l in log:
                self.assertTrue(l.get("uuid"))
                self.assertTrue(l.get("_id"))
                self.assertTrue(l.get("status"))

    def test_flow_logs_filter_status_uuid(self):
        request = self.factory.get(
            self.flow_logs_url,
            {
                "flow_uuids": [dataobj.flow.uuid for dataobj in self.flow_data_objs],
                "flow_status": "failed",
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, log in response.data.items():
            self.assertIn(uuid, self.str_uuids)
            if uuid in self.exception_uuids:
                for l in log:
                    self.assertTrue(l.get("uuid"))
                    self.assertTrue(l.get("_id"))
                    self.assertTrue(l.get("status"))

    def test_flow_logs_result_str_uuid(self):
        request = self.factory.get(
            self.flow_logs_url,
            {
                "str_uuid": True,
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, logs in response.data.items():
            log = logs["logs"]
            self.assertIsInstance(uuid, str)
            self.assertIn(uu_id.UUID(uuid), self.all_uuids)
            for l in log:
                self.assertTrue(l.get("uuid"))
                self.assertTrue(l.get("_id"))
                self.assertTrue(l.get("status"))

    def test_node_calls(self):
        request = self.factory.get(
            self.node_calls_url,
            {},
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, node_calls_logs in response.data.items():
            node_calls = node_calls_logs["node_calls"]
            self.assertIn(uuid, self.str_uuids)
            for nc in node_calls:
                self.assertTrue(nc.get("uuid"))
                self.assertTrue(nc.get("status"))

    def test_node_calls_filter(self):
        request = self.factory.get(
            self.node_calls_url,
            {
                "node_status": "failed",
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, node_calls_logs in response.data.items():
            node_calls = node_calls_logs["node_calls"]
            self.assertIn(uuid, self.str_uuids)
            if uuid in self.exception_uuids:
                for nc in node_calls:
                    self.assertTrue(nc.get("uuid"))
                    self.assertTrue(nc.get("status"))

    def test_node_calls_filter_mongo_flow_ids(self):
        request = self.factory.get(
            self.node_calls_url,
            {
                "mongo_flow_ids": self.mongo_flow_ids[:-1],
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 2)
        for mongo_flow_id, node_calls_logs in response.data.items():
            self.assertIn(mongo_flow_id, self.mongo_flow_ids)
            node_calls = node_calls_logs["node_calls"]
            for nc in node_calls:
                self.assertTrue(nc.get("status"))

    def test_node_calls_bulk(self):
        request = self.factory.get(
            self.node_calls_url,
            {
                "bulk_result": True,
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("total_count"))
        self.assertTrue(response.data.get("node_calls"))
        self.assertEqual(
            response.data["total_count"],
            sum([fdata.executed_node_count for fdata in self.flow_data_objs]),
        )
        for nc in response.data["node_calls"]:
            self.assertTrue(nc.get("uuid"))
            self.assertTrue(nc.get("status"))

    def test_node_calls_bulk_organization(self):
        request = self.factory.get(
            self.node_calls_org_url,
            {
                "organization_uuids": [
                    self.flow_data_objs[0].org.uuid,
                    self.flow_data_objs[1].org.uuid,
                ],
                "bulk_result": True,
            },
        )
        force_authenticate(request, user=self.admin)
        response = OrganizationViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("total_count"))
        self.assertTrue(response.data.get("node_calls"))
        self.assertEqual(
            response.data["total_count"],
            self.flow_data_objs[0].executed_node_count
            + self.flow_data_objs[1].executed_node_count,
        )
        for nc in response.data["node_calls"]:
            self.assertTrue(nc.get("uuid"))
            self.assertTrue(nc.get("status"))

    def test_bulk_pagination_node_calls(self):
        request = self.factory.get(
            self.node_calls_url,
            {"bulk_result": True, "limit": 1, "offset": 0},
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("next_url"))
        self.assertTrue(response.data.get("total_count"))
        self.assertTrue(response.data.get("node_calls"))
        self.assertEqual(len(response.data["node_calls"]), 1)
        self.assertEqual(
            response.data["total_count"],
            sum([fdata.executed_node_count for fdata in self.flow_data_objs]),
        )
        for nc in response.data["node_calls"]:
            self.assertTrue(nc.get("uuid"))
            self.assertTrue(nc.get("status"))

    def test_bulk_pagination_node_calls_prev_link(self):
        request = self.factory.get(
            self.node_calls_url,
            {"bulk_result": True, "limit": 1, "offset": 1},
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("next_url"))
        self.assertTrue(response.data.get("prev_url"))
        self.assertTrue(response.data.get("total_count"))
        self.assertTrue(response.data.get("node_calls"))
        self.assertEqual(len(response.data["node_calls"]), 1)
        self.assertEqual(
            response.data["total_count"],
            sum([fdata.executed_node_count for fdata in self.flow_data_objs]),
        )
        for nc in response.data["node_calls"]:
            self.assertTrue(nc.get("uuid"))
            self.assertTrue(nc.get("status"))

    def test_pagination_node_calls(self):
        request = self.factory.get(
            self.node_calls_url,
            {"limit": 2, "offset": 0},
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "node_calls",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data.get("next_url"))
        self.assertEqual(len(response.data), 4)
        for k, v in response.data.items():
            if "url" not in k:
                self.assertTrue(v.get("total_count"))
                self.assertTrue(v.get("node_calls"))

    def test_get_logs_limit(self):
        request = self.factory.get(
            self.flow_logs_url,
            {
                "bulk_result": True,
                "limit": 1,
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data["logs"]), 1)

    def test_get_logs_exclude_nodes(self):
        request = self.factory.get(
            self.flow_logs_url,
            {
                "exclude_nodes": True,
            },
        )
        force_authenticate(request, user=self.admin)
        response = FlowViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.data), 3)
        for uuid, logs in response.data.items():
            for log in logs["logs"]:
                self.assertTrue(log.get("node_calls", None) is None)

    def test_logs_project(self):
        request = self.factory.get(
            self.project_logs_url,
            {},
        )
        force_authenticate(request, user=self.admin)
        response = ProjectViewSet.as_view(
            {
                "get": "logs",
            }
        )(request)

        self.assertEqual(response.status_code, 200)
        for project_uuid, proj_data in response.data.items():
            for flow_uuid, data in proj_data.items():
                self.assertIn("logs", data)
                self.assertIn(flow_uuid, self.str_uuids)
                for result in data["logs"]:
                    self.assertIn("node_calls", result)

    @classmethod
    def tearDownClass(cls):
        cls.admin.organizations.all().delete()
        cls.admin.delete()
        for flow_data in cls.flow_data_objs:
            flow_data.delete_models()

        clear_flows_collection()
        clear_gridfs()

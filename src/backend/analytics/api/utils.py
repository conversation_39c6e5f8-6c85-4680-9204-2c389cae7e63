import re
import logging

from rest_framework import status
from rest_framework.response import Response

logger = logging.getLogger("symmy")

ERROR_MESSAGE_500 = "Internal Server Error"


def _get_new_offset_url(request, offset: int, limit: int):
    return re.sub(
        r"(offset=)\d+", r"\g<1>{}".format(offset + limit), request.build_absolute_uri()
    )


def get_new_offset_urls(request, **kwargs) -> dict:
    res = dict()
    if kwargs.get("limit", None) is not None and kwargs.get("offset", None) is not None:
        limit, offset = kwargs["limit"], kwargs["offset"]
        if limit <= offset:
            res["prev_url"] = _get_new_offset_url(request, offset, -limit)
        res["next_url"] = _get_new_offset_url(request, offset, limit)
    return res


def create_response(func):
    def wrapper(self, *args, **kwargs):
        try:
            data = func(self, *args, **kwargs)
            return Response(data=data, status=status.HTTP_200_OK)

        except (TypeError, ValueError) as ex:
            err_msg = str(ex)
            logger.error(ex)
            return Response(
                data={"error": err_msg},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as ex:
            logger.error(ex)
            return Response(
                data={"error": ERROR_MESSAGE_500},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    return wrapper

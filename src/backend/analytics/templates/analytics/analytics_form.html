<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Form</title>
</head>
<body>

<form id="analytics_form">
    <label for="organization_ids">Organization:</label>
    <select id="organization_ids" name="organization_ids" multiple>
        <option value=""></option>
        {% for org in organizations %}
            <option value={{ org.id }}>{{ org.name }}</option>
        {% endfor %}
    </select>

    <label for="project_ids">Project:</label>
    <select id="project_ids" name="project_ids" multiple>
        <option value=""></option>
    </select>

    <label for="flow_ids">Flows:</label>
    <select id="flow_ids" name="flow_ids" multiple>
        <option value=""></option>
    </select>


    <label for="flow_status">Flow status:</label>
    <select id="flow_status" name="flow_status" multiple>
        <option value=""></option>
        {% for s in statuses %}
            <option value={{ s.name }}>{{ s.value.1 }}</option>
        {% endfor %}
    </select>

    <label for="node_status">Node status:</label>
    <select id="node_status" name="node_status" multiple>
        {% for s in statuses %}
            <option value={{ s.name }}>{{ s.value.1 }}</option>
        {% endfor %}
    </select>

    <label for="date_from">From</label>
    <input type="datetime-local" id="date_from" name="date_from">

    <label for="date_to">To</label>
    <input type="datetime-local" id="date_to" name="date_to">

    <button type="submit">Submit</button>
</form>

<div id="flow_count"></div>
<div id="node_calls_count"></div>
<div id="request_count"></div>
<div id="logs"></div>

<script>
    // TODO: change to proper names, instead of literal constants
    {#let api_endpoint = "{% url 'api:v1:analytics' %}"#}
    let api_endpoint = "http://localhost:8010/api/v1/analytics/"
    let model_endpoints = [
        api_endpoint + "flows/",
        api_endpoint + "projects/",
        api_endpoint + "organizations/",
    ];
    let analytics_endpoints = [
        "flow_count/",
        "node_calls_count/",
        "request_count/",
        "logs/"
    ]

    let analyticsFormId = "analytics_form";
    let organizationSelectBoxId = "organization_id";
    let projectSelectBoxId = "project_id";
    let flowSelectBoxId = "flow_id";

    function updateSelectBox(sourceId, targetUpdateId, targetDeleteIdList, url) {
        // clear related select boxes
        for (let targetDeleteId of targetDeleteIdList) {
            let targetDelete = document.getElementById(targetDeleteId);
            targetDelete.innerHTML = '<option value=""></option>';
        }

        let selectElement = document.getElementById(sourceId);
        let selectedOptions = Array.from(selectElement.selectedOptions);
        url += '?';
        for (let option of selectedOptions) {
            url += `${sourceId}=${option.value}&`;
        }
        let target = document.getElementById(targetUpdateId);

        let xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                let items = JSON.parse(xhr.responseText);
                for (let item of items) {
                    let option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.name;
                    target.appendChild(option);
                }
                target.dispatchEvent(new Event("change"));
            } else {
                console.error('Failed to fetch items:', xhr.statusText);
            }
        };
        xhr.send();
    }

    document.getElementById(organizationSelectBoxId).addEventListener('change', function () {
        updateSelectBox(
            organizationSelectBoxId,
            projectSelectBoxId,
            [projectSelectBoxId, flowSelectBoxId],
            api_endpoint + "projects/"
        );
    });

    document.getElementById(projectSelectBoxId).addEventListener('change', function () {
        updateSelectBox(
            projectSelectBoxId,
            flowSelectBoxId,
            [flowSelectBoxId],
            api_endpoint + "flows/"
        );
    });

    function addSectionHeader(text, target) {
        let sectionHeaderElem = document.createElement("p");
        sectionHeaderElem.textContent = text;
        target.appendChild(sectionHeaderElem);
    }

    function addElem(text, target) {
        let elem = document.createElement("p");
        elem.textContent = text;
        target.appendChild(elem);
    }

    function formatItem(item) {
        let line = '';

        if (typeof item === 'object') {
            for (let [k, v] of Object.entries(item)) {
                line += `${k} - ${v};\n`;
            }
        } else {
            line += String(item) + ';\n';
        }

        return line;
    }

    function getQuery(name, params) {
        let query = "?";
        for (let v of params) {
            query += `${name}=${v}&`;
        }
        return query;
    }

    function populateResult(formData) {
        // TODO: do everything properly:)))
        let query = "";
        let m_endpoint = "";
        let flow_ids = formData.getAll("flow_id");
        let project_ids = formData.getAll("project_id");
        let organization_ids = formData.getAll("organization_id");
        if (flow_ids.length > 0) {
            m_endpoint = model_endpoints[0];
            query = getQuery("flow_id", flow_ids);
        } else if (project_ids.length > 0) {
            m_endpoint = model_endpoints[1];
            query = getQuery("project_id", project_ids);
        } else {
            m_endpoint = model_endpoints[2];
            query = getQuery("organization_id", organization_ids);
        }

        for (let a_endpoint of analytics_endpoints) {
            let a_name = a_endpoint.slice(0, -1);
            let target = document.getElementById(a_name);
            target.innerHTML = "";

            let xhr = new XMLHttpRequest();
            xhr.open('GET', m_endpoint + a_endpoint + query, true);
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onload = function () {
                if (xhr.status >= 200 && xhr.status < 300) {
                    addSectionHeader(target, a_name);

                    let items = JSON.parse(xhr.responseText);
                    if (Array.isArray(items)) {
                        for (let item of items) {
                            let elem = formatItem(item);
                            addElem(elem, target);
                        }
                    } else {
                        let elem = formatItem(items);
                        addElem(elem, target);
                    }

                    target.appendChild(document.createElement("br"));
                } else {
                    console.error('Failed to fetch items:', xhr.statusText);
                }
            };
            xhr.send();
        }

    }


    document.getElementById(analyticsFormId).addEventListener('submit', function (event) {
        event.preventDefault();
        let formData = new FormData(this);
        populateResult(formData);
    });

</script>

</body>
</html>

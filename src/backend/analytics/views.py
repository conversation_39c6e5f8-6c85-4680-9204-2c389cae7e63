from django.views.generic.base import TemplateView

from core.models import Organization
from mongo.utils import status_as_dict
from mongo.types import Status


class InfoForm(TemplateView):
    template_name = "analytics/analytics_form.html"

    def get_context_data(self, **kwargs):
        context = dict()
        context["organizations"] = Organization.objects.all()
        context["statuses"] = [s for s in Status]
        return context

from rest_framework import generics
from rest_framework import status
from rest_framework.response import Response
from .serializers import ProcesWebhookDataSerializer


class ProcessWebhookData(generics.CreateAPIView):
    serializer_class = ProcesWebhookDataSerializer

    def post(self, request, **kwargs):
        action_uuid = kwargs.get("action_uuid")
        flow_uuid = kwargs.get("flow_uuid")
        if not action_uuid or not flow_uuid:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        # TODO: handle webhook data
        return Response(f"Processing webhook data for {action_uuid}:{flow_uuid}")

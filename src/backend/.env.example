DEBUG=True
DJANGO_SECRET_KEY=<SECRET_KEY>
DJANGO_ALLOWED_HOSTS=*
DJANGO_BASE_URL=http://localhost:8010

TZ=Europe/Prague

SQL_ENGINE=django.db.backends.postgresql
POSTGRES_USER=<DB>
POSTGRES_PASSWORD=<USER>
POSTGRES_DB=<PASSWORD>
POSTGRES_HOST=db
POSTGRES_PORT=5432

CELERY_BROKER_URL=amqp://rabbitmq
CELERY_RESULT_BACKEND=redis://redis:6379

IGNORE_PACKAGES=symmy_boredapi,symmy_nodetype

MONGO_INITDB_ROOT_USERNAME=<NAME>
MONGO_INITDB_ROOT_PASSWORD=<PASSWORD>
MONGO_HOST=mongo
MONGO_PORT=27017
MONGO_CONNECTION_STRING=mongodb://{user}:{password}@{host}:{port}/admin

ALLOW_PROD_DB_TESTS=False

EMAIL_HOST=mx.ftcz.net
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=<PASSWORD>

SSH_AUTH_SOCK=/ssh-agent # MacOS

"""
Django settings for symmy project.

Generated by 'django-admin startproject' using Django 4.2.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import logging.config
import os
from datetime import timed<PERSON><PERSON>
from pathlib import Path

import sentry_sdk
from decouple import Csv, config

# from dj_database_url import parse as db_url
from dotenv import load_dotenv
from sentry_sdk.integrations.django import DjangoIntegration

from symmy import VERSION

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
BASE_URL = config("DJANGO_BASE_URL", default="https://tune.symmy.app/")

SECRET_KEY = config("DJANGO_SECRET_KEY", default="django-SECRET_KEY")

DEBUG = config("DEBUG", default=True, cast=bool)

ENV = config("ENV", default="TEST").upper()

ALLOWED_HOSTS = config(
    "DJANGO_ALLOWED_HOSTS", default="localhost,127.0.0.1", cast=Csv()
)

AUTH_USER_MODEL = "users.SymmyUser"

CSRF_TRUSTED_ORIGINS = config("CSRF_TRUSTED_ORIGINS", default=BASE_URL, cast=Csv())

# https://github.com/jazzband/django-tinymce/issues/354
X_FRAME_OPTIONS = "SAMEORIGIN"
# https://issueantenna.com/repo/jazzband/django-tinymce/issues/389
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

DATETIME_DEPLOY = config("DATETIME_DEPLOY", default="")

SITE_ID = 1

# Internal IPs docker config
if DEBUG:
    import socket

    hostname, _, ips = socket.gethostbyname_ex(socket.gethostname())
    INTERNAL_IPS = [ip[: ip.rfind(".")] + ".1" for ip in ips] + [
        "127.0.0.1",
        "********",
    ]

# Application definition

INSTALLED_APPS = [
    # Jet
    "jet.dashboard",
    "jet",
    # Django
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    # 3rd Party
    "debug_toolbar",
    "django_celery_beat",
    "django_extensions",
    "django_object_actions",
    "drf_spectacular",  # DRF Yet Another Swagger Generator
    "rest_framework",
    "knox",
    "django_json_widget",
    "admin_extra_buttons",
    # Local
    "connections.apps.ConnectionsConfig",
    "core.apps.CoreConfig",
    "flows.apps.FlowsConfig",
    "nodetypes.apps.NodetypesConfig",
    "users.apps.UsersConfig",
    "webhooks.apps.WebhooksConfig",
    "analytics.apps.AnalyticsConfig",
    "endpoints.apps.EndpointsConfig",
    "mails.apps.MailsConfig",
    "mongo.apps.MongoConfig",
    "testapi.apps.TestapiConfig",
    # Django Cleanup - should stay last
    "django_cleanup.apps.CleanupConfig",
]

CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"
# Disabling asynchronous operation of tasks Celery if the parameter is True
CELERY_TASK_ALWAYS_EAGER = config("CELERY_TASK_ALWAYS_EAGER", default=DEBUG, cast=bool)

MIDDLEWARE = [
    "log_request_id.middleware.RequestIDMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "symmy.middleware.DocumentPolicyMiddleware",
]

ROOT_URLCONF = "symmy.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "symmy.wsgi.application"

# The time it takes for a PasswordResetToken to expire
PASSWORD_RESET_TIMEOUT = 15 * 60  # 15 minutes

# REST
# https://www.django-rest-framework.org/

REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "knox.auth.TokenAuthentication",
    ],
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.NamespaceVersioning",
    "ALLOWED_VERSIONS": ["v1", "v2"],
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
}

REST_KNOX = {
    "TOKEN_TTL": timedelta(days=7),  # AuthToken expiration time
    "AUTH_HEADER_PREFIX": "Bearer",
}

# DRF Spectacular
# https://drf-spectacular.readthedocs.io/en/latest/readme.html

SPECTACULAR_SETTINGS = {
    "TITLE": "Symmy API",
    "DESCRIPTION": "An innovative ERP integration system.",
    "VERSION": "v1",
    "SERVE_INCLUDE_SCHEMA": False,
    "SCHEMA_PATH_PREFIX": r"/api/v[0-9]",
    "SCHEMA_PATH_PREFIX_TRIM": True,
    "SWAGGER_UI_SETTINGS": {
        "deepLinking": True,
        "displayOperationId": True,
    },
}

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": config("SQL_ENGINE", default="django.db.backends.sqlite3"),
        "USER": config("POSTGRES_USER", default="postgres"),
        "PASSWORD": config("POSTGRES_PASSWORD", default="password"),
        "NAME": config("POSTGRES_DB", default=os.path.join(BASE_DIR, "db.sqlite3")),
        "HOST": config("POSTGRES_HOST", default="localhost"),
        "PORT": config("POSTGRES_PORT", default="5432"),
    },
}
# MONGO
MONGO_INITDB_ROOT_USERNAME = config("MONGO_INITDB_ROOT_USERNAME", default="symmy")
MONGO_INITDB_ROOT_PASSWORD = config("MONGO_INITDB_ROOT_PASSWORD", default="symmy")
MONGO_HOST = config("MONGO_HOST", default="mongo")
MONGO_PORT = config("MONGO_PORT", default=27017)
MONGO_CONNECTION_STRING = config(
    "MONGO_CONNECTION_STRING", default="*******************************************"
)

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": config("CACHES_REDIS_LOCATION", default="redis://redis:6379/1"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        },
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "/admin/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "staticfiles")
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

STORAGES = {
    "default": {
        "BACKEND": "django.core.files.storage.FileSystemStorage",
    },
    "staticfiles": {
        "BACKEND": "whitenoise.storage.CompressedStaticFilesStorage",
    },
}

# Media files --- generated content by your application or users of your application
MEDIA_URL = "/admin/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

IGNORE_PACKAGES = config(
    "IGNORE_PACKAGES", default="symmy_boredapi,symmy_nodetype", cast=Csv()
)

# EMAIL
EMAIL_BACKEND = "mails.backends.smtp.SymmyEmailBackend"
EMAIL_MESSAGE_CLASS = "mails.message.SymmyEmailMessage"
EMAIL_DEBUG_SEND = False

EMAIL_HOST = config("EMAIL_HOST", default="smtp.example.com")
EMAIL_HOST_USER = config("EMAIL_HOST_USER", default="<EMAIL>")
EMAIL_HOST_PASSWORD = config("EMAIL_HOST_PASSWORD", default="your_password")
DEFAULT_FROM_EMAIL = config("DEFAULT_FROM_EMAIL", default=EMAIL_HOST_USER)

EMAIL_PORT = config("EMAIL_PORT", default=0)
EMAIL_USE_TLS = config("EMAIL_USE_TLS", default=True)
EMAIL_USE_SSL = config("EMAIL_USE_SSL", default=False)

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# celery setup
CELERY_BROKER_URL = config("CELERY_BROKER_URL", default="amqp://localhost")
CELERY_RESULT_BACKEND = config(
    "CELERY_RESULT_BACKEND", default="redis://localhost:6379"
)
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TASK_RETRY_BACKOFF = 60  # seconds
CELERY_TASK_MAX_RETRIES = 3

# TEST SETTINGS
TEST_RUNNER = "symmy.test_utils.test_runner.SymmyTestRunner"
ALLOW_PROD_DB_TESTS = config("ALLOW_PROD_DB_TESTS", default=False, cast=bool)

# LOGGING SETTINGS
LOGGING_CONFIG = None  # This empties out Django's logging config
LOGLEVEL = config("LOGLEVEL", default="info").upper()
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "()": "colorlog.ColoredFormatter",
            "format": "%(log_color)s %(levelname)s [%(name)s] %(asctime)s %(request_id)s %(process)s | "
            "%(module)s:%(lineno)s:%(funcName)s | %(message)s",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "white",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "bold_red",
            },
        },
        "verbose_symmy": {
            "()": "colorlog.ColoredFormatter",
            "format": "%(log_color)s %(levelname)s [%(name)s] %(asctime)s %(request_id)s %(process)s | "
            "%(pathname)s:%(lineno)s:%(funcName)s | %(message)s",
            "log_colors": {
                "DEBUG": "cyan",
                "INFO": "white",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "bold_red",
            },
        },
        "aws": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "filters": {
        "request_id": {"()": "log_request_id.filters.RequestIDFilter"},
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "verbose",
            "filters": ["request_id"],
        },
        "console_symmy": {
            "class": "logging.StreamHandler",
            "formatter": "verbose_symmy",
            "filters": ["request_id"],
        },
    },
    "loggers": {
        # Default logger for any logger name
        "": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
        "symmy": {
            "level": LOGLEVEL,
            "handlers": ["console_symmy"],
            "propagate": False,
        },
        "django.server": {
            "level": "DEBUG",
            "handlers": ["console"],
            "propagate": False,
        },
    },
}
logging.config.dictConfig(LOGGING)

# SENTRY CONFIG
SENTRY_DSN = config("SENTRY_DSN", default="")
SENTRY_TRACES_RATE = config("SENTRY_TRACES_RATE", default=1.0, cast=float)
SENTRY_PROFILES_RATE = config("SENTRY_PROFILES_RATE", default=0.5, cast=float)
if not DEBUG and SENTRY_DSN:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        traces_sample_rate=SENTRY_TRACES_RATE,
        profiles_sample_rate=SENTRY_PROFILES_RATE,
        environment=ENV,
        release=VERSION,
    )

# JET CONFIG
JET_SIDE_MENU_COMPACT = True
JET_INDEX_DASHBOARD = "symmy.dashboard.CustomDashboard"

# DEFAULT NAMES
DEFAULT_ORGANIZATION_NAME = "My Organization"
DEFAULT_PROJECT_NAME = "My Project"

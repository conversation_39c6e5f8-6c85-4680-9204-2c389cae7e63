from time import sleep

from django.conf import settings

from mongo.analytics import get_flow_logs
from mongo.conf import db, grid_fs


def wait_flow_end(mongo_flow_id, timeout=60, retry_delay=0.5):  # secs
    i = 0
    status = get_flow_logs(mongo_flow_ids=[mongo_flow_id])["logs"][0]["status"]["name"]
    while status not in ("Success", "Failed") and i < timeout:
        sleep(retry_delay)
        i += retry_delay
        status = get_flow_logs(mongo_flow_ids=[mongo_flow_id])["logs"][0]["status"]["name"]
    return status


def clear_flows_collection():
    db.flows.drop()


def clear_gridfs():
    for i in grid_fs.find():
        grid_fs.delete(i._id)


class CheckTestsAllowedMixin:
    def __init__(self, *args, **kwargs):
        if not settings.ALLOW_PROD_DB_TESTS:
            raise Exception("Tests are not allowed to run")
        super().__init__(*args, **kwargs)

import unittest

from django.test.runner import DiscoverRunner

from .utils import CheckTestsAllowedMixin


class SymmyTestRunner(DiscoverRunner):
    def build_suite(self, test_labels=None, extra_tests=None, **kwargs):
        suite = super().build_suite(test_labels, extra_tests, **kwargs)
        filtered_suite = unittest.TestSuite()
        for t in suite:
            if not issubclass(type(t), CheckTestsAllowedMixin):
                filtered_suite.addTest(t)

        return filtered_suite

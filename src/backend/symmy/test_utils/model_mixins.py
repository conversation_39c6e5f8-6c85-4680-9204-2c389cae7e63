from faker import Faker

from connections.models import Connection
from core.models import Project, Organization
from flows.models import Flow, Node, NodeTypeResource
from users.models import SymmyUser

fake = Faker()


class CommonFlowData:
    node_count = 0

    def __init__(self):
        self.all_models = list()

        self.org = Organization.objects.create(
            name=fake.word(), email=fake.ascii_email()
        )
        self.all_models.append(self.org)

        self.usr = SymmyUser.objects.create(
            first_name=fake.name(), email=fake.ascii_email()
        )
        self.all_models.append(self.usr)

        self.project = Project.objects.create(
            name=fake.word(), user=self.usr, organization=self.org
        )
        self.all_models.append(self.project)

        self.flow = Flow.objects.create(name=fake.word(), project=self.project)
        self.all_models.append(self.flow)

        self.nodetype = NodeTypeResource.objects.create(
            prefix="symmy",
            name="testnode",
            uri=".",
            is_available=True,
            is_official=True,
            is_public=True,
        )
        self.all_models.append(self.nodetype)

        self.conn = Connection.objects.create(
            name=fake.word(),
            data={},
            nodetype=self.nodetype,
            organization=self.org,
            user=self.usr,
        )
        self.all_models.append(self.conn)

        self.simple_action = self.nodetype.actions.get(name="NoRequest")
        self.all_models.append(self.simple_action)

        self.nested_simple = self.nodetype.actions.get(
            name="NoRequestSingleLevelComposite"
        )
        self.all_models.append(self.nested_simple)

        self.nested_complex = self.nodetype.actions.get(
            name="NoRequestMultiLevelComposite"
        )
        self.all_models.append(self.nested_complex)

        self.exception_action = self.nodetype.actions.get(
            name="NoRequestRaiseException"
        )
        self.all_models.append(self.exception_action)

        self.nested_exception_action = self.nodetype.actions.get(
            name="NoRequestCompositeRaiseException"
        )
        self.all_models.append(self.nested_exception_action)

        self.retry_simple = self.nodetype.actions.get(name="NoRequestRaiseRetryError")
        self.all_models.append(self.retry_simple)

    def get_supposed_request_count(self):
        res = 0
        for i in range(self.node_count):
            node = getattr(self, f"n{i}")
            action = node.node_instance.get_action(node.action_resource.name)
            res += getattr(action, "supposed_request_count", 0)

        return res

    def delete_models(self):
        # No need, as when a flow is deleted,
        # all related nodes are deleted as well
        # for i in range(self.node_count):
        #     getattr(self, f"n{i}").delete()

        for model in self.all_models:
            try:
                model.delete(soft=False)
            except TypeError:
                model.delete()


class SimpleFlowData(CommonFlowData):
    node_count = 7
    executed_node_count = 7

    def __init__(self):
        super().__init__()
        #    ---> n3 ---> n6
        #   /
        # n0--> n2
        #   \
        #    ---> n1 ---> n4
        #         \
        #          ---> n5
        for i in range(self.node_count):
            node_name = f"n{i}"
            new_node = Node.objects.create(
                flow=self.flow,
                node_type_resource=self.nodetype,
                action_resource=self.simple_action,
                connection=self.conn,
            )
            setattr(self, node_name, new_node)
        self.n0.append(self.n1)
        self.n0.append(self.n2)
        self.n0.append(self.n3)

        self.n1.append(self.n4)
        self.n1.append(self.n5)

        self.n3.append(self.n6)


class NestedActionsFlowData(CommonFlowData):
    node_count = 4
    executed_node_count = 4

    def __init__(self):
        super().__init__()
        #    ---> n1 ---> n3
        #   /
        # n0--> n2

        self.n0 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.simple_action,
            connection=self.conn,
        )
        self.n1 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.nested_simple,
            connection=self.conn,
        )
        self.n2 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.nested_complex,
            connection=self.conn,
        )

        self.n3 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.nested_simple,
            connection=self.conn,
        )

        self.n0.append(self.n1)
        self.n0.append(self.n2)

        self.n1.append(self.n3)


class SimpleExceptionFlowData(CommonFlowData):
    node_count = 2
    exception_node_count = 1
    executed_node_count = 2

    def __init__(self):
        super().__init__()

        # n0--> n1

        self.n0 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.simple_action,
            connection=self.conn,
        )

        self.n1 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.exception_action,
            connection=self.conn,
        )

        self.n0.append(self.n1)


class ComplexExceptionFlowData(CommonFlowData):
    node_count = 5
    exception_node_count = 2
    executed_node_count = 4

    def __init__(self):
        super().__init__()
        #    ---> n1 ---> n4
        #   /
        # n0--> n2
        #   \
        #    ---> n3

        self.n0 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.simple_action,
            connection=self.conn,
        )
        self.n1 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.nested_exception_action,
            connection=self.conn,
        )
        self.n2 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.simple_action,
            connection=self.conn,
        )
        self.n3 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.exception_action,
            connection=self.conn,
        )
        self.n4 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.simple_action,
            connection=self.conn,
        )

        self.n0.append(self.n1)
        self.n0.append(self.n2)
        self.n0.append(self.n3)

        self.n1.append(self.n4)


class SimpleRetryFlowData(CommonFlowData):
    node_count = 1
    exception_node_count = 1
    executed_node_count = 1

    def __init__(self):
        super().__init__()
        # n0

        self.n0 = Node.objects.create(
            flow=self.flow,
            node_type_resource=self.nodetype,
            action_resource=self.retry_simple,
            connection=self.conn,
        )

from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from jet.dashboard import modules
from jet.dashboard.dashboard import DefaultIndexDashboard


class CustomDashboard(DefaultIndexDashboard):
    columns = 3

    def init_with_context(self, context):
        super().init_with_context(context)
        self.available_children.append(modules.LinkList)
        self.children.append(modules.LinkList(

            _("Analytics tool"),
            children=[
                {
                    "title": "Analytics",
                    "url": reverse("analytics:info_form"),
                    "external": False,
                },
            ],
            column=0,
            order=0)
        ),
        self.children.append(modules.LinkList(
            _("Servers"),
            children=[
                {
                    "title": "Test server",
                    "url": "https://tune.symmy.app/login",
                    "external": True
                },
                {
                    "title": "Production server",
                    "url": "https://symmy.app/login",
                    "external": True
                }
            ],
            column=0,
            order=1)
        )

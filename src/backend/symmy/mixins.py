from users.models import SymmyUser


class FilterByUserMixin:
    @staticmethod
    def filter_queryset_superuser(queryset):
        raise NotImplementedError("filter_queryset_superuser must be implemented")

    @staticmethod
    def filter_queryset_regular_user(queryset, user):
        raise NotImplementedError("filter_queryset_regular_user must be implemented")

    def get_queryset(self):
        if self.request and self.request.user:
            queryset = self.model.objects.all()
            user = self.request.user
            if user.is_superuser:
                queryset = self.filter_queryset_superuser(queryset)
            else:
                queryset = self.filter_queryset_regular_user(queryset, user)

            return queryset

        return self.model.objects.none()

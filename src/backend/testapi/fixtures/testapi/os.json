[{"model": "testapi.operatingsystem", "pk": 1, "fields": {"name": "Windows", "kernel": "Windows NT", "kernel_type": "hybrid", "distributor": "Microsoft"}}, {"model": "testapi.operatingsystem", "pk": 2, "fields": {"name": "Ubuntu", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Canonical"}}, {"model": "testapi.operatingsystem", "pk": 3, "fields": {"name": "macOS", "kernel": "XNU", "kernel_type": "hybrid", "distributor": "Apple Inc."}}, {"model": "testapi.operatingsystem", "pk": 4, "fields": {"name": "<PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Fedora Project"}}, {"model": "testapi.operatingsystem", "pk": 5, "fields": {"name": "FreeBSD", "kernel": "BSD", "kernel_type": "monolithic", "distributor": "The FreeBSD Project"}}, {"model": "testapi.operatingsystem", "pk": 6, "fields": {"name": "Debian", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Debian Project"}}, {"model": "testapi.operatingsystem", "pk": 7, "fields": {"name": "Arch Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Arch Linux Team"}}, {"model": "testapi.operatingsystem", "pk": 8, "fields": {"name": "Gentoo", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Gentoo Foundation"}}, {"model": "testapi.operatingsystem", "pk": 9, "fields": {"name": "Slackware", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Slackware Project"}}, {"model": "testapi.operatingsystem", "pk": 10, "fields": {"name": "Red Hat Enterprise Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Red Hat"}}, {"model": "testapi.operatingsystem", "pk": 11, "fields": {"name": "CentOS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "CentOS Project"}}, {"model": "testapi.operatingsystem", "pk": 12, "fields": {"name": "SUSE Linux Enterprise", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "SUSE"}}, {"model": "testapi.operatingsystem", "pk": 13, "fields": {"name": "OpenSUSE", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "OpenSUSE Project"}}, {"model": "testapi.operatingsystem", "pk": 14, "fields": {"name": "Kali Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Offensive Security"}}, {"model": "testapi.operatingsystem", "pk": 15, "fields": {"name": "Parrot OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Parrot Security"}}, {"model": "testapi.operatingsystem", "pk": 16, "fields": {"name": "Android", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Google"}}, {"model": "testapi.operatingsystem", "pk": 17, "fields": {"name": "iOS", "kernel": "XNU", "kernel_type": "hybrid", "distributor": "Apple Inc."}}, {"model": "testapi.operatingsystem", "pk": 18, "fields": {"name": "Solar<PERSON>", "kernel": "SunOS", "kernel_type": "monolithic", "distributor": "Oracle Corporation"}}, {"model": "testapi.operatingsystem", "pk": 19, "fields": {"name": "Illumos", "kernel": "SunOS", "kernel_type": "monolithic", "distributor": "Illumos Foundation"}}, {"model": "testapi.operatingsystem", "pk": 20, "fields": {"name": "AIX", "kernel": "Unix", "kernel_type": "monolithic", "distributor": "IBM"}}, {"model": "testapi.operatingsystem", "pk": 21, "fields": {"name": "HP-UX", "kernel": "Unix", "kernel_type": "monolithic", "distributor": "Hewlett-Packard"}}, {"model": "testapi.operatingsystem", "pk": 22, "fields": {"name": "QNX", "kernel": "Neutrino", "kernel_type": "microkernel", "distributor": "BlackBerry Limited"}}, {"model": "testapi.operatingsystem", "pk": 23, "fields": {"name": "Minix", "kernel": "Minix", "kernel_type": "microkernel", "distributor": "Vrije Universiteit"}}, {"model": "testapi.operatingsystem", "pk": 24, "fields": {"name": "GNU Hurd", "kernel": "Mach", "kernel_type": "microkernel", "distributor": "GNU Project"}}, {"model": "testapi.operatingsystem", "pk": 25, "fields": {"name": "Fuchsia", "kernel": "Zircon", "kernel_type": "microkernel", "distributor": "Google"}}, {"model": "testapi.operatingsystem", "pk": 26, "fields": {"name": "DragonFly BSD", "kernel": "BSD", "kernel_type": "monolithic", "distributor": "DragonFly BSD Project"}}, {"model": "testapi.operatingsystem", "pk": 27, "fields": {"name": "Hai<PERSON>", "kernel": "NewOS", "kernel_type": "hybrid", "distributor": "Haiku, Inc."}}, {"model": "testapi.operatingsystem", "pk": 28, "fields": {"name": "ReactOS", "kernel": "NT", "kernel_type": "hybrid", "distributor": "ReactOS Foundation"}}, {"model": "testapi.operatingsystem", "pk": 29, "fields": {"name": "Tizen", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Linux Foundation"}}, {"model": "testapi.operatingsystem", "pk": 30, "fields": {"name": "Chrome OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Google"}}, {"model": "testapi.operatingsystem", "pk": 31, "fields": {"name": "Manjaro", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Manjaro GmbH & Co. KG"}}, {"model": "testapi.operatingsystem", "pk": 32, "fields": {"name": "<PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Deepin Technology"}}, {"model": "testapi.operatingsystem", "pk": 33, "fields": {"name": "Zorin OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Zorin Group"}}, {"model": "testapi.operatingsystem", "pk": 34, "fields": {"name": "Elementary OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Elementary, Inc."}}, {"model": "testapi.operatingsystem", "pk": 35, "fields": {"name": "MX Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "MX Linux Community"}}, {"model": "testapi.operatingsystem", "pk": 36, "fields": {"name": "Lubuntu", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Lubuntu Team"}}, {"model": "testapi.operatingsystem", "pk": 37, "fields": {"name": "Peppermint OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Peppermint OS Team"}}, {"model": "testapi.operatingsystem", "pk": 38, "fields": {"name": "Pop!_OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "System76"}}, {"model": "testapi.operatingsystem", "pk": 39, "fields": {"name": "Solus", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Solus Project"}}, {"model": "testapi.operatingsystem", "pk": 40, "fields": {"name": "Puppy Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 41, "fields": {"name": "Trisquel", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Trisquel Project"}}, {"model": "testapi.operatingsystem", "pk": 42, "fields": {"name": "ClearOS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "ClearFoundation"}}, {"model": "testapi.operatingsystem", "pk": 43, "fields": {"name": "Void Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Void Linux Community"}}, {"model": "testapi.operatingsystem", "pk": 44, "fields": {"name": "<PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON><PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 45, "fields": {"name": "NixOS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "NixOS Foundation"}}, {"model": "testapi.operatingsystem", "pk": 46, "fields": {"name": "EndeavourOS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "EndeavourOS Team"}}, {"model": "testapi.operatingsystem", "pk": 47, "fields": {"name": "KDE neon", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "KDE Community"}}, {"model": "testapi.operatingsystem", "pk": 48, "fields": {"name": "Garuda Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Garuda Linux Team"}}, {"model": "testapi.operatingsystem", "pk": 49, "fields": {"name": "LXLE", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 50, "fields": {"name": "Ko<PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "EagleEye"}}, {"model": "testapi.operatingsystem", "pk": 51, "fields": {"name": "Elive", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Elive Team"}}, {"model": "testapi.operatingsystem", "pk": 52, "fields": {"name": "Calculate Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Calculate Ltd."}}, {"model": "testapi.operatingsystem", "pk": 53, "fields": {"name": "MakuluLinux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "MakuluLinux Team"}}, {"model": "testapi.operatingsystem", "pk": 54, "fields": {"name": "<PERSON><PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Robolinux Team"}}, {"model": "testapi.operatingsystem", "pk": 55, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Emmabuntüs Collective"}}, {"model": "testapi.operatingsystem", "pk": 56, "fields": {"name": "ArcoLinux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 57, "fields": {"name": "Bluestar Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Bluestar Linux Team"}}, {"model": "testapi.operatingsystem", "pk": 58, "fields": {"name": "AntiX", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Antix Project"}}, {"model": "testapi.operatingsystem", "pk": 59, "fields": {"name": "<PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Feren OS Team"}}, {"model": "testapi.operatingsystem", "pk": 60, "fields": {"name": "<PERSON><PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Nitrux Latinoamericana"}}, {"model": "testapi.operatingsystem", "pk": 61, "fields": {"name": "Q4OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Q4OS Team"}}, {"model": "testapi.operatingsystem", "pk": 62, "fields": {"name": "Raspberry Pi OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Raspberry Pi Foundation"}}, {"model": "testapi.operatingsystem", "pk": 63, "fields": {"name": "Redcore Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Redcore Project"}}, {"model": "testapi.operatingsystem", "pk": 64, "fields": {"name": "Sabayon Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Sabayon Project"}}, {"model": "testapi.operatingsystem", "pk": 65, "fields": {"name": "SparkyLinux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "SparkyLinux Team"}}, {"model": "testapi.operatingsystem", "pk": 66, "fields": {"name": "Peppermint OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Peppermint OS Team"}}, {"model": "testapi.operatingsystem", "pk": 67, "fields": {"name": "Mageia", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Mageia.Org"}}, {"model": "testapi.operatingsystem", "pk": 68, "fields": {"name": "Mandriva", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Mandriva S.A."}}, {"model": "testapi.operatingsystem", "pk": 69, "fields": {"name": "<PERSON><PERSON><PERSON>", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "TÜBİTAK"}}, {"model": "testapi.operatingsystem", "pk": 70, "fields": {"name": "ROSA", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "ROSA Laboratory"}}, {"model": "testapi.operatingsystem", "pk": 71, "fields": {"name": "ALT Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "BaseALT Ltd."}}, {"model": "testapi.operatingsystem", "pk": 72, "fields": {"name": "OpenMandriva", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "OpenMandriva Association"}}, {"model": "testapi.operatingsystem", "pk": 73, "fields": {"name": "PCLinuxOS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "PCLinuxOS Community"}}, {"model": "testapi.operatingsystem", "pk": 74, "fields": {"name": "Zenwalk", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Zenwalk Team"}}, {"model": "testapi.operatingsystem", "pk": 75, "fields": {"name": "4MLinux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON><PERSON><PERSON><PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 76, "fields": {"name": "Slax", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 77, "fields": {"name": "Tiny Core Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Tiny Core Linux Team"}}, {"model": "testapi.operatingsystem", "pk": 78, "fields": {"name": "Plop Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON><PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 79, "fields": {"name": "Absolute Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 80, "fields": {"name": "Bodhi Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Bodhi Linux Team"}}, {"model": "testapi.operatingsystem", "pk": 81, "fields": {"name": "SliTaz", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 82, "fields": {"name": "Damn Small Linux", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "<PERSON>"}}, {"model": "testapi.operatingsystem", "pk": 83, "fields": {"name": "Peppermint OS", "kernel": "Linux", "kernel_type": "monolithic", "distributor": "Peppermint OS Team"}}]
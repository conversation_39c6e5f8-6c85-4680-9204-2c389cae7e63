from django.db import models
from django.utils.translation import gettext_lazy as _

# Create your models here.


class OperatingSystem(models.Model):
    class KernelType(models.TextChoices):
        MONOLITHIC = "monolithic", _("Monolithic")
        HYBRID = "hybrid", _("Hybrid")
        MICROKERNEL = "microkernel", _("Microkernel")
        NANOKERNEL = "nanokernel", _("Nanokernel")
        EXOKERNEL = "exokernel", _("Exokernel")

    name = models.CharField(_("Name"), max_length=100)
    kernel = models.CharField(_("Kernel"), max_length=100)
    kernel_type = models.CharField(
        _("Kernel Type"), max_length=20, choices=KernelType.choices
    )
    distributor = models.CharField(_("Distributor"), max_length=100)

from random import choice

from django.db.models import Q
from django.urls.exceptions import Http404
from rest_framework import generics

from testapi.api.v1.serializers import OperatingSystemSerializer
from testapi.models import OperatingSystem

# Create your views here.


class RetrieveOperatingSystemView(generics.RetrieveAPIView):
    serializer_class = OperatingSystemSerializer

    def get_queryset(self):
        query_params = self.request.query_params
        name = query_params.get("name")
        kernel = query_params.get("kernel")
        kernel_type = query_params.get("kernel_type")
        distributor = query_params.get("distributor")

        filters = Q()

        if name is not None:
            filters &= Q(name__iexact=name)

        if kernel is not None:
            filters &= Q(kernel__iexact=kernel)

        if kernel_type is not None:
            filters &= Q(kernel_type__iexact=kernel_type)

        if distributor is not None:
            filters &= Q(distributor__iexact=distributor)

        return OperatingSystem.objects.filter(filters)

    def get_object(self):
        queryset = self.get_queryset()

        if not queryset.exists():
            raise Http404

        return choice(queryset)

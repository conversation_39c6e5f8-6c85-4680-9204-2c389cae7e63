# Generated by Django 4.2.2 on 2024-06-14 16:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='OperatingSystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100, verbose_name='Name')),
                ('kernel', models.Char<PERSON>ield(max_length=100, verbose_name='Kernel')),
                ('kernel_type', models.CharField(choices=[('monolithic', 'Monolithic'), ('hybrid', 'Hybrid'), ('microkernel', 'Microkernel'), ('nanokernel', 'Nanokernel'), ('exokernel', 'Exokernel')], max_length=20, verbose_name='Kernel Type')),
                ('distributor', models.CharField(max_length=100, verbose_name='Distributor')),
            ],
        ),
    ]

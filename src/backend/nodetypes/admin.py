from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse

from nodetypes.models import NodeTypeResource, ActionResource
from admin_extra_buttons.mixins import ExtraButtonsMixin
from admin_extra_buttons.decorators import button
from jet.admin import CompactInline


class ExtraButtonsForceDelete(ExtraButtonsMixin):

    @button(
        change_form=True,
        html_attrs={
            "class": "deletelink",
            "style": "background-color:#c14747;color:white"
        },
        label=_("FORCE DELETE"),
    )
    def force_delete(self, request, pk):
        obj = self.get_object(request, pk)
        if request.user.is_superuser:
            messages.success(
                request,
                f"The Action Resource “{obj}” was FORCE Deleted successfully."
            )
            obj.delete(soft=False)
            return redirect(
                reverse(
                    'admin:%s_%s_changelist' % (
                        obj._meta.app_label, obj._meta.model_name
                    )
                )
            )
        else:
            messages.error(
                request, "You do not have permission to FORCE DELETE."
            )
            return redirect(request.META.get('HTTP_REFERER'))


class ActionResourceInlaine(CompactInline):

    model = ActionResource
    fields = (
        "is_deprecated",
    )
    readonly_fields = (
        "is_deprecated",
    )
    show_change_link = ("name",)
    extra = 0
    verbose_name_plural = "ActionResources"


@admin.register(NodeTypeResource)
class NodeTypeResourceAdmin(ExtraButtonsForceDelete, admin.ModelAdmin):
    list_display = (
        "is_loaded",
        "name",
        "is_available",
        "is_official",
        "is_deprecated"
    )
    inlines = (ActionResourceInlaine,)
    list_display_links = ("name",)
    readonly_fields = ("is_deprecated",)
    ordering = ('is_deprecated', "created_at")

    @admin.decorators.display(boolean=True, description=_("Is loaded"))
    def is_loaded(self, obj):
        return obj.is_loaded


@admin.register(ActionResource)
class ActionResourceAdmin(ExtraButtonsForceDelete, admin.ModelAdmin):

    list_display = (
        "__str__",
        "node_type",
        # "name",
        "is_available",
        "is_deprecated",
    )
    readonly_fields = ("is_deprecated",)
    ordering = ('is_deprecated', "created_at")

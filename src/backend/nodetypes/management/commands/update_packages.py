from django.core.management.base import BaseCommand

# from django.core.management import call_command
from nodetypes.models import NodeTypeResource
from django.conf import settings
import pkgutil


class Command(BaseCommand):
    help = """
    This command updates the NodeTypeResource objects
    for packages with names starting with the specified prefix.
    """

    def _find_matching_libraries(
        self, prefix: str, ignore: list = None, remove_prefix: bool = False
    ) -> list:
        matching_libraries = []

        for _, name, _ in pkgutil.iter_modules():
            if name.startswith(prefix + "_") and (ignore is None or name not in ignore):
                matching_libraries.append(
                    name if not remove_prefix else name[len(prefix + "_") :]
                )

        return matching_libraries

    def handle(self, prefix="symmy", *args, **kwargs):
        name_packages = self._find_matching_libraries(
            prefix, ignore=settings.IGNORE_PACKAGES, remove_prefix=True
        )

        try:
            for package in name_packages:
                try:
                    NodeTypeResource.objects.update_or_create(
                        prefix=prefix,
                        name=package,
                        defaults={
                            "is_available": True,
                            "is_official": True,
                            "is_deprecated": False,
                            "uri": "",
                            "is_public": False,
                        },
                    )
                except Exception as ex:
                    pass

            self.stdout.write(
                self.style.SUCCESS(f"UPDATED PACKAGES: ( {len(name_packages)} )")
            )
        except Exception as e:
            self.stdout.write(self.style.ERROR(str(e)))

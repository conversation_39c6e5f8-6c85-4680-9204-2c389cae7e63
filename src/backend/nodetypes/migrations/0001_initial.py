# Generated by Django 4.2.2 on 2023-12-20 04:10

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='NodeTypeResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('prefix', models.CharField(default='symmy_', max_length=50, verbose_name='Package Prefix')),
                ('name', models.Char<PERSON>ield(max_length=50, verbose_name='Internal Name')),
                ('uri', models.CharField(max_length=200, verbose_name='Package Location URI')),
                ('is_available', models.BooleanField(verbose_name='Is Available')),
                ('is_official', models.BooleanField(verbose_name='Official Support')),
                ('is_public', models.BooleanField(verbose_name='Is Public')),
            ],
            options={
                'verbose_name': 'Node Type Resource',
                'verbose_name_plural': 'Node Type Resources',
            },
        ),
        migrations.CreateModel(
            name='ActionResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('name', models.CharField(max_length=64, verbose_name='Method Name')),
                ('display_name', models.CharField(max_length=64, verbose_name='Display Name')),
                ('description', models.CharField(blank=True, max_length=256, null=True, verbose_name='Description')),
                ('is_available', models.BooleanField(verbose_name='Is Available')),
                ('webhook', models.BooleanField(default=False, verbose_name='Webhook')),
                ('node_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='actions', to='nodetypes.nodetyperesource')),
            ],
            options={
                'verbose_name': 'Action Resource',
                'verbose_name_plural': 'Action Resources',
            },
        ),
    ]

import logging
from importlib import import_module
from types import ModuleType
from typing import Type

from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from symmy_nodetype.nodetype.node import NodeType
from django.utils import timezone

from core.models import SymmyBaseModel

logger = logging.getLogger("symmy")


class SoftDeleteQuerySet(models.QuerySet):
    def delete(self, soft=True, *args, **kwargs):
        if soft:
            # Soft-delete instances
            self.update(
                is_deprecated=True,
                **kwargs,
            )
            self.soft_delete_related(**kwargs)
        else:
            # Delete from DB
            super().delete(*args, **kwargs)

    def soft_delete_related(self, **kwargs):
        pass


class NodeTypeResourceQuerySet(SoftDeleteQuerySet):
    def soft_delete_related(self, **kwargs):
        # Soft-delete related ActionResource instances
        for resource in self:
            resource.actions.update(
                is_deprecated=True,
                **kwargs,
            )


class NodeTypeResource(SymmyBaseModel):
    """Stores data about registered NodeType resources in Symmy."""

    prefix = models.CharField(_("Package Prefix"), default="symmy", max_length=50)
    name = models.CharField(_("Internal Name"), max_length=150)
    uri = models.CharField(_("Package Location URI"), max_length=200)

    is_available = models.BooleanField(_("Is Available"))
    is_official = models.BooleanField(_("Official Support"))
    is_public = models.BooleanField(_("Is Public"))

    is_deprecated = models.BooleanField(_("Is Deprecated"), default=False)

    objects = NodeTypeResourceQuerySet.as_manager()

    class Meta:
        verbose_name = _("Node Type Resource")
        verbose_name_plural = _("Node Type Resources")

    def delete(self, soft=True,  *args, **kwargs):
        if soft:
            # soft-delete instance
            related_actions = ActionResource.objects.filter(node_type=self)
            related_actions.update(is_deprecated=True)

            self.is_deprecated = True
            self.save()
        else:
            # delete from DB
            super().delete(*args, **kwargs)

    def save(self, **kwargs) -> None:
        if NodeTypeResource.objects.filter(
            pk=self.pk, is_deprecated=True
        ).exists():
            self.is_deprecated = False
        return super().save(**kwargs)

    @staticmethod
    def register(**kwargs):
        resource = NodeTypeResource.objects.create(**kwargs)

        # TODO: Tests whether the np follows our guidelines
        # TODO: Maybe override create method instead

    @cached_property
    def module(self) -> ModuleType | None:
        """
        Gets and returns the parent module
        referenced by this `NodeTypeResource` instance.
        """

        try:
            return import_module(self.module_string)
        except:
            logger.exception(
                f"Error importing module: {self.module_string}", exc_info=True
            )

        return None

    @cached_property
    def nodetype_class(self) -> Type[NodeType] | None:
        """
        Gets and returns the `Node` class
        referenced by this `NodeTypeResource` instance.
        """

        try:
            node_module = import_module(".node", f"{self.module_string}.{self.name}")

            return node_module.Node
        except:
            logger.exception(
                f"Error importing Node class: {self.module_string}.{self.name}",
                exc_info=True,
            )

        return None

    @property
    def module_string(self) -> str:
        """
        Returns the module name
        referenced by this `NodeTypeResource` instance.
        """

        return f"{self.prefix}_{self.name}"

    @cached_property
    def display_name(self) -> str:
        """Returns the display name of the model."""

        if self.is_loaded:
            return self.nodetype_class.name

        return self.name

    @cached_property
    def is_loaded(self) -> bool:
        """Returns whether the module has been loaded. False if not found."""
        return self.nodetype_class is not None

    def __str__(self) -> str:
        if self.is_deprecated:
            return f"{self.display_name} [is_deprecated]"
        return self.display_name


@receiver(post_save, sender=NodeTypeResource)
def node_type_resource_post_save(sender, instance, created, **kwargs):
    # Create or update the resource's actions
    if not instance.is_loaded or instance.is_deprecated:
        return

    actions = instance.nodetype_class.actions
    for action in actions:
        ActionResource.objects.update_or_create(
            name=action.__name__,
            node_type=instance,
            defaults={
                "display_name": action.name,
                "description": action.description,
                "is_available": instance.is_available,
                "is_deprecated": False,
                # FIXME
                # "webhook": action.webhook,
            },
        )


class ActionResource(SymmyBaseModel):
    name = models.CharField(_("Method Name"), max_length=150)
    display_name = models.CharField(_("Display Name"), max_length=150)
    description = models.CharField(
        _("Description"), blank=True, null=True, max_length=512
    )
    node_type = models.ForeignKey(
        NodeTypeResource, on_delete=models.CASCADE, related_name="actions"
    )

    is_available = models.BooleanField(_("Is Available"))
    webhook = models.BooleanField(_("Webhook"), default=False)

    is_deprecated = models.BooleanField(_("Is Deprecated"), default=False)

    objects = SoftDeleteQuerySet.as_manager()

    class Meta:
        verbose_name = _("Action Resource")
        verbose_name_plural = _("Action Resources")

    def delete(self, soft=True,  *args, **kwargs):
        if soft:
            # soft-delete instance
            self.is_deprecated = True
            self.save()
        else:
            # delete from DB
            super().delete(*args, **kwargs)

    def execute(self, data: dict, node: NodeType):
        action = node.get_action(action_name=self.name)
        if action is not None:
            return action(data)
        raise Exception(f"Action {self.name} was not found")

    def __str__(self) -> str:
        if self.is_deprecated:
            return f"{self.display_name} ({self.node_type}) [is_deprecated]"
        return f"{self.display_name} ({self.node_type})"

import logging
from functools import partial

from django.conf import settings
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.mail import EmailMessage
from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

from core.models import SymmyBaseModel
from users.managers import CustomUserManager
from users.media import FileValidator, MEGABYTE
from users.tokens import password_reset_token_generator
from users.utils import user_directory_path

logger = logging.getLogger("symmy")


class SymmyUser(SymmyBaseModel, AbstractBaseUser, PermissionsMixin):
    # Auth/Core details
    email = models.EmailField(_("Email Address"), unique=True)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # Profile details
    first_name = models.CharField(_("First Name"), max_length=100)
    last_name = models.CharField(_("Last Name"), blank=True, default="", max_length=100)
    avatar = models.ImageField(
        _("Avatar"),
        upload_to=partial(user_directory_path, subdir="avatars"),
        null=True,
        blank=True,
        validators=[
            FileValidator(
                max_size=5 * MEGABYTE,
                content_types=[
                    "image/jpeg",
                    "image/png",
                ],
            )
        ],
    )

    organizations = models.ManyToManyField(
        "core.Organization",
        related_name="users",
        through="core.OrganizationUser",
        verbose_name=_("Organizations"),
    )

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    class Meta:
        verbose_name = _("User")
        verbose_name_plural = _("Users")

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        if self.first_name and self.last_name:
            return f"{self.email} ({self.get_full_name()})"

        return self.email

    def get_password_reset_api_url(self) -> str:
        token = password_reset_token_generator.make_token(self)
        api_reset_url = reverse(
            f"api:v1:users:reset-password",
            kwargs={"uuid": self.uuid, "token": token},
        )
        return api_reset_url

    def get_password_reset_frontend_url(self) -> str:
        token = password_reset_token_generator.make_token(self)
        frontend_reset_url = f"reset-password/{self.uuid}/{token}"
        return frontend_reset_url

    def request_password_change(self):
        """
        Requests password change by sending an email with instructions to the user.

        Args:
            api_version (str): The api version on which the reset endpoint is.
        """

        reset_url = self.get_password_reset_frontend_url()
        email = EmailMessage(
            subject="Symmy: Forgot Password",
            body=f"{settings.BASE_URL}{reset_url}",
            to=[self.email],
        )
        email.send(fail_silently=True)

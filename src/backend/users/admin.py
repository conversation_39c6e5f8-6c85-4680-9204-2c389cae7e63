from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _

from users.forms import SymmyUserCreationForm
from users.models import SymmyUser


class OrganizationUserInline(admin.StackedInline):
    model = SymmyUser.organizations.through
    extra = 1
    verbose_name = _("Organization")
    verbose_name_plural = _("Organizations")


@admin.register(SymmyUser)
class SymmyUserAdmin(UserAdmin):
    add_form = SymmyUserCreationForm
    model = SymmyUser
    list_display = (
        "email",
        "first_name",
        "last_name",
        "is_staff",
        "is_active",
    )
    list_filter = (
        "email",
        "is_staff",
        "is_active",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "email",
                    "first_name",
                    "last_name",
                    "password",
                    "avatar",
                )
            },
        ),
        (
            "Permissions",
            {"fields": ("is_staff", "is_active")},
        ),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "first_name",
                    "last_name",
                    "password1",
                    "password2",
                    "is_staff",
                    "is_active",
                ),
            },
        ),
    )
    search_fields = ("email",)
    ordering = ("email",)
    list_display_links = ("email",)
    inlines = (OrganizationUserInline,)

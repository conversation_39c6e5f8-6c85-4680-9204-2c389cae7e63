import logging

from django.http import Http404
from rest_framework import generics, permissions, request
from rest_framework import status
from rest_framework.response import Response
from knox.auth import AuthToken
from knox import views as knox_views
from drf_spectacular.utils import (
    extend_schema_view,
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
)
from drf_spectacular.types import OpenApiTypes

from users.api.v1.serializers import (
    UserSerializer,
    AuthSerializer,
    AuthResponseSerializer,
    EmailToUserSerializer,
    ResetPasswordSerializer,
    UserDataSerializer,
    UserAvatarSerializer,
)
from users.models import SymmyUser
from users.tokens import password_reset_token_generator
from api.schemes import KnoxTokenScheme  # noqa

logger = logging.getLogger("symmy")


@extend_schema_view(
    post=extend_schema(
        operation_id="Logout",
        description="Logs the user out, i.e. deletes the provided token",
        responses={
            204: "No response body",
        },
    )
)
class LogoutView(knox_views.LogoutView):
    """`knox.views.LogoutView` override in order to document it."""


@extend_schema_view(
    post=extend_schema(
        operation_id="Logout All",
        description="Logs the user out of all sessions, i.e. deletes all tokens associated with the user",
        responses={
            204: "No response body",
        },
    )
)
class LogoutAllView(knox_views.LogoutAllView):
    """`knox.views.LogoutAllView` override in order to document it."""


@extend_schema_view(
    post=extend_schema(
        operation_id="Authentication",
        description="Authenticates the user.",
        responses={
            200: AuthResponseSerializer,
            400: OpenApiResponse(description="User credentials are invalid"),
        },
    )
)
class AuthView(generics.GenericAPIView):
    serializer_class = AuthSerializer
    response_serializer_class = AuthResponseSerializer

    def post(self, request: request.Request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data

        response_serializer = self.response_serializer_class(
            {
                "user": user,
                "token": AuthToken.objects.create(user=user)[1],
            }
        )

        return Response(
            data=response_serializer.data,
            status=status.HTTP_200_OK,
        )


@extend_schema_view(
    get=extend_schema(
        operation_id="User Details",
        description="Shows user details.",
        responses={
            200: UserSerializer,
            404: OpenApiResponse(description="Unauthorized"),
        },
    )
)
class UserView(generics.RetrieveAPIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserSerializer

    def get_object(self) -> SymmyUser:
        return self.request.user


@extend_schema_view(
    post=extend_schema(
        operation_id="Change Password",
        description=(
            "Sends a password reset link to the email of the authenticated user."
        ),
        responses={
            204: OpenApiResponse(description="No response body"),
        },
    )
)
class ChangePasswordView(generics.GenericAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self) -> SymmyUser:
        return self.request.user

    def post(self, request: request.Request):
        user = self.get_object()
        user.request_password_change()

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema_view(
    post=extend_schema(
        operation_id="Forgot Password",
        description=(
            "Sends a password reset link to the email provided "
            "(only if a user with said email exists)."
        ),
        responses={
            204: OpenApiResponse(description="No response body"),
        },
    )
)
class ForgotPasswordView(generics.GenericAPIView):
    serializer_class = EmailToUserSerializer

    def get_serializer(self, *args, **kwargs) -> serializer_class:
        return super().get_serializer(*args, **kwargs)

    def post(self, request: request.Request):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            user: SymmyUser = serializer.validated_data
            user.request_password_change()
        except Exception as e:
            logger.exception(e)

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema_view(
    patch=extend_schema(
        operation_id="Reset Password",
        description="Sets a new password to the user.",
        parameters=[
            OpenApiParameter(
                name="uuid",
                type=OpenApiTypes.UUID,
                description="UUID of the user.",
                required=True,
                location="path",
            ),
            OpenApiParameter(
                name="token",
                type=OpenApiTypes.STR,
                description="Token generated on `forgot password` or `change password` request.",
                required=True,
                location="path",
            ),
        ],
        responses={
            204: OpenApiResponse(description="No response body"),
            400: OpenApiResponse(
                description=(
                    "The token or the UUID is invalid, "
                    "or the password doesn't meet our security standards"
                )
            ),
        },
    )
)
class ResetPasswordView(generics.UpdateAPIView):
    lookup_field = "uuid"
    queryset = SymmyUser.objects.all()
    serializer_class = ResetPasswordSerializer
    http_method_names = ["patch"]

    def partial_update(self, request: request.Request, token: str, **kwargs):
        try:
            user = self.get_object()
        except Http404:
            user = None

        if not password_reset_token_generator.check_token(user=user, token=token):
            return Response(
                {"detail": "UUID or token are invalid, or the token is expired."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(instance=user, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema_view(
    patch=extend_schema(
        operation_id="Update User Data",
        description="Updates user's data.",
        responses={
            204: OpenApiResponse(description="No response body"),
            400: OpenApiResponse(description="Validation on the data has failed"),
        },
    )
)
class UpdateDataView(generics.UpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserDataSerializer
    http_method_names = ["patch"]

    def get_object(self) -> SymmyUser:
        return self.request.user

    def partial_update(self, request, *args, **kwargs):
        super().partial_update(request, *args, **kwargs)

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema_view(
    patch=extend_schema(
        operation_id="Update User Avatar",
        description="Updates user's avatar.",
        responses={
            204: OpenApiResponse(description="No response body"),
            400: OpenApiResponse(description="Validation on the file has failed"),
        },
    )
)
class UpdateAvatarView(generics.UpdateAPIView):
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = UserAvatarSerializer
    http_method_names = ["patch"]

    def get_object(self) -> SymmyUser:
        return self.request.user

    def partial_update(self, request, *args, **kwargs):
        super().partial_update(request, *args, **kwargs)

        return Response(status=status.HTTP_204_NO_CONTENT)


@extend_schema_view(
    delete=extend_schema(
        operation_id="Remove User Avatar",
        description="Deletes the user's avatar.",
    )
)
class RemoveAvatarView(generics.DestroyAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self) -> SymmyUser:
        return self.request.user

    def perform_destroy(self, instance: SymmyUser):
        instance.avatar = None
        instance.save()

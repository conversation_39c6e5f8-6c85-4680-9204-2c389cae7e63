import uuid
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from knox.auth import AuthToken
from knox.settings import knox_settings

from api.test import test_not_allowed_methods
from users.models import SymmyUser
from users.tokens import password_reset_token_generator


class TestAPI(APITestCase):
    EMAIL = "<EMAIL>"
    FICTIONAL_EMAIL = "<EMAIL>"

    PASSWORD = "Secure P4ssw0rd"
    INCORRECT_PASSWORD = "Inc0rr3ct P4ssw0rd"

    user: SymmyUser

    @classmethod
    def setUpTestData(cls) -> None:
        cls.user = SymmyUser.objects.create_user(email=cls.EMAIL, password=cls.PASSWORD)

        cls.auth_client = APIClient()
        cls.auth_client.force_authenticate(cls.user)

    def test_authenticate(self) -> None:
        url = reverse("api:v1:users:authenticate")

        # Correct input
        data = {"email": self.EMAIL, "password": self.PASSWORD}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("user", response.data)
        self.assertIn("token", response.data)
        self.assertEqual(response.data["user"]["uuid"], str(self.user.uuid))

        # Incorrect password
        data = {"email": self.EMAIL, "password": self.INCORRECT_PASSWORD}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Non-existant user
        data = {"email": self.FICTIONAL_EMAIL, "password": self.PASSWORD}
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        test_not_allowed_methods(self, self.client, url, allowed_methods={"post"})

    def test_user_detail(self) -> None:
        url = reverse("api:v1:users:user-detail")

        # Authorized
        response = self.auth_client.get(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("email", response.data)
        self.assertIn("uuid", response.data)
        self.assertEqual(response.data["email"], self.user.email)
        self.assertEqual(response.data["uuid"], str(self.user.uuid))

        # Unauthorized
        response = self.client.get(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"get"})

    def test_logout(self) -> None:
        url = reverse("api:v1:users:logout")
        token = AuthToken.objects.create(user=self.user)[1]

        token_client = APIClient()
        token_client.credentials(
            HTTP_AUTHORIZATION=f"{knox_settings.AUTH_HEADER_PREFIX} {token}"
        )

        # Authorized
        response = token_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(AuthToken.objects.filter(user=self.user))

        # Unauthorized
        response = token_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"post"})

    def test_logout_all(self) -> None:
        url = reverse("api:v1:users:logout-all")
        AuthToken.objects.create(user=self.user)
        AuthToken.objects.create(user=self.user)
        # XXX AuthTokens cannot be created in bulk:
        # IntegrityError - some constraint fails

        # Authorized
        response = self.auth_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(AuthToken.objects.filter(user=self.user))

        # Unauthorized
        response = self.client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"post"})

    def test_change_password(self) -> None:
        url = reverse("api:v1:users:change-password")

        # Authorized
        response = self.auth_client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # Unauthorized
        response = self.client.post(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"post"})

    def test_forgot_password(self) -> None:
        url = reverse("api:v1:users:forgot-password")

        data = {"email": self.EMAIL}
        response = self.client.post(url, data=data, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"post"})

    def test_reset_password(self) -> None:
        url = self.user.get_password_reset_api_url()
        AuthToken.objects.create(user=self.user)
        AuthToken.objects.create(user=self.user)

        # Correct input
        data = {
            "logout": False,
            "password1": self.INCORRECT_PASSWORD,
            "password2": self.INCORRECT_PASSWORD,
        }
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password(self.INCORRECT_PASSWORD))
        self.assertTrue(AuthToken.objects.filter(user=self.user))

        url = self.user.get_password_reset_api_url()
        # Must reset because token validity depends on password hash which has changed
        data = {
            "logout": True,
            "password1": self.PASSWORD,
            "password2": self.PASSWORD,
        }
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password(self.PASSWORD))
        self.assertFalse(AuthToken.objects.filter(user=self.user))

        # Incorrect passwords
        url = self.user.get_password_reset_api_url()
        data = {
            "logout": False,
            "password1": self.PASSWORD,
            "password2": self.INCORRECT_PASSWORD,
        }
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        data = {
            "logout": False,
            "password1": self.INCORRECT_PASSWORD,
            "password2": self.PASSWORD,
        }
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Incorrect UUID
        token = password_reset_token_generator.make_token(self.user)
        uuid4 = uuid.uuid4()
        url = reverse(
            "api:v1:users:reset-password",
            kwargs={"uuid": uuid4, "token": token},
        )
        data = {
            "password1": self.PASSWORD,
            "password2": self.PASSWORD,
        }
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Incorrect token
        temp = SymmyUser.objects.create()
        token = password_reset_token_generator.make_token(temp)
        url = reverse(
            "api:v1:users:reset-password",
            kwargs={"uuid": self.user.uuid, "token": token},
        )
        response = self.auth_client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        url = self.user.get_password_reset_api_url()

        test_not_allowed_methods(self, self.auth_client, url, allowed_methods={"patch"})

    # TODO: Other API methods (avatar related --- were not as important as previous ones)

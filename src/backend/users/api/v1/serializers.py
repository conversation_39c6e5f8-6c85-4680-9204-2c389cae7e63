from django.contrib.auth import password_validation
from django.contrib.auth import authenticate
from rest_framework import serializers
from knox.models import AuthToken

from api.fields import KnoxTokenField, PasswordField
from users.models import SymmyUser


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = SymmyUser
        fields = [
            "uuid",
            "email",
            "first_name",
            "last_name",
            "avatar",
        ]


class AuthSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = PasswordField()

    def validate(self, data):
        user = authenticate(**data)
        if user is not None:
            return user

        raise serializers.ValidationError("Incorrect user credentials.")


class AuthResponseSerializer(serializers.Serializer):
    user = UserSerializer()
    token = KnoxTokenField()


class EmailToUserSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate(self, data):
        return SymmyUser.objects.get(
            **data
        )  # TODO: Do some time benchmark tests for security


class ResetPasswordSerializer(serializers.ModelSerializer):
    password1 = PasswordField(write_only=True)
    password2 = PasswordField(write_only=True)
    logout = serializers.BooleanField(
        default=True, initial=True
    )  # Indicates whether to logout all when password is updated

    class Meta:
        model = SymmyUser
        fields = ["password1", "password2", "logout"]

    def validate(self, data):
        password1 = data.get("password1")
        password2 = data.get("password2")

        if password1 != password2:
            raise serializers.ValidationError(
                {"password2": "The passwords don't match."}
            )

        return data

    def validate_password1(self, password):
        password_validation.validate_password(password, self.instance)

        return password

    def update(self, instance, validated_data):
        instance.set_password(validated_data["password1"])
        instance.save()

        if validated_data["logout"]:
            AuthToken.objects.filter(user=instance).delete()

        return instance


class UserDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = SymmyUser
        fields = ["first_name", "last_name"]


class UserAvatarSerializer(serializers.ModelSerializer):
    class Meta:
        model = SymmyUser
        fields = ["avatar"]

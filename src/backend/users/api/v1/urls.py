from django.urls import path

from users.api.v1.views import (
    AuthView,
    LogoutView,
    LogoutAllView,
    UserView,
    ForgotPasswordView,
    ResetPasswordView,
    ChangePasswordView,
    UpdateDataView,
    UpdateAvatarView,
    RemoveAvatarView,
)

app_name = "users"

urlpatterns = [
    # Authentication & User Details
    path("user/", UserView.as_view(), name="user-detail"),
    path("authenticate/", AuthView.as_view(), name="authenticate"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("logout-all/", LogoutAllView.as_view(), name="logout-all"),
    # Password Change
    path("change-password/", ChangePasswordView.as_view(), name="change-password"),
    path("forgot-password/", ForgotPasswordView.as_view(), name="forgot-password"),
    path(
        "reset-password/<uuid:uuid>/<str:token>/",
        ResetPasswordView.as_view(),
        name="reset-password",
    ),
    # Profile
    path("update-data/", UpdateDataView.as_view(), name="update-data"),
    path("update-avatar/", UpdateAvatarView.as_view(), name="update-avatar"),
    path("remove-avatar/", RemoveAvatarView.as_view(), name="remove-avatar"),
]

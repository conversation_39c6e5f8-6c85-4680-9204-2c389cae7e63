import pathlib

from django.conf import settings

from core.models import Organization, OrganizationUser, Project
from core.utils import suuid


def user_directory_path(instance, filename: str, subdir: str = "other") -> str:
    """
    Function that generates path of an uploaded file.
    File will be uploaded to MEDIA_ROOT/uploads/user_<pk>/<subdir>/<random short uuid>.

    Use like this:

    submission_file = models.FileField(
        upload_to=partial(user_directory_path, subdir="submissions")
    )
    """

    ext: str = pathlib.Path(filename).suffix
    filename: str = f"{suuid()}{ext}"

    try:
        user_id = f"user_{instance.pk}"
    except AttributeError:
        user_id = "user_none"

    return f"uploads/{user_id}/{subdir}/{filename}"


def generate_default_user_relations(user) -> None:
    """
    Generates default objects related to user: Organization and Project.
    This function is called in the create_user() manager method.
    Args:
        user (SymmyUser): SymmyUser instance.

    Returns: None
    """

    organization = Organization.objects.create(
        name=settings.DEFAULT_ORGANIZATION_NAME, email=user.email
    )
    OrganizationUser.objects.create(
        user=user, organization=organization, role=OrganizationUser.RoleChoices.OWNER
    )
    Project.objects.create(
        name=settings.DEFAULT_PROJECT_NAME, user=user, organization=organization
    )

    user.organizations.add(organization)
    user.save()

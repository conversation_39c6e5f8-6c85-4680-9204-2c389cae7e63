from django.conf import settings
from django.contrib.auth import get_user_model
from django.test import TestCase

from core.models import Organization, Project, OrganizationUser


class UserManagerTests(TestCase):
    def test_create_user(self):
        User = get_user_model()
        user = User.objects.create_user(email="<EMAIL>", password="foo")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(
            Organization.objects.filter(
                name=settings.DEFAULT_ORGANIZATION_NAME, users=user
            ).exists()
        )
        organization = Organization.objects.get(
            name=settings.DEFAULT_ORGANIZATION_NAME, users=user
        )
        self.assertTrue(
            OrganizationUser.objects.filter(
                user=user,
                organization=organization,
                role=OrganizationUser.RoleChoices.OWNER,
            ).exists()
        )
        self.assertTrue(
            Project.objects.filter(
                user=user, organization=organization, name=settings.DEFAULT_PROJECT_NAME
            ).exists()
        )
        try:
            # username is None for the AbstractUser option
            # username does not exist for the AbstractBaseUser option
            self.assertIsNone(user.username)
        except AttributeError:
            pass
        with self.assertRaises(TypeError):
            User.objects.create_user()
        with self.assertRaises(TypeError):
            User.objects.create_user(email="")
        with self.assertRaises(ValueError):
            User.objects.create_user(email="", password="foo")

    def test_create_superuser(self):
        User = get_user_model()
        admin_user = User.objects.create_superuser("<EMAIL>", "foo")
        self.assertEqual(admin_user.email, "<EMAIL>")
        self.assertTrue(admin_user.is_active)
        self.assertTrue(admin_user.is_staff)
        self.assertTrue(admin_user.is_superuser)
        try:
            # username is None for the AbstractUser option
            # username does not exist for the AbstractBaseUser option
            self.assertIsNone(admin_user.username)
        except AttributeError:
            pass
        with self.assertRaises(ValueError):
            User.objects.create_superuser(
                email="<EMAIL>", password="foo", is_superuser=False
            )

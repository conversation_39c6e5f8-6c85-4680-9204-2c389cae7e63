from django.core.mail import get_connection
from django.template.loader import render_to_string
from .message import SymmyEmailMultiAlternatives
from django.conf import settings


def get_html_message(
    template_name: str = "",
    template_language: str = settings.LANGUAGE_CODE,
    template_context: dict = {},
):
    pass


def send_mail(
    subject,
    message,
    from_email,
    recipient_list,
    fail_silently=False,
    template_name: str = "",
    template_language: str = settings.LANGUAGE_CODE,
    template_context: dict = {},
    # html_message=None,
):
    """
    Easy wrapper for sending a single message to a recipient list. All members
    of the recipient list will see the other recipients in the 'To' field.

    If from_email is None, use the DEFAULT_FROM_EMAIL setting.
    If auth_user is None, use the EMAIL_HOST_USER setting.
    If auth_password is None, use the EMAIL_HOST_PASSWORD setting.

    Note: The API for this method is frozen. New code wanting to extend the
    functionality should use the EmailMessage class directly.
    """

    html_message = render_to_string(template_name, template_context)

    connection = get_connection(
        # username=auth_user,
        # password=auth_password,
        fail_silently=fail_silently,
    )
    mail = SymmyEmailMultiAlternatives(
        subject, message, from_email, recipient_list, connection=connection,
        template_name=template_name,
        template_context=template_context,
        template_language=template_language
    )
    if html_message:
        mail.attach_alternative(html_message, "text/html")

    return mail.send()

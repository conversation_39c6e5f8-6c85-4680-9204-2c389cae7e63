from rest_framework import serializers
# from django.utils.translation import gettext_lazy as _
from django.conf.global_settings import LANGUAGES
from rest_framework.fields import Field
from django.conf import settings

import base64


class EmailListField(serializers.ListField):
    child = serializers.EmailField()


class Base64FileField(Field):
    """
    Pass "file-like" object to this field.
    You can call .read() on this object type.
    BytesIO is here to your help.
    """

    def to_internal_value(self, value):
        return value

    def to_representation(self, value):
        try:
            file = open(value.file.path_full, "rb")
            encoded_file = base64.b64encode(file.read())
        except AttributeError as e:
            print("[!] Supplied variable is not Attachment, cannot be read.")
            raise e
        return encoded_file


class AttachmentsSerializer(serializers.Serializer):
    """
    If you have problems with passing attachments, are you sending right data?
    This should help you!
    [{"name": attach.name, "file": attach} for attach in self.attachments]
    """
    name = serializers.CharField(max_length=128)
    file = serializers.Base64FileField()


class MailSerializer(serializers.Serializer):
    """
    email_to = CharField(max_length=256)
    recipient = ForeignKey(
        'user.Profile', verbose_name=_("recipient"), on_delete=SET_NULL,
        related_name="received_mails", null=True, blank=True
    )
    subject = CharField(max_length=512)
    message = TextField(default="", blank=True)
    html_message = TextField(null=True, blank=True)
    created = DateTimeField(default=timezone.now, editable=False)
    sent = BooleanField(default=False)
    """
    subject = serializers.CharField(max_length=998)
    sender_address = serializers.EmailField(required=True)
    recipient_list = serializers.EmailListField()
    smtpconfig = serializers.CharField(
        max_length=32, default=settings.WMAIL_SMTP_TOKEN
    )
    body = serializers.CharField(required=False)
    template_name = serializers.CharField(required=False)
    template_language = serializers.ChoiceField(
        choices=LANGUAGES, required=False, default=settings.LANGUAGE_CODE
    )
    source_domain = serializers.CharField(required=True, allow_blank=True)
    template_context = serializers.DictField(required=False)
    attachments = serializers.AttachmentsSerializer(many=True, required=False)

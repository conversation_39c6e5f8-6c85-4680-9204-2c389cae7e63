# Generated by Django 4.2.2 on 2025-05-26 15:28

from django.db import migrations, models
import mails.models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('mails', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mail',
            name='sender_address',
            field=models.EmailField(default='<EMAIL>', max_length=254),
        ),
        migrations.AlterField(
            model_name='mail',
            name='template_name',
            field=models.CharField(blank=True, default='', max_length=254, validators=[mails.models.validator_template_name]),
        ),
        migrations.Alter<PERSON>ield(
            model_name='mail',
            name='uuid',
            field=models.UUIDField(db_index=True, default=uuid.uuid4, editable=False, unique=True, verbose_name='UUID'),
        ),
    ]

from django.test import TestCase, override_settings
# from django.conf import settings
from .models import Mail
from .tasks import send_email_async, send_email_instance


@override_settings(
    EMAIL_DEBUG_SEND=True,
    EMAIL_BACKEND="mails.backends.smtp.SymmyEmailBackend"
)
class EmailSyncTests(TestCase):

    def test_base_send_mail(self):
        content = {
            "subject": "Subject Test",
            "message": "Message Test",
            "recipient_list": [
                "<EMAIL>", "<EMAIL>"
            ],
        }
        send_email_async(**content)

        mail_obj = Mail.objects.last()

        self.assertEqual(mail_obj.subject, content["subject"])
        self.assertEqual(mail_obj.body, content["message"])
        self.assertEqual(mail_obj.recipient_list, content["recipient_list"])
        self.assertEqual(mail_obj.status, mail_obj.status_choices.SENT)

    def test_send_mail_correct_template(self):
        content = {
            "subject": "Subject Test",
            "message": "Message Test",
            "recipient_list": ["<EMAIL>", "<EMAIL>"],
            "template_name": "test_mail.html",
            "template_context": {
                "language_code": "en",
                "heading": "Test Mail System",
                "content": "Test Content",
                "sender": "Test Chelik",
            },
        }
        send_email_async(**content)

        mail_obj = Mail.objects.last()

        self.assertEqual(mail_obj.subject, content["subject"])
        self.assertEqual(mail_obj.body, content["message"])
        self.assertEqual(mail_obj.recipient_list, content["recipient_list"])
        self.assertEqual(mail_obj.template_name, content["template_name"])
        self.assertEqual(
            mail_obj.template_context, content["template_context"]
        )
        self.assertEqual(mail_obj.status, mail_obj.status_choices.SENT)

    def test_send_instance_mail(self):
        mail_obj = Mail.objects.create(
            **{
                "subject": "Subject Test",
                "body": "Message Test",
                "recipient_list": [
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "template_name": "test_mail.html",
                "template_context": {
                    "language_code": "en",
                    "heading": "Test Mail System",
                    "content": "Test Content",
                    "sender": "Test Chelik",
                },
            }
        )

        send_email_instance(mail_obj.id)
        self.assertEqual(
            Mail.objects.last().status, mail_obj.status_choices.SENT
        )

    def test_instance_mail_status_fail(self):
        mail_obj = Mail.objects.create(
            **{
                "subject": "Subject Test",
                "body": "Message Test",
                "recipient_list": [
                    "<EMAIL>",
                    "<EMAIL>"
                ],
                "template_name": "NOT_FOUND",
                "template_context": {
                    "language_code": "en",
                    "heading": "Test Mail System",
                    "content": "Test Content",
                    "sender": "Test Chelik",
                },
            }
        )
        try:
            send_email_instance(mail_obj.id)
        except Exception:
            pass
        self.assertEqual(
            Mail.objects.last().status, mail_obj.status_choices.FAILED
        )

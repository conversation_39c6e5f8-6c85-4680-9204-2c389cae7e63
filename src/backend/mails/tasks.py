from mails.mail import SymmyEmailMultiAlternatives
from django.core.mail import get_connection
from django.template.loader import render_to_string, get_template
from django.template.exceptions import TemplateDoesNotExist
from django.conf import settings
from symmy.celery import app
from django.apps import apps

redis_client = app.backend.client


def method_with_lock(func):
    def wrapper(instance, *args, **kwargs):
        lock_key = f"{instance.__class__.__name__}_{instance.id}_lock"
        lock = redis_client.lock(lock_key)
        if lock.acquire(blocking=False):
            try:
                return func(instance, *args, **kwargs)
            finally:
                lock.release()
        else:
            print(
                f"Method {func.__name__} for instance {instance.id} is already running"
            )
    return wrapper


# def validator_template_name(func):
#     def wrapper(*args, **kwargs):
#         template_name = kwargs.get('template_name')
#         if template_name:
#             get_template(template_name)
#         return func(*args, **kwargs)
#     return wrapper


# @validator_template_name
@app.task
def send_email_async(
    subject: str,
    message: str,
    recipient_list: [str],
    from_email=None,
    template_name: str = "",
    template_language: str = settings.LANGUAGE_CODE,
    template_context: dict = {},
) -> None:
    """
    Asynchronously sends an email.

    Parameters:
        - subject (str): The subject of the email.
        - message (str): The plain text content of the email.
        - recipient_list (list): A list of recipient email addresses.
        - from_email (str, optional): The sender's email address.
            Defaults to None.
        - template_name (str, optional): The name of the email template.
            Defaults to "".
        - template_language (str, optional): The language code for the template.
            Defaults to settings.LANGUAGE_CODE.
        - template_context (dict, optional): The context data for rendering the email template.
            Defaults to {}.

    Returns:
        - None

    This function sends an email asynchronously using Celery.
    It constructs the email message using the provided parameters
    and optionally renders an email template if template_name is provided.
    The HTML version of the email is attached if a template is rendered.
    The email is sent using the SymmyEmailMultiAlternatives class.
    """
    html_message = None
    if template_name:
        html_message = render_to_string(template_name, template_context)

    connection = get_connection(
        fail_silently=False,
    )
    mail = SymmyEmailMultiAlternatives(
        subject,
        message,
        from_email,
        recipient_list,
        connection=connection,
        template_name=template_name,
        template_context=template_context,
        template_language=template_language,
    )

    if html_message:
        mail.attach_alternative(html_message, "text/html")

    mail.send()


@app.task
def send_email_instance(pk: int) -> None:
    """
    Asynchronously sends an email using an instance of the Mail model.

    Parameters:
    - pk (int): The primary key of the Mail instance to send.

    Returns:
    - None

    This function retrieves an instance of the Mail model using
    the provided primary key and then calls the send_mail() method
    on that instance to send the email.
    It is intended to be used with Celery for asynchronous execution.
    """
    Mail = apps.get_model("mails", "Mail")
    instance = Mail.objects.get(pk=pk)
    instance.send_mail()

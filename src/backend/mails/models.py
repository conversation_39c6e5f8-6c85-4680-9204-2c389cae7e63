import json

from django.conf import settings
from django.conf.global_settings import LANGUAGES
from django.contrib.postgres.fields import <PERSON><PERSON>yField
from django.core.exceptions import ValidationError
from django.core.mail import get_connection
from django.db import models
from django.template.exceptions import TemplateDoesNotExist
from django.template.loader import get_template
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _

from core.models import SymmyBaseModel
from .message import SymmyEmailMultiAlternatives
from .tasks import method_with_lock


class MailStatus:
    CREATED = "CREATED"
    SENDING = "SENDING"
    SENT = "SENT"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"

    @classmethod
    def choices(cls) -> tuple:
        return (
            (cls.CREATED, "Created"),
            (cls.SENDING, "Sending"),
            (cls.SENT, "Sent"),
            (cls.FAILED, "Failed"),
            (cls.SKIPPED, "Skipped"),
        )


def validator_str_json(value) -> None:
    if isinstance(value, str):
        try:
            json.loads(value.replace("'", '"'))
        except json.JSONDecodeError as e:
            raise ValidationError(f"JSONDecodeError({str(e)})")


def validator_template_name(value) -> None:
    try:
        get_template(value)
    except TemplateDoesNotExist as e:
        raise ValidationError(f"Template '{e}' Does Not Exist")


class Mail(SymmyBaseModel):

    status_choices = MailStatus

    subject = models.CharField(max_length=998)
    sender_address = models.EmailField(default=settings.DEFAULT_FROM_EMAIL)
    recipient_list = ArrayField(
        models.EmailField(),
        help_text=_("separation by ','")
    )
    body = models.TextField(blank=True, default="")
    template_name = models.CharField(
        max_length=254, blank=True, default="",
        validators=[validator_template_name]
    )
    template_language = models.CharField(
        choices=LANGUAGES, default=settings.LANGUAGE_CODE, max_length=12
    )
    template_context = models.JSONField(
        default=dict,
        blank=True,
        validators=[validator_str_json],
    )

    tries = models.IntegerField(default=0)
    status = models.CharField(
        choices=MailStatus.choices(), default=MailStatus.CREATED, max_length=12
    )
    status_text = models.TextField(
        blank=True, default="",
        help_text=_("Last output of sending the email.")
    )

    comment = models.CharField(
        max_length=254, blank=True, default="",
        help_text=_("Just any message you want")
    )

    class Meta:
        verbose_name = _("Mail")
        verbose_name_plural = _("Mails")

    def __str__(self) -> str:
        return f"[{self.id}] {self.subject}@{self.template_name} \
            ({self.recipients_display})"

    @property
    def recipients_display(self) -> str:
        recipients = ", ".join(self.recipient_list)
        if len(recipients) > 25:
            recipients = recipients[:25] + "..."
        return recipients

    @property
    def is_can_send(self) -> bool:
        if self.status in [
            self.status_choices.SENT,
            self.status_choices.FAILED
        ]:
            return False
        return True

    @property
    def is_can_cange(self) -> bool:
        if self.status == self.status_choices.CREATED:
            return True
        return False

    @method_with_lock
    def send_mail(self):
        self.status = self.status_choices.SENDING
        self.save()
        try:
            html_message = None
            if self.template_name:
                html_message = render_to_string(
                    self.template_name, self.template_context
                )

            connection = get_connection(fail_silently=False)
            mail = SymmyEmailMultiAlternatives(
                to=self.recipient_list,
                connection=connection,
                instance=self,
            )
            if html_message:
                mail.attach_alternative(html_message, "text/html")
            return mail.send()
        except Exception as e:
            self.status = self.status_choices.FAILED
            self.status_text = str(e)
            self.save()
            raise e

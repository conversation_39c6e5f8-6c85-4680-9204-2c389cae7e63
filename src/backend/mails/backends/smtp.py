import smtplib

from django.conf import settings
from django.core.mail.backends.smtp import EmailBackend
from django.core.mail.message import sanitize_address

from mails.models import MailStatus


class SymmyEmailBackend(EmailBackend):

    def _send(self, email_message):
        """A helper method that does the actual sending."""
        if not email_message.recipients():
            return False
        encoding = email_message.encoding or settings.DEFAULT_CHARSET
        from_email = sanitize_address(email_message.from_email, encoding)
        recipients = [
            sanitize_address(addr, encoding) for addr in email_message.recipients()
        ]
        message = email_message.message()

        try:
            if not settings.EMAIL_DEBUG_SEND:
                self.connection.sendmail(
                    from_email, recipients, message.as_bytes(linesep="\r\n")
                )
            email_message.instance.status = MailStatus.SENT
            email_message.instance.tries += 1
            email_message.instance.save()
            pass
        except smtplib.SMTPException as e:
            email_message.instance.status = (MailStatus.FAILED,)
            email_message.instance.status_text = str(e)
            email_message.instance.save()
            if not self.fail_silently:
                raise
            return False

        return True

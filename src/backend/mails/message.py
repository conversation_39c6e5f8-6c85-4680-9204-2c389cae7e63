from django.core.mail.message import EmailMultiAlternatives
from django.conf import settings
from django.apps import apps


class TemplateEmail:
    def __init__(self, *args,
                 template_name: str = "",
                 template_language: str = settings.LANGUAGE_CODE,
                 template_context: dict = {},
                 **kwargs):
        super().__init__(*args, **kwargs)
        self.template_name = template_name
        self.template_language = template_language
        self.template_context = template_context


class SymmyEmailMultiAlternatives(TemplateEmail, EmailMultiAlternatives):
    """
    A version of EmailMessage that makes it easy to send multipart/alternative
    messages. For example, including text and HTML versions of the text is
    made easier.
    """

    def __init__(self, *args,
                 instance=None,
                 **kwargs):
        super().__init__(*args, **kwargs)
        self.instance = instance

    def message(self):
        self.set_instance()
        msg = super().message()
        self.create_instance()
        return msg

    def set_instance(self) -> None:
        if self.instance:
            self.subject = self.instance.subject
            self.from_email = self.instance.sender_address
            self.to = self.instance.recipient_list
            self.body = self.instance.body
            self.template_name = self.instance.template_name
            self.template_language = self.instance.template_language
            self.template_context = self.instance.template_context

    def create_instance(self) -> None:
        if not self.instance:
            Mail = apps.get_model('mails', 'Mail')
            self.instance = Mail.objects.create(
                subject=self.subject,
                sender_address=self.from_email,
                recipient_list=self.to,
                body=self.body,
                template_name=self.template_name,
                template_language=self.template_language,
                template_context=self.template_context,
                tries=1
            )

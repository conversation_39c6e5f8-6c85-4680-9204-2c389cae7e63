import json

from admin_extra_buttons.decorators import button
from admin_extra_buttons.mixins import ExtraButtonsMixin
from django.contrib import admin
from django.contrib import messages
from django.db import models
from django.shortcuts import redirect
from django.urls import reverse
from django.utils.html import format_html
from django.utils.http import urlencode
from django.utils.translation import gettext_lazy as _
from django_json_widget.widgets import JSONEditorWidget

from .models import Mail, MailStatus
from .tasks import send_email_instance


@admin.register(Mail)
class MailAdmin(ExtraButtonsMixin, admin.ModelAdmin):

    formfield_overrides = {
        models.JSONField: {
            "widget": JSONEditorWidget(
                attrs={"style": "height:250px;max-width:700px;display:flex;"}
            )
        },
    }
    list_display = (
        "get_html_view_status",
        "__str__",
    )
    list_display_links = ("__str__",)
    readonly_fields = ("tries",)
    custom_fields = ("get_html_view_status",)
    change_fields = (
        "comment",
        "status",
    )

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "get_html_view_status",
                    "subject",
                    "sender_address",
                    "recipient_list",
                    "body",
                    "template_language",
                    "template_name",
                    "template_context",
                    "tries",
                    "status_text",
                    "comment",
                ),
            },
        ),
    )

    @button(
        change_form=True,
        html_attrs={"style": "background-color:#47BAC1;color:white"},
        label=_("Send"),
    )
    def send(self, request, pk):
        instance = Mail.objects.get(pk=pk)
        if not instance.is_can_send:
            messages.error(
                request,
                format_html(
                    _("This email has already been sent, ")
                    + f"<a href='{reverse('admin:mails_mail_add')}'>create a new one</a>"
                ),
            )
            return

        send_email_instance.delay(instance.id)
        messages.info(request, format_html(_("This email has already been sent")))

    @button(
        change_form=True,
        html_attrs={"style": "background-color:#47BAC1;color:white"},
        label=_("Copy Mail"),
    )
    def copy_mail(self, request, pk):
        # TODO: create a check for the length of the url,
        # and if it is longer than 2000,
        # then create it as an object and redirect to it

        mail = Mail.objects.get(pk=pk)
        query_string = urlencode(
            {
                "subject": mail.subject,
                "sender_address": mail.sender_address,
                "recipient_list": ",".join(mail.recipient_list),
                "body": mail.body,
                "template_name": mail.template_name,
                "template_context": mail.template_context,
                "comment": f"Copied from mail id {pk}",
            }
        )
        url = reverse("admin:mails_mail_add")
        url = f"{url}?{query_string}"
        messages.info(
            request,
            format_html(
                _("Copied from ")
                + f"<a href='{reverse('admin:mails_mail_change', args=[pk])}'>{mail}</a>"
            ),
        )
        return redirect(url)

    @admin.display(description="Status")
    def get_html_view_status(self, obj: Mail):
        status_color = "gray"
        status_text = obj.status
        match obj.status:
            case MailStatus.CREATED:
                status_color = "orange"
            case MailStatus.SENDING:
                status_color = "blue"
            case MailStatus.SENT:
                status_color = "green"
            case MailStatus.FAILED:
                status_color = "red"
            case MailStatus.SKIPPED:
                status_color = "gray"

        return format_html(
            '<span style="color:{};font-weight: bold;">{}</span>',
            status_color,
            status_text,
        )

    def save_model(self, request, obj, form, change):
        if "template_context" in form.cleaned_data and isinstance(
            form.cleaned_data["template_context"], str
        ):
            try:
                obj.template_context = json.loads(
                    form.cleaned_data["template_context"].replace("'", '"')
                )
            except json.JSONDecodeError as e:
                self.message_user(
                    request,
                    f"JSONDecodeError({str(e)})",
                    level="ERROR",
                    fail_silently=True,
                )
                return
        super().save_model(request, obj, form, change)

    def get_readonly_fields(self, request, obj=None) -> list:
        """
        If the Email object has already been sent,
        then revision is prohibited and all fields become readonly,
        if the object has not yet been sent,
        then the fields specified in `change_fields` can be changed
        """

        if obj and self.change_fields and not obj.is_can_cange:

            return [
                field.name for field in (
                    *self.model._meta.get_fields(),
                ) if field.name not in self.change_fields
            ] + [
                *self.custom_fields
            ]

        return [
            *self.readonly_fields,
            *self.custom_fields
        ]

from pymongo import MongoClient
from gridfs import GridFS
from django.conf import settings
from bson.json_util import DEFAULT_JSON_OPTIONS
from bson.binary import UuidRepresentation

mongo_client = MongoClient(
    settings.MONGO_CONNECTION_STRING.format(
        user=settings.MONGO_INITDB_ROOT_USERNAME,
        password=settings.MONGO_INITDB_ROOT_PASSWORD,
        host=settings.MONGO_HOST,
        port=settings.MONGO_PORT,
    ),
    uuidRepresentation="standard",
)

json_options = DEFAULT_JSON_OPTIONS.with_options(
    uuid_representation=UuidRepresentation.STANDARD
)

db = mongo_client["prod"]
flows = db["flows"]
flow_store = db["flow_store"]

grid_fs = GridFS(db)

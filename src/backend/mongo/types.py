from enum import Enum
from typing import TypedDict
import uuid
import datetime

from bson import ObjectId

MONGO_ID = ObjectId | str
UUID = str | uuid.UUID


class FlowLogField(Enum):
    (
        uuid,
        name,
        start,
        end,
        node_calls,
        status,
        executed_node_count,
        total_node_count,
    ) = range(8)


class NodeCallField(Enum):
    (
        uuid,
        start,
        end,
        request_count,
        status,
        retries,
        errors,
        input_data_ids,
        raw_data,
        output_data_ids,
    ) = range(10)


class GridFSDataType(Enum):
    serializable_data = 0
    raw_data = 1


class Status(Enum):
    success = (0, "Success")
    failed = (1, "Failed")
    retrying = (2, "Retrying")
    in_progress = (3, "In progress")
    in_queue = (4, "In queue")
    skipped = (5, "Skipped")


class StatusDict(TypedDict):
    code: int
    name: str


class NodeCallLog(TypedDict):
    _id: MONGO_ID | None
    uuid: UUID | None
    start: datetime.datetime | None
    end: datetime.datetime | None
    status: StatusDict | None
    request_count: int | None
    retries: int | None
    errors: list[str] | None
    input_data: MONGO_ID | None
    raw_data: MONGO_ID | None
    output_data: MONGO_ID | None


class FlowLog(TypedDict):
    _id: MONGO_ID | None
    uuid: UUID | None
    name: str | None
    start: datetime.datetime | None
    end: datetime.datetime | None
    node_calls: list[NodeCallLog] | None
    status: StatusDict | None
    executed_node_count: int | None
    total_node_count: int | None

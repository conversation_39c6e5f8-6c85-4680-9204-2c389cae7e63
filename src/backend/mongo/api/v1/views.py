from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import (
    extend_schema,
    OpenApiParameter,
    OpenApiResponse,
    OpenApiExample,
)
from mongo.operations import GridFSOperations


@extend_schema(
    operation_id="flow logs data",
    summary="Gets a list containing nodecall's output/input/rawdata using GridFS file id",
    responses={
        200: OpenApiResponse(
            description="""Each response is a list of dicts in a form of<br>
            {'data':'arbitrary data',<br>
             'data_type':'either raw_data or serializable_data string',<br>
             'python_type': 'string name of data's original python type'}""",
            response={"data": "data", "data_type": "type", "python_type": "type"},
            examples=[
                OpenApiExample(
                    name="generic example",
                    value={
                        "data": {"a": "b"},
                        "data_type": "serializable_data",
                        "python_type": "dict",
                    },
                ),
            ],
        ),
        400: OpenApiResponse(description="Invalid request."),
    },
    parameters=[
        OpenApiParameter(
            name="file_ids",
            type=OpenApiTypes.STR,
            location=OpenApiParameter.QUERY,
            required=True,
            many=True,
            description="File id(s) retrieved from nodecall logs(e.g. from output_data attribute)",
        ),
    ],
)
class GetGridFSDataView(APIView):
    permission_classes = [
        IsAuthenticated,
    ]

    def get(self, request):
        file_ids = self.request.query_params.getlist("file_ids")
        try:
            return Response(data=GridFSOperations.get_data(file_ids), status=status.HTTP_200_OK)
        except Exception as ex:
            return Response(
                data={"errors": str(ex)}, status=status.HTTP_400_BAD_REQUEST
            )

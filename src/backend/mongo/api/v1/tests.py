import unittest

from faker import Faker

from rest_framework.test import APIRequestFactory, force_authenticate
from django.urls import reverse

from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    clear_gridfs,
)
from mongo.operations import GridFSOperations
from mongo.types import GridFSDataType
from mongo.api.v1.views import GetGridFSDataView
from users.models import SymmyUser

fake = Faker()


class TestMongoRelatedAPI(CheckTestsAllowedMixin, unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.factory = APIRequestFactory()
        cls.admin = SymmyUser.objects.create_superuser(
            email=fake.ascii_email(), password=fake.password()
        )

    def test_get_gridfs_data(self):
        data = {"a": "b"}
        objid = GridFSOperations.store_data(
            data, GridFSDataType.serializable_data, obj_id_as_str=True
        )
        request = self.factory.get(
            reverse("api:v1:mongo:flow-data"), data={"file_ids": objid}
        )
        force_authenticate(request, user=self.admin)
        response = GetGridFSDataView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, GridFSOperations.get_data(objid))
        self.assertEqual(response.data[0]["data"], data)

    def tearDown(self):
        clear_gridfs()

    @classmethod
    def tearDownClass(cls):
        cls.admin.organizations.all().delete()
        cls.admin.delete()

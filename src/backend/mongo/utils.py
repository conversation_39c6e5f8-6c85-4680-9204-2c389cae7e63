import datetime

from bson import json_util, ObjectId
import json

from mongo.conf import flows

from .conf import json_options
from .types import FlowLogField, NodeCallField, Status, MONGO_ID


def construct_from_to_filter(
    from_: datetime.datetime | None = None,
    to: datetime.datetime | None = None,
) -> dict | list:
    cond = dict()
    if from_ and to:
        cond["$and"] = [
            {"start": {"$gte": from_}},
            {"start": {"$lte": to.replace(microsecond=999999)}},
        ]

    elif from_ is not None:
        cond["start"] = {"$gte": from_}

    elif to is not None:
        cond["start"] = {"$lte": to.replace(microsecond=999999)}

    return cond


def construct_status_query(
    status: Status | list[Status],
    field_name: str | None = None,
) -> dict:
    if status is None:
        return {}

    query = {}

    rstatus = status_as_dict(status)
    if isinstance(rstatus, list):
        query["$or"] = []
        for s in rstatus:
            if field_name is not None:
                query["$or"].append({f"{field_name}.status": s})
            else:
                query["$or"].append({"status": s})

    else:
        if field_name is not None:
            query[f"{field_name}.status"] = rstatus
        else:
            query["status"] = rstatus

    return query


def status_as_dict(status: Status | list[Status]) -> dict | list[dict]:
    def format_status(stat: Status) -> dict:
        return {"code": stat.value[0], "name": stat.value[1]}

    if isinstance(status, list):
        return [format_status(s) for s in status]

    return format_status(status)


def dict_status_to_enum(status: dict) -> Status:
    return Status((status["code"], status["name"]))


def format_time(dt: datetime.datetime) -> str:
    try:
        return dt.strftime("%Y-%m-%dT%H:%M:%S")
    except ValueError:
        return dt.strftime("%Y-%m-%dT%H:%M:00")


def mongo_to_py_representation(data):
    if not data:
        return {}

    return json.loads(
        json_util.dumps(convert_mongo_data(data), json_options=json_options)
    )


def convert_mongo_keys(data: dict) -> dict:
    convert_to_str = {"uuid", "_id", "input_data", "output_data", "raw_data"}
    convert_to_date = {"start", "end"}

    convert_dict_values_to_type(
        data=data, keys=convert_to_str, conversion=str, filter_=lambda x: x is not None
    )
    convert_dict_values_to_type(
        data=data,
        keys=convert_to_date,
        conversion=format_time,
        filter_=lambda x: isinstance(x, datetime.datetime),
    )
    if data.get("node_calls"):
        if isinstance(data["node_calls"], dict):
            data["node_calls"] = [data["node_calls"]]

        for i in range(len(data["node_calls"])):
            convert_dict_values_to_type(data["node_calls"][i], convert_to_str, str)
            convert_dict_values_to_type(
                data["node_calls"][i],
                convert_to_date,
                format_time,
                lambda x: isinstance(x, datetime.datetime),
            )

    return data


def convert_mongo_data(mongo_data: dict) -> dict:
    if isinstance(mongo_data, list):
        for i in range(len(mongo_data)):
            mongo_data[i] = convert_mongo_keys(mongo_data[i])
    else:
        convert_mongo_keys(mongo_data)

    return mongo_data


def convert_dict_values_to_type(
    data: dict, keys: set[str], conversion: callable, filter_: callable = None
) -> None:
    for key, val in data.items():
        if key in keys:
            if isinstance(val, list):
                if filter_ is None:
                    data[key] = [conversion(x) for x in val]
                else:
                    data[key] = [conversion(x) for x in val if filter_(x)]
            elif filter_ is None or filter_(val):
                data[key] = conversion(val)


def transform_to_object_id(
    id_: MONGO_ID,
) -> ObjectId | None:
    if id_ is None:
        return None

    return id_ if isinstance(id_, ObjectId) else ObjectId(id_)


def get_flow_uuids_corresponding_to_document_ids(
    mongo_flow_ids: list[str],
) -> list[dict]:
    return list(
        flows.find(
            {"_id": {"$in": [transform_to_object_id(id_) for id_ in mongo_flow_ids]}},
            {
                "_id": 1,
                "uuid": 1,
            },
        )
    )


def check_field_names_valid(
    fields: dict, namespace: type[FlowLogField] | type[NodeCallField]
):
    for k, _ in fields.items():
        if k not in fields:
            raise ValueError(
                f"Invalid field name: {k}\nSupported fields: {list(namespace.__members__)}"
            )

import datetime
import unittest
from collections import deque

from bson import ObjectId

from symmy.test_utils.utils import (
    CheckTestsAllowedMixin,
    clear_gridfs,
    clear_flows_collection,
    wait_flow_end,
)
from symmy.test_utils.model_mixins import SimpleFlowData
from mongo.utils import status_as_dict
from mongo.types import GridFSDataType, Status
from mongo.operations import FlowLogOperations, NodeCallLogOperations, GridFSOperations
from mongo.analytics import get_flow_logs, get_node_calls


class TestOperations(CheckTestsAllowedMixin, unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.flow_data = SimpleFlowData()
        cls.doc_ids = list()
        cls.gridfs_ids = list()

    def test_auto_flow_log_create(self):
        flow_log_params = {
            "start": datetime.datetime.now().replace(microsecond=0),
            "status": Status.success,
        }
        flow_op = FlowLogOperations(self.flow_data.flow)
        flow_op.set_params(**flow_log_params)
        flow_op.create_log()

        flow_logs = get_flow_logs(mongo_flow_ids=[flow_op.mongo_flow_id])["logs"]
        self.assertEqual(len(flow_logs), 1)
        log = flow_logs[0]
        self.assertEqual(log["start"], flow_log_params["start"])
        self.assertEqual(log["status"], status_as_dict(flow_log_params["status"]))

    def test_auto_flow_log_update(self):
        flow_log_params = {
            "start": datetime.datetime.now().replace(microsecond=0),
            "status": Status.in_progress,
        }
        flow_op = FlowLogOperations(self.flow_data.flow)
        flow_op.set_params(**flow_log_params)
        flow_op.create_log()

        update_params = {
            "end": datetime.datetime.now().replace(microsecond=0),
            "status": Status.failed,
        }
        flow_op.set_params(**update_params)
        flow_op.update_log()

        flow_logs = get_flow_logs(mongo_flow_ids=[flow_op.mongo_flow_id])["logs"]
        self.assertEqual(len(flow_logs), 1)
        log = flow_logs[0]
        self.assertEqual(log["start"], flow_log_params["start"])
        self.assertEqual(log["end"], update_params["end"])
        self.assertEqual(log["status"], status_as_dict(update_params["status"]))

    def test_auto_node_call_log_create(self):
        flow_log_params = {
            "start": datetime.datetime.now().replace(microsecond=0),
            "status": Status.in_progress,
        }
        flow_op = FlowLogOperations(self.flow_data.flow)
        flow_op.set_params(**flow_log_params)
        flow_op.create_log()

        node_call_op = NodeCallLogOperations(
            flow_log_operations=flow_op, node_instance=self.flow_data.n0
        )
        node_calls_params = {
            "status": Status.in_progress,
            "start": datetime.datetime.now().replace(microsecond=0),
            "request_count": 0,
            "retries": 0,
        }
        node_call_op.set_params(**node_calls_params)
        node_call_op.create_log()

        node_calls = get_node_calls(
            mongo_flow_ids=[node_call_op.flow_log_operations.mongo_flow_id]
        )["node_calls"]
        self.assertEqual(len(node_calls), 1)

        node_call = node_calls[0]
        self.assertEqual(
            node_call["status"], status_as_dict(node_calls_params["status"])
        )
        self.assertEqual(node_call["start"], node_calls_params["start"])
        self.assertEqual(node_call["request_count"], node_calls_params["request_count"])
        self.assertEqual(node_call["retries"], node_calls_params["retries"])

    def test_auto_node_call_log_update(self):
        flow_log_params = {
            "start": datetime.datetime.now().replace(microsecond=0),
            "status": Status.in_progress,
        }
        flow_op = FlowLogOperations(self.flow_data.flow)
        flow_op.set_params(**flow_log_params)
        flow_op.create_log()

        node_call_op = NodeCallLogOperations(
            flow_log_operations=flow_op, node_instance=self.flow_data.n0
        )
        node_calls_params = {
            "status": Status.in_progress,
            "start": datetime.datetime.now().replace(microsecond=0),
            "request_count": 0,
            "retries": 0,
        }
        node_call_op.set_params(**node_calls_params)
        node_call_op.create_log()

        node_calls_update_params = {
            "status": Status.failed,
            "end": datetime.datetime.now().replace(microsecond=0),
            "retries": 10,
        }
        node_call_op.set_params(**node_calls_update_params)
        node_call_op.update_log()

        node_calls = get_node_calls(
            mongo_flow_ids=[node_call_op.flow_log_operations.mongo_flow_id]
        )["node_calls"]
        self.assertEqual(len(node_calls), 1)

        node_call = node_calls[0]
        self.assertEqual(
            node_call["status"], status_as_dict(node_calls_update_params["status"])
        )
        self.assertEqual(node_call["end"], node_calls_update_params["end"])
        self.assertEqual(node_call["retries"], node_calls_update_params["retries"])

    def test_child_input_equals_to_parent_output(self):
        mongo_flow_id = self.flow_data.flow.execute()
        wait_flow_end(mongo_flow_id)
        nodes = deque([self.flow_data.flow.root])
        while len(nodes) != 0:
            curr_node = nodes.popleft()
            parent_node_call = get_node_calls(
                mongo_flow_ids=[mongo_flow_id], node_uuids=[curr_node.uuid]
            )["node_calls"][0]
            children = curr_node.children
            for child in children:
                child_node_call = get_node_calls(
                    mongo_flow_ids=[mongo_flow_id], node_uuids=[child.uuid]
                )["node_calls"][0]

                self.assertEqual(
                    parent_node_call["output_data"], child_node_call["input_data"]
                )
                nodes.append(child)

    def test_create_flow_log_manual(self):
        res = FlowLogOperations.create_flow_log_manually(
            uuid=self.flow_data.flow.uuid,
            status=status_as_dict(Status.in_progress),
            start=datetime.datetime.now().replace(microsecond=0),
        )
        self.assertIsInstance(res.inserted_id, ObjectId)
        self.doc_ids.append(res.inserted_id)

    def test_create_node_call_log_manual(self):
        flow_log_id = FlowLogOperations.create_flow_log_manually(
            uuid=self.flow_data.flow.uuid,
            status=status_as_dict(Status.in_progress),
            start=datetime.datetime.now().replace(microsecond=0),
        ).inserted_id
        self.doc_ids.append(flow_log_id)
        res = NodeCallLogOperations.create_node_call_manually(
            mongo_flow_id=flow_log_id,
            uuid=self.flow_data.flow.root.uuid,
            status=status_as_dict(Status.in_progress),
            errors=[],
            input_data=None,
            start=datetime.datetime.now().replace(microsecond=0),
        )
        self.assertEqual(res.matched_count, 1)
        self.assertEqual(res.modified_count, 1)

    def test_store_node_data(self):
        data = {"test": "data"}
        res = GridFSOperations.store_data(
            data, data_type=GridFSDataType.serializable_data
        )
        self.assertIsInstance(res, ObjectId)
        self.gridfs_ids.append(res)

    def test_get_node_data(self):
        data = {"test": "data"}
        gridid = GridFSOperations.store_data(
            data, data_type=GridFSDataType.serializable_data
        )
        self.gridfs_ids.append(gridid)
        list_data = GridFSOperations.get_data(gridid)
        self.assertEqual(data, list_data[0]["data"])

    def test_get_datetime_obj(self):
        data = {"test": datetime.datetime.now().replace(microsecond=0)}
        gridid = GridFSOperations.store_data(
            data, data_type=GridFSDataType.serializable_data
        )
        self.gridfs_ids.append(gridid)
        list_data = GridFSOperations.get_data(gridid)
        self.assertEqual(data, list_data[0]["data"])

    def tearDown(self):
        clear_gridfs()
        clear_flows_collection()

    @classmethod
    def tearDownClass(cls):
        cls.flow_data.delete_models()
        clear_gridfs()
        clear_flows_collection()

import datetime
from typing import TypedDict
import uuid

from pymongo import DESCENDING
from pymongo.cursor import Cursor
from django.db.models.query import QuerySet

from flows.models import Flow
from core.models import Project, Organization
from mongo.types import (
    FlowLogField,
    NodeCallField,
    Status,
    FlowLog,
    NodeCallLog,
    UUID,
    MONGO_ID,
)
from mongo.utils import (
    construct_from_to_filter,
    construct_status_query,
    status_as_dict,
    mongo_to_py_representation,
    transform_to_object_id,
)
from mongo.conf import flows


class GetFlowLogsResult(TypedDict):
    total_count: int
    logs: list[FlowLog]


class GetNodeCallsResult(TypedDict):
    total_count: int
    node_calls: list[NodeCallLog]


# TODO: ADD LOGGING!


def create_id_query(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    aggregation: bool = False,
) -> dict | list:
    if not flow_list and not mongo_flow_ids and not flow_uuids:
        raise ValueError(
            "At least one of flow, mongo_flow_id, flow_uuid has to be specified"
        )
    if mongo_flow_ids:
        id_cond = {
            "_id": {"$in": [transform_to_object_id(id_) for id_ in mongo_flow_ids]}
        }
    elif flow_uuids:
        id_cond = {"uuid": {"$in": [uuid.UUID(id_) for id_ in flow_uuids]}}
    else:
        id_cond = {"uuid": {"$in": [f.uuid for f in flow_list]}}

    if aggregation:
        match_cond = {"$match": {"$and": []}}
        match_cond["$match"]["$and"].append(id_cond)
        return [match_cond]

    return id_cond


def update_aggregation_match_stage(pipeline: list, new_filter: dict):
    for i, q in enumerate(pipeline):
        if q.get("$match", None) is not None:
            pipeline[i]["$match"]["$and"].append(new_filter)
            break
        else:  # executes if break has not been executed
            pipeline.append({"$match": {"$and": [new_filter]}})


def add_date_filter(
    search: dict | list,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
) -> None:
    if from_ or to:
        date_filter = construct_from_to_filter(from_, to)
        match search:
            case list():
                update_aggregation_match_stage(search, date_filter)
            case dict():
                search.update(date_filter)
            case _:
                return


def add_status_filter(
    search: dict | list,
    status: Status | list[Status] = None,
    field_name: str = None,
) -> None:
    if status:
        if field_name:
            status_filter = construct_status_query(status, field_name)
        else:
            status_filter = construct_status_query(status)

        match search:
            case list():
                update_aggregation_match_stage(search, status_filter)
            case dict():
                search.update(status_filter)


def add_skip_filter_aggregation(search: list, skip: int) -> None:
    if skip is not None:
        search.append({"$skip": skip})


def add_limit_filter_aggregation(search: list, limit: int) -> None:
    if limit is not None:
        search.append({"$limit": limit})


def apply_limit_skip_to_query_result(res: Cursor, limit: int, skip: int) -> Cursor:
    if skip is not None:
        res = res.skip(skip)
    if limit is not None:
        res = res.limit(limit)
    return res


def exclude_fields_filter(exclude_fields: list[FlowLogField | NodeCallField]) -> dict:
    filter_ = dict()
    if exclude_fields:
        for f in exclude_fields:
            if isinstance(f, FlowLogField):
                filter_[f.name] = 0
            elif isinstance(f, NodeCallField):
                filter_[f"node_calls.{f.name}"] = 0

    return filter_


def get_flow_logs(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    flow_status: Status | list[Status] = None,
    exclude_fields: list[FlowLogField | NodeCallField] = None,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
    offset: int = None,
    limit: int = None,
    json_serializable: bool = False,
) -> GetFlowLogsResult:
    query: dict = create_id_query(
        flow_list=flow_list, mongo_flow_ids=mongo_flow_ids, flow_uuids=flow_uuids
    )

    add_date_filter(search=query, from_=from_, to=to)
    add_status_filter(search=query, status=flow_status)

    total_count = get_executed_flow_count(
        flow_list=flow_list,
        flow_uuids=flow_uuids,
        mongo_flow_ids=mongo_flow_ids,
        from_=from_,
        to=to,
        flow_status=flow_status,
    )

    projection = exclude_fields_filter(exclude_fields)
    if projection:
        res = flows.find(query, projection).sort([("_id", DESCENDING)])
    else:
        res = flows.find(query).sort([("_id", DESCENDING)])

    res = apply_limit_skip_to_query_result(res, limit, offset)

    if json_serializable:
        return GetFlowLogsResult(
            total_count=total_count,
            logs=[mongo_to_py_representation(f) for f in res],
        )
    return GetFlowLogsResult(total_count=total_count, logs=list(res))


def get_node_calls(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    node_uuids: list[UUID] = None,
    flow_status: Status | list[Status] = None,
    node_status: Status | list[Status] = None,
    exclude_fields: list[NodeCallField] = None,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
    offset: int = None,
    limit: int = None,
    json_serializable: bool = False,
) -> GetNodeCallsResult:
    pipeline: list = create_id_query(
        flow_list=flow_list,
        mongo_flow_ids=mongo_flow_ids,
        flow_uuids=flow_uuids,
        aggregation=True,
    )

    add_date_filter(search=pipeline, from_=from_, to=to)
    add_status_filter(search=pipeline, status=flow_status)
    pipeline.append({"$unwind": "$node_calls"})
    if node_status:
        pipeline.append({"$match": {"node_calls.status": status_as_dict(node_status)}})

    if node_uuids:
        pipeline.append({"$match": {"node_calls.uuid": {"$in": node_uuids}}})

    project_stage = exclude_fields_filter(exclude_fields)
    if project_stage:
        pipeline.append({"$project": project_stage})

    total_count = next(flows.aggregate(pipeline + [{"$count": "total_count"}]), {}).get(
        "total_count", 0
    )

    add_skip_filter_aggregation(pipeline, offset)
    add_limit_filter_aggregation(pipeline, limit)
    if json_serializable:
        return GetNodeCallsResult(
            total_count=total_count,
            node_calls=[
                mongo_to_py_representation(f["node_calls"])
                for f in flows.aggregate(pipeline)
            ],
        )
    return GetNodeCallsResult(
        total_count=total_count,
        node_calls=[f["node_calls"] for f in flows.aggregate(pipeline)],
    )


def get_executed_flow_count(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
    flow_status: Status | list[Status] = None,
) -> int:
    query = create_id_query(
        flow_list=flow_list, mongo_flow_ids=mongo_flow_ids, flow_uuids=flow_uuids
    )

    add_date_filter(search=query, from_=from_, to=to)
    add_status_filter(search=query, status=flow_status)

    return flows.count_documents(filter=query)


def get_node_calls_count(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
    flow_status: Status | list[Status] = None,
    node_status: Status | list[Status] = None,
) -> int:
    pipeline = create_id_query(
        flow_list=flow_list,
        mongo_flow_ids=mongo_flow_ids,
        flow_uuids=flow_uuids,
        aggregation=True,
    )

    add_date_filter(search=pipeline, from_=from_, to=to)
    add_status_filter(search=pipeline, status=flow_status)
    pipeline.append({"$unwind": "$node_calls"})
    if node_status:
        pipeline.append({"$match": {"node_calls.status": status_as_dict(node_status)}})
    pipeline.append({"$count": "total_count"})

    return next(flows.aggregate(pipeline), {}).get("total_count", 0)


def get_request_count(
    flow_list: list[Flow] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    flow_uuids: list[UUID] = None,
    from_: datetime.datetime = None,
    to: datetime.datetime = None,
) -> int:
    pipeline = create_id_query(
        flow_list=flow_list,
        mongo_flow_ids=mongo_flow_ids,
        flow_uuids=flow_uuids,
        aggregation=True,
    )
    add_date_filter(search=pipeline, from_=from_, to=to)

    pipeline += [
        {"$unwind": "$node_calls"},
        {
            "$match": {
                "$or": [
                    # get only successful/failed/ node calls
                    {"node_calls.status": status_as_dict(Status.retrying)},
                    {"node_calls.status": status_as_dict(Status.success)},
                    {"node_calls.status": status_as_dict(Status.failed)},
                ]
            }
        },
        {
            "$group": {
                "_id": None,
                "total_request_count": {"$sum": "$node_calls.request_count"},
            }
        },
    ]
    res = flows.aggregate(pipeline)
    return next(res, {}).get("total_request_count", 0)


def _save_result(res: dict, instance, op_result, name_as_key: bool, str_uuid: bool):
    if name_as_key:
        res[instance.name] = op_result
    elif str_uuid:
        res[str(instance.uuid)] = op_result
    else:
        res[instance.uuid] = op_result


def _prepare_query_set(queryset, uuids: list[UUID], model):
    if queryset is None:
        queryset = model.objects.all()
    if uuids:
        return queryset.filter(uuid__in=uuids)
    return queryset


def flow_operation(
    operation: callable,
    queryset: QuerySet[Flow] = None,
    flow_uuids: list[UUID] = None,
    mongo_flow_ids: list[MONGO_ID] = None,
    name_as_key: bool = False,
    str_uuid: bool = False,
    bulk_result: bool = False,
    **kwargs,
):
    """
    Apply an operation to all of the specified Flows.

    Args:
        operation (callable): A Flow operation(e.g. get_flow_logs) to be applied.
        queryset (QuerySet[Project]): The initial queryset to work with.
        flow_uuids (List[uuid] | List[str]): Flow uuids used to filter the initial queryset.
        mongo_flow_ids (list[str] | str): List of mongo document ids.
        name_as_key (bool): Make the keys in the resulting dict be the names of the Flows objects.
                            Propagates downwards.
        str_uuid(bool): Make the keys in the resulting dict be the string uuids of the Flows objects.
                        Propagates downwards.
        bulk_result(bool): Execute the operation for all found Flows at once
                           and return the result as a single list.
    """
    if bulk_result:
        return operation(
            flow_list=queryset,
            mongo_flow_ids=mongo_flow_ids,
            flow_uuids=flow_uuids,
            **kwargs,
        )

    res = dict()

    if mongo_flow_ids:
        for mongo_flow_id in mongo_flow_ids:
            res[str(mongo_flow_id)] = operation(
                mongo_flow_ids=[mongo_flow_id], **kwargs
            )
    else:
        queryset = _prepare_query_set(queryset, flow_uuids, Flow)
        for flow in queryset:
            op_result = operation(flow_list=[flow], **kwargs)
            _save_result(res, flow, op_result, name_as_key, str_uuid)

    return res


def project_operation(
    operation: callable,
    queryset: QuerySet[Project] = None,
    project_uuids: list[UUID] = None,
    name_as_key: bool = False,
    str_uuid: bool = False,
    bulk_result: bool = False,
    **kwargs,
):
    """
    Apply an operation to all Flows belonging to the specified Projects

    Args:
        operation (callable): A Flow operation(e.g. get_flow_logs) to be applied.
        queryset (QuerySet[Project]): The initial queryset to work with.
        project_uuids (List[uuid] | List[str]): Project uuids used to filter the initial queryset.
        name_as_key (bool): Make the keys in the resulting dict be the names of the Project objects.
                            Propagates downwards.
        str_uuid(bool): Make the keys in the resulting dict be the string uuids of the Project objects.
                        Propagates downwards.
        bulk_result(bool): Execute the operation for all found Project at once
                           and return the result as a single list.
    """
    queryset = _prepare_query_set(queryset, project_uuids, Organization)
    if bulk_result:
        flow_queryset = Flow.objects.none()
        for proj in queryset:
            flow_queryset = flow_queryset.union(proj.flows.all())
        return flow_operation(
            operation=operation,
            queryset=flow_queryset,
            name_as_key=name_as_key,
            str_uuid=str_uuid,
            bulk_result=bulk_result,
            **kwargs,
        )

    res = dict()
    for proj in queryset:
        op_result = flow_operation(
            operation=operation,
            queryset=proj.flows.all(),
            name_as_key=name_as_key,
            str_uuid=str_uuid,
            **kwargs,
        )
        _save_result(res, proj, op_result, name_as_key, str_uuid)

    return res


def organization_operation(
    operation: callable,
    queryset: QuerySet[Organization] = None,
    organization_uuids: list[UUID] = None,
    name_as_key: bool = False,
    str_uuid: bool = False,
    bulk_result: bool = False,
    **kwargs,
):
    """
    Apply an operation to all Flows belonging to the specified Organizations

    Args:
        operation (callable): A Flow operation(e.g. get_flow_logs) to be applied.
        queryset (QuerySet[Organization]): Apply the operation to the Organizations in the queryset.
        organization_uuids (List[uuid] | List[str]): Apply the operation to Organizations with the given uuids.
        name_as_key (bool): Make the keys in the resulting dict be the names of the Organization objects.
                            Propagates downwards.
        str_uuid(bool): Make the keys in the resulting dict be the string uuids of the Organization objects.
                        Propagates downwards.
        bulk_result(bool): Execute the operation for all found Organizations at once
                           and return the result as a single list.
    """
    queryset = _prepare_query_set(queryset, organization_uuids, Organization)

    if bulk_result:
        projects = Project.objects.none()
        for org in queryset:
            projects = projects.union(org.project_set.all())
        return project_operation(
            operation=operation,
            queryset=projects,
            name_as_key=name_as_key,
            str_uuid=str_uuid,
            bulk_result=bulk_result,
            **kwargs,
        )

    res = dict()
    for org in queryset:
        op_result = project_operation(
            operation=operation,
            queryset=org.project_set.all(),
            name_as_key=name_as_key,
            str_uuid=str_uuid,
            **kwargs,
        )
        _save_result(res, org, op_result, name_as_key, str_uuid)

    return res

import sys
import unittest

from django.conf import settings
from django.core.management.base import BaseCommand
from django.test.runner import <PERSON><PERSON><PERSON><PERSON>ner

from symmy.test_utils.utils import CheckTestsAllowedMixin

class Command(BaseCommand):
    help = "Run tests which require production database"

    def add_arguments(self, parser):
        parser.add_argument(
            "test_labels", nargs="*", help="Specific tests or directories to run."
        )

    def handle(self, *args, **options):
        test_labels = options.get("test_labels", [])

        test_runner = Command.ProdDBTestRunner(verbosity=2)
        result = test_runner.run_tests(test_labels)

        if result:
            sys.exit(result)

    class ProdDBTestRunner(DiscoverRunner):
        def build_suite(self, test_labels=None, extra_tests=None, **kwargs):
            if settings.ALLOW_PROD_DB_TESTS:
                suite = super().build_suite(test_labels, extra_tests, **kwargs)
                filtered_suite = unittest.TestSuite()
                for t in suite:
                    if issubclass(type(t), CheckTestsAllowedMixin):
                        filtered_suite.addTest(t)
                return filtered_suite

            raise Exception(
                "Test which utilize production database are not allowed to run"
            )

        def setup_databases(self, **kwargs):
            pass

        def teardown_databases(self, old_config, **kwargs):
            pass

{"name": "symmy-frontend", "version": "1.0.0", "main": "src/index.tsx", "repository": "https://gt.whys.dev/symmy/symmy", "author": "<PERSON>", "private": true, "scripts": {"dev": "vite", "build": "vite build", "serve": "vite preview", "lint": "eslint . && tsc", "test": "vitest watch", "test:no-watch": "cross-env ASYNC_UTIL_TIMEOUT=10000 vitest run --test-timeout=20000", "test:coverage": "vitest run --coverage", "messages:extract": "lingui extract", "start": "node ./src/server.js"}, "dependencies": {"@ant-design/charts": "^2.0.3", "@ant-design/icons": "^5.1.4", "@dagrejs/dagre": "^1.1.2", "@lexical/html": "^0.14.5", "@lexical/markdown": "^0.14.5", "@lexical/react": "^0.14.5", "@lexical/utils": "^0.14.5", "@lingui/macro": "^4.5.0", "@lingui/react": "^4.5.0", "@lingui/vite-plugin": "^4.5.0", "@sentry/react": "^8.30.0", "@sentry/vite-plugin": "^2.22.4", "@tanstack/react-query": "^4.32.0", "@tanstack/react-query-devtools": "^4.32.0", "@vitejs/plugin-react": "^4.0.3", "antd": "^5.9.4", "antd-img-crop": "^4.13.0", "axios": "^1.4.0", "express": "^4.18.2", "file-saver": "^2.0.5", "http-proxy-middleware": "^2.0.6", "js-cookie": "^3.0.5", "lexical": "^0.14.5", "lodash": "^4.17.21", "moment": "^2.29.4", "rc-picker": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-view-lite": "^0.9.7", "react-router-dom": "^6.14.2", "react-spring": "^9.7.3", "reactflow": "^11.7.4", "sass": "^1.64.1", "vite": "^4.4.7", "vite-plugin-svgr": "^3.2.0", "vite-tsconfig-paths": "^4.2.0", "zustand": "^4.5.4"}, "devDependencies": {"@lingui/cli": "^4.5.0", "@tanstack/eslint-plugin-query": "^4.29.25", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.3", "@types/lodash": "^4.17.0", "@types/node": "^20.4.4", "@types/react": "^18.2.16", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitest/coverage-v8": "^0.33.0", "babel-plugin-macros": "^3.1.0", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jsdom": "^22.1.0", "msw": "^2.2.10", "prettier": "^3.0.0", "typescript": "^5.1.6", "vitest": "^0.33.0", "vitest-matchmedia-mock": "^1.0.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}
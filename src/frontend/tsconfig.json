{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "types": ["vite/client", "vite-plugin-svgr/client", "@testing-library/jest-dom", "vitest/globals"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "isolatedModules": true, "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "declaration": true, "noUnusedLocals": true, "noUnusedParameters": true, "incremental": true, "noImplicitAny": true, "noFallthroughCasesInSwitch": true, "paths": {"@": ["./src"], "@app": ["./src/app"], "@assets": ["./src/assets"], "@components": ["./src/components"], "@config": ["./src/config"], "@hooks": ["./src/hooks"], "@locales": ["./src/locales"], "@pages": ["./src/pages"], "@reducers": ["./src/reducers"], "@styles": ["./src/styles"], "@tests": ["./src/tests"], "@types": ["./src/types"], "@utils": ["./src/utils"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "build", "src/tests/setup.ts", "src/tests/server"]}
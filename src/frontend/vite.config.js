/// <reference types="vitest" />
import path from 'path';

import { lingui } from '@lingui/vite-plugin';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import svgrPlugin from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    build: {
      sourcemap: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/styles/variables.scss";',
        },
      },
    },
    define: {
      'process.env.ENV': JSON.stringify(env.ENV) || 'LOCAL',
    },
    plugins: [
      react({
        babel: {
          plugins: ['macros'],
        },
      }),
      lingui(),
      viteTsconfigPaths(),
      svgrPlugin(),
      sentryVitePlugin({
        authToken: env.SENTRY_AUTH_TOKEN,
        org: 'symmy',
        project: 'symmy-frontend',
        release: {
          deploy: {
            env: env.ENV || 'LOCAL',
          },
        },
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@app': path.resolve(__dirname, './src/app'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@components': path.resolve(__dirname, './src/components'),
        '@config': path.resolve(__dirname, './src/config'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@locales': path.resolve(__dirname, './src/locales'),
        '@pages': path.resolve(__dirname, './src/pages'),
        '@reducers': path.resolve(__dirname, './src/reducers'),
        '@styles': path.resolve(__dirname, './src/styles'),
        '@tests': path.resolve(__dirname, './src/tests'),
        '@types': path.resolve(__dirname, './src/types'),
        '@utils': path.resolve(__dirname, './src/utils'),
      },
    },
    server: {
      open: true,
      port: 3000,
      proxy: {
        '/api': {
          target: 'http://localhost:8010',
          changeOrigin: true,
        },
        '/debug': {
          target: 'http://localhost:8010',
          changeOrigin: true,
        },
        '/media': {
          target: 'http://localhost:8010',
          changeOrigin: true,
        },
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/tests/setup.ts',
      coverage: {
        reporter: ['text', 'html'],
        exclude: [
          'node_modules/',
          'src/tests/',
          'src/components/analytics/CallItemsChart.tsx',
        ],
      },
      testTimeout: 5000,
      mockReset: true,
    },
  };
});

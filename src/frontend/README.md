# Symmy frontend

### Built With

- React 18
- React Query
- Ant Design
- React Testing Library
- Vite and Vitest

### Installation

1. Install packages

   ```
   yarn
   ```

2. Run development server (http://localhost:3000)

   ```
   yarn dev
   ```

### Testing

- Run tests

  ```
  yarn test
  ```

- Test coverage

  ```
  yarn test:coverage
  ```

### Build

1. Build

   ```
   yarn build
   ```

2. Run frontend server with build

   ```
   yarn start
   ```

### Folder structure

- `app` folder contains application routes along with containers and context providers for React Query, Ant Design, etc.
- `assets` folder contains a few assets such as SVG files, etc.
- `components` folder contains reusable components (mostly in `other` folder) and components for pages in _intuitively_ named folders
- `config` folder contains constants such as API endpoints, route paths, etc.
- `hooks` folder contains many reusable hooks, whenever you write a hook spanning multiple lines, consider putting it there
- `locales` folder contains translations including response success / error messages usable via the `useGetTranslatedMessage` hook
- `pages` folder contains application pages
- `reducers` folder contains applicationwide reducers accessible via contexts
- `styles` folder contains global styles and variables, keep in mind that most styles are found in `.module.scss` files next to their JavaScript counterparts
- `tests` folder contains setup used for tests as well as mock API and some testing utilities
- `types` folder contains applicationwide types
- `utils` folder contains useful utility functions

### Other notes

- Vite is amazingly fast, but it doesn't perform type checking, it assumes type checking is taken care of by your IDE and the build process (https://vitejs.dev/guide/features.html#transpile-only)
- If you aren't familiar with Typescript, check out https://github.com/typescript-cheatsheets/react
- Aliases are being used for directories inside the `src` folder, e.g. files inside the `app` directory can be accessed by `@app/...`
- When creating new directories in the `src` folder, add aliases to
  - `.eslintrc.js`
  - `tsconfig.json`
  - `vite.config.js`
- Rem units are used in CSS files, 1 Rem = 10 px
- SCSS variables are available in JavaScript code via styles/variables.module.scss and styles/index.ts files

import { ReactNode } from 'react';
import {
  DecoratorN<PERSON>,
  NodeKey,
  LexicalNode,
  SerializedLexicalNode,
  Spread,
  DOMExportOutput,
  DOMConversionMap,
  DOMConversionOutput,
  LexicalCommand,
  createCommand,
} from 'lexical';

import { OutputFieldTag } from '@components';

function convertOutputFieldElement(domNode: HTMLElement): null | DOMConversionOutput {
  const text = domNode.getAttribute('data-lexical-text') as string;
  return { node: $createOutputFieldNode(text) };
}

export type SerializedOutputFieldNode = Spread<
  {
    text: string;
  },
  SerializedLexicalNode
>;

export class OutputFieldNode extends DecoratorNode<ReactNode> {
  text: string;

  static getType(): string {
    return 'output-field';
  }

  static clone(node: OutputFieldNode): OutputFieldNode {
    return new OutputFieldNode(node.text, node.__key);
  }

  constructor(text: string, key?: Node<PERSON><PERSON>) {
    super(key);
    this.text = text;
  }

  static importJSON(serializedNode: SerializedOutputFieldNode): OutputFieldNode {
    const node = $createOutputFieldNode(serializedNode.text);
    return node;
  }

  exportJSON(): SerializedOutputFieldNode {
    return {
      text: this.text,
      type: 'output-field',
      version: 1,
    };
  }

  createDOM(): HTMLElement {
    return document.createElement('span');
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement('span');
    element.setAttribute('data-lexical-text', this.text);
    return { element };
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: (domNode: HTMLElement) => {
        if (!domNode.hasAttribute('data-lexical-text')) {
          return null;
        }
        return {
          conversion: convertOutputFieldElement,
          priority: 1,
        };
      },
    };
  }

  updateDOM(): false {
    return false;
  }

  getTextContent() {
    return this.text;
  }

  decorate() {
    return <OutputFieldTag text={this.text} />;
  }

  isInline() {
    return true;
  }

  isIsolated() {
    return true;
  }
}

export const $createOutputFieldNode = (text: string): OutputFieldNode => {
  return new OutputFieldNode(text);
};

export const $isOutputFieldNode = (node: LexicalNode) => {
  return node instanceof OutputFieldNode;
};

export type OutputFieldCommandPayload = {
  text: string;
};

export const INSERT_OUTPUT_FIELD_COMMAND: LexicalCommand<OutputFieldCommandPayload> =
  createCommand('INSERT_OUTPUT_FIELD_COMMAND');

// import { Button, Form, Input } from 'antd';
// import { msg, Trans } from '@lingui/macro';
// import { useLingui } from '@lingui/react';

// import { FormItem, InputField } from '@components';

// import { useMessage } from '@hooks';

// import styles from './CreateConnectionForm.module.scss';

// type CreateConnectionFormProps = {
//   fields: FieldConfiguration[];
//   onCreateConnection: (name: string, fields: { [key: string]: string }) => void;
// };

// const CreateConnectionForm = ({
//   fields,
//   onCreateConnection,
// }: CreateConnectionFormProps) => {
//   const { _ } = useLingui();
//   const [form] = Form.useForm();
//   const message = useMessage();

//   const onSubmit = async () => {
//     try {
//       const data = await form.validateFields();
//       const { name, ...fields } = data;
//       onCreateConnection(name, fields);
//     } catch (error) {
//       console.error(error);
//       message.error(
//         _(msg`The form contains errors, please fix them before submitting`),
//       );
//     }
//   };

//   return (
//     <div className={styles.container}>
//       <Form form={form}>
//         <FormItem
//           key="name"
//           className={styles.fieldContainer}
//           name="name"
//           label={_(msg`Name`)}
//           rules={[{ required: true, message: _(msg`This field is required`) }]}
//         >
//           <Input placeholder={_(msg`Name`)} />
//         </FormItem>
//         {fields.length > 0
//           ? fields.map((field: FieldConfiguration) => {
//               return (
//                 <FormItem
//                   key={field.key}
//                   className={styles.fieldContainer}
//                   name={field.key}
//                   label={field.label}
//                   rules={[
//                     ...(field.required
//                       ? [{ required: true, message: _(msg`This field is required`) }]
//                       : []),
//                   ]}
//                 >
//                   <InputField lexical={false} field={field} pickOutputFields={false} />
//                 </FormItem>
//               );
//             })
//           : null}
//         {/* (
//           <span className={styles.fieldContainer}>
//             <Trans>Connection has no fields</Trans>
//           </span>
//         ) */}
//       </Form>
//       <div>
//         <Button
//           type="primary"
//           size="small"
//           shape="round"
//           className={styles.button}
//           onClick={() => onSubmit()}
//         >
//           <span>
//             <Trans>Create connection</Trans>
//           </span>
//         </Button>
//       </div>
//     </div>
//   );
// };

// export default CreateConnectionForm;

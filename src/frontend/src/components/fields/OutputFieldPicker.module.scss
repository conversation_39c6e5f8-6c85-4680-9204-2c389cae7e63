.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  height: 100%;
}

.nodeOutputFieldsContainer {
  border: 0.1rem solid #eee;
  border-radius: 0.6rem;
  padding: 1rem;
}

.nodeName {
  font-size: 1.6rem;
}

.tagContainer {
  margin-top: 0.3rem;
}

.tag {
  cursor: pointer;
  user-select: none;

  &:hover {
    filter: brightness(95%);
  }
}

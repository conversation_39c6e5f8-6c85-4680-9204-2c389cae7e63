// import { Tag } from 'antd';
// import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

// import { useOutputFieldsMap } from '@hooks';

// import { INSERT_OUTPUT_FIELD_COMMAND } from '@utils';

// import styles from './OutputFieldPicker.module.scss';

// const OutputFieldPicker = () => {
//   const [editor] = useLexicalComposerContext();
//   const outputFieldsMap = useOutputFieldsMap();

//   const onClick = (text: string) => {
//     editor.dispatchCommand(INSERT_OUTPUT_FIELD_COMMAND, { text });
//   };

//   return (
//     <div
//       className={styles.container}
//       role="toolbar"
//       onMouseDown={(event) => {
//         event.preventDefault();
//       }}
//     >
//       {Array.from(outputFieldsMap.entries()).map(([node, outputFields]) => {
//         return (
//           <div key={node.data.uuid} className={styles.nodeOutputFieldsContainer}>
//             <div className={styles.nodeName}>{node.data.nodeType?.name}</div>
//             {outputFields.map((outputField: FieldConfiguration, index: number) => (
//               <div key={index} className={styles.tagContainer}>
//                 <Tag
//                   color="blue"
//                   className={styles.tag}
//                   onMouseDown={(event) => {
//                     event.preventDefault();
//                     onClick(outputField.label);
//                   }}
//                 >
//                   {outputField.label}
//                 </Tag>
//               </div>
//             ))}
//           </div>
//         );
//       })}
//     </div>
//   );
// };

// export default OutputFieldPicker;

// import { memo, useMemo } from 'react';
// import { EditorState } from 'lexical';
// import { LexicalComposer } from '@lexical/react/LexicalComposer';
// import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
// import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';

// import {
//   DisableLineBreakPlugin,
//   // ImportPlugin,
//   // InputFieldPlugin,
//   // OutputFieldPlugin,
//   RichCopyPastePlugin,
// } from '@components';

// import {
//   OutputFieldNode,
//   // $createOutputFieldNode
// } from '@utils';

// import styles from './LexicalInputField.module.scss';

// type LexicalInputFieldProps = {
//   field: FieldConfiguration;
//   pickOutputFields?: boolean;
//   onChange?: (value: string) => void;
//   hasError?: boolean;
// };

// const LexicalInputField = ({
//   field,
//   pickOutputFields = true,
//   onChange,
//   hasError = false,
// }: LexicalInputFieldProps) => {
//   const initialLexicalConfig = useMemo(() => {
//     return {
//       namespace: 'LexicalInputField',
//       theme: {},
//       onError: (error: Error) => {
//         console.error(error);
//       },
//       nodes: [OutputFieldNode],
//     };
//   }, []);

//   const onEditorChange = (editorState: EditorState) => {
//     onChange?.(JSON.stringify(editorState.toJSON()));
//   };

//   return (
//     <div className={styles.container}>
//       <LexicalComposer initialConfig={initialLexicalConfig}>
//         {/* <InputFieldPlugin
//           pickOutputFields={pickOutputFields}
//           placeholder={field.placeholder || field.label}
//           hasError={hasError}
//         /> */}
//         {/* {<OutputFieldPlugin />} */}
//         <HistoryPlugin />
//         <OnChangePlugin onChange={onEditorChange} />
//         <DisableLineBreakPlugin />
//         <RichCopyPastePlugin />
//         {/* <ImportPlugin /> */}
//       </LexicalComposer>
//     </div>
//   );
// };

// const MemoizedLexicalInputField = memo(LexicalInputField);
// export default MemoizedLexicalInputField;

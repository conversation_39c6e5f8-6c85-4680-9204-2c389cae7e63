// import { memo, ChangeEvent } from 'react';
// import { Input } from 'antd';

// import { LexicalInputField } from '@components';

// import styles from './InputField.module.scss';

// type InputFieldProps = {
//   lexical?: boolean;
//   field: FieldConfiguration;
//   pickOutputFields?: boolean;
//   onChange?: (value: string) => void;
//   'aria-invalid'?: boolean;
// };

// const InputField = (props: InputFieldProps) => {
//   const { lexical = true, field, pickOutputFields, onChange } = props;

//   if (field.fields && field.fields.length > 0) {
//     return (
//       <div className={styles.fieldContainer}>
//         <div className={styles.fieldContainerLabel}>{field.label}</div>
//         <div className={styles.fieldContainerFields}>
//           {field.fields?.map((field) => (
//             <InputField
//               lexical={lexical}
//               key={field.key}
//               field={field}
//               pickOutputFields={pickOutputFields}
//             />
//           ))}
//         </div>
//       </div>
//     );
//   }

//   if (lexical) {
//     return (
//       <LexicalInputField
//         field={field}
//         pickOutputFields={pickOutputFields}
//         onChange={onChange}
//         hasError={props['aria-invalid']}
//       />
//     );
//   } else {
//     return (
//       <Input
//         placeholder={props.field.label}
//         onChange={(event: ChangeEvent<HTMLInputElement>) => {
//           onChange?.(event.target.value);
//         }}
//       />
//     );
//   }
// };

// const MemoizedInputField = memo(InputField);
// export default MemoizedInputField;

import { useEffect } from 'react';
import type { CommandPayloadType, LexicalEditor } from 'lexical';
import {
  $getSelection,
  $isNodeSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_LOW,
  COPY_COMMAND,
  CUT_COMMAND,
  PASTE_COMMAND,
} from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $insertDataTransferForRichText, copyToClipboard } from '@lexical/clipboard';
import { objectKlassEquals } from '@lexical/utils';

async function onCutForRichText(
  event: CommandPayloadType<typeof CUT_COMMAND>,
  editor: LexicalEditor,
): Promise<void> {
  await copyToClipboard(
    editor,
    objectKlassEquals(event, ClipboardEvent) ? (event as ClipboardEvent) : null,
  );
  editor.update(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      selection.removeText();
    } else if ($isNodeSelection(selection)) {
      selection.getNodes().forEach((node) => node.remove());
    }
  });
}

function onPasteForRichText(
  event: CommandPayloadType<typeof PASTE_COMMAND>,
  editor: LexicalEditor,
): void {
  event.preventDefault();
  editor.update(
    () => {
      const selection = $getSelection();
      const clipboardData =
        objectKlassEquals(event, InputEvent) || objectKlassEquals(event, KeyboardEvent)
          ? null
          : (event as ClipboardEvent).clipboardData;
      if (clipboardData != null && selection !== null) {
        $insertDataTransferForRichText(clipboardData, selection, editor);
      }
    },
    {
      tag: 'paste',
    },
  );
}

// This component implements copying and pasting
// the same way 'RichTextPlugin' does
// (we use PlainTechPlugin, so by default, copying
//  and pasting is text-based only, this enables
//  copying and pasting output fields)
const RichCopyPastePlugin = () => {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    return editor.registerCommand(
      COPY_COMMAND,
      (event) => {
        copyToClipboard(
          editor,
          objectKlassEquals(event, ClipboardEvent) ? (event as ClipboardEvent) : null,
        );
        return true;
      },
      // default 'COPY_COMMAND' uses 'COMMAND_PRIORITY_EDITOR',
      // which is lower than 'COMMAND_PRIORITY_LOW',
      // therefore this overrides the default copy functionality
      COMMAND_PRIORITY_LOW,
    );
  }, [editor]);

  useEffect(() => {
    return editor.registerCommand(
      CUT_COMMAND,
      (event) => {
        onCutForRichText(event, editor);
        return true;
      },
      // default 'CUT_COMMAND' uses 'COMMAND_PRIORITY_EDITOR',
      // which is lower than 'COMMAND_PRIORITY_LOW',
      // therefore this overrides the default cut functionality
      COMMAND_PRIORITY_LOW,
    );
  }, [editor]);

  useEffect(() => {
    return editor.registerCommand(
      PASTE_COMMAND,
      (event) => {
        const selection = $getSelection();
        if (selection !== null) {
          onPasteForRichText(event, editor);
          return true;
        }

        return false;
      },
      // default 'COPY_COMMAND' uses 'COMMAND_PRIORITY_EDITOR',
      // which is lower than 'COMMAND_PRIORITY_LOW',
      // therefore this overrides the default paste functionality
      COMMAND_PRIORITY_LOW,
    );
  }, [editor]);

  return null;
};

export default RichCopyPastePlugin;

// import { useState } from 'react';
// import { Popover } from 'antd';
// import { ContentEditable } from '@lexical/react/LexicalContentEditable';
// import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary';
// import { PlainTextPlugin } from '@lexical/react/LexicalPlainTextPlugin';

// import { OutputFieldPicker } from '@components';

// import styles from './InputFieldPlugin.module.scss';

// type InputFieldPluginProps = {
//   pickOutputFields: boolean;
//   placeholder: string;
//   hasError: boolean;
// };

// const InputFieldPlugin = ({
//   pickOutputFields,
//   placeholder,
//   hasError,
// }: InputFieldPluginProps) => {
//   const [displayOutputFieldPicker, setDisplayOutputFieldPicker] = useState(false);

//   return (
//     <PlainTextPlugin
//       contentEditable={
//         <Popover
//           open={pickOutputFields && displayOutputFieldPicker}
//           content={<OutputFieldPicker />}
//           placement="right"
//           overlayInnerStyle={{ padding: 0 }}
//         >
//           <div>
//             <ContentEditable
//               className={`${styles.contentEditable} ${
//                 hasError ? styles.contentEditableError : ''
//               }`}
//               onFocus={() => setDisplayOutputFieldPicker(true)}
//               onBlur={() => setDisplayOutputFieldPicker(false)}
//             />
//           </div>
//         </Popover>
//       }
//       ErrorBoundary={LexicalErrorBoundary}
//       placeholder={<span className={styles.placeholder}>{placeholder}</span>}
//     />
//   );
// };

// export default InputFieldPlugin;

.contentEditable {
  background-color: #fff;
  border: 0.1rem solid #d9d9d9;
  border-radius: 0.6rem;
  font-size: 1.4rem;
  min-height: 3.12rem;
  line-height: 2.2rem;
  outline: none;
  padding: 0.4rem 1.1rem;
  transition: border-color 0.2s;

  &:hover,
  &:focus {
    border-color: $themeHover;
  }

  p {
    margin: 0 !important;
  }
}

.contentEditableError {
  border-color: red !important;
}

.placeholder {
  color: #bbb;
  font-weight: 300;
  margin-left: 1.2rem;
  pointer-events: none;
  position: absolute;
  top: 50%;
  // top: 0.7rem;
  transform: translateY(-50%);
}

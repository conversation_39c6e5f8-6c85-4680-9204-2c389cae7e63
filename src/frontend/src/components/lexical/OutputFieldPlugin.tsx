// import { useEffect } from 'react';
// import {
//   $createParagraphNode,
//   $insertNodes,
//   $isRootOrShadowRoot,
//   COMMAND_PRIORITY_EDITOR,
// } from 'lexical';
// import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
// import { $wrapNodeInElement } from '@lexical/utils';

// import {
//   OutputFieldNode,
//   $createOutputFieldNode,
//   OutputFieldCommandPayload,
//   INSERT_OUTPUT_FIELD_COMMAND,
// } from '@utils';

// const OutputFieldPlugin = () => {
//   const [editor] = useLexicalComposerContext();

//   useEffect(() => {
//     if (!editor.hasNodes([OutputFieldNode])) {
//       throw new Error('OutputFieldPlugin: OutputFieldNode not registered on editor');
//     }

//     return editor.registerCommand<OutputFieldCommandPayload>(
//       INSERT_OUTPUT_FIELD_COMMAND,
//       (payload) => {
//         const { text } = payload;
//         const outputFieldNode = $createOutputFieldNode(text);

//         $insertNodes([outputFieldNode]);
//         if ($isRootOrShadowRoot(outputFieldNode.getParentOrThrow())) {
//           $wrapNodeInElement(outputFieldNode, $createParagraphNode).selectEnd();
//         }

//         return true;
//       },
//       COMMAND_PRIORITY_EDITOR,
//     );
//   }, [editor]);

//   return null;
// };

// export default OutputFieldPlugin;

import { $getRoot, $insertNodes } from 'lexical';
import {
  // $generateHtmlFromNodes,
  $generateNodesFromDOM,
} from '@lexical/html';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

const ImportPlugin = () => {
  const [editor] = useLexicalComposerContext();
  // const editorState = editor.getEditorState();

  // const stringifiedEditorState =
  //   '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"","text":"a","type":"text","version":1},{"text":"something","type":"output-field","version":1},{"detail":0,"format":0,"mode":"normal","style":"","text":"a","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}';
  const htmlString =
    '<p dir="ltr"><span style="white-space: pre-wrap;">a</span><span data-lexical-text="something"></span><span style="white-space: pre-wrap;">a</span></p>';
  // const htmlString = '<p><span data-lexical-text="something"></span></p>';

  const onImport = () => {
    // console.log(JSON.stringify(editorState.toJSON()));

    // const parsedState = editor.parseEditorState(stringifiedEditorState);
    // editor.setEditorState(parsedState);

    // editorState.read(() => {
    //   console.log($generateHtmlFromNodes(editor));
    // });

    editor.update(() => {
      const parser = new DOMParser();
      const dom = parser.parseFromString(htmlString, 'text/html');
      const nodes = $generateNodesFromDOM(editor, dom);
      // https://github.com/facebook/lexical/issues/3677#issuecomment-2032298281
      $getRoot().clear();
      $getRoot().select();
      $insertNodes(nodes);
    });
  };

  return <button onClick={() => onImport()}>Import</button>;
};

export default ImportPlugin;

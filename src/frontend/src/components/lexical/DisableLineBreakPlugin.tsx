import { useEffect } from 'react';
import { LineBreakNode } from 'lexical';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';

const DisableLineBreakPlugin = () => {
  const [editor] = useLexicalComposerContext();

  // https://github.com/facebook/lexical/issues/3675#issuecomment-1917027590
  useEffect(() => {
    return editor.registerNodeTransform(LineBreakNode, (node) => {
      node.remove();
    });
  }, [editor]);

  return null;
};

export default DisableLineBreakPlugin;

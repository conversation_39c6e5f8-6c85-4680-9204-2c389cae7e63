// import { <PERSON> } from 'react-router-dom';
// import { Button } from 'antd';
// import { ArrowLeftOutlined } from '@ant-design/icons';

// import styles from './GoBackButton.module.scss';

// const GoBackButton = ({ to }: { to: string }) => {
//   return (
//     <Link to={to}>
//       <Button className={styles.button} type="text" shape="circle">
//         <ArrowLeftOutlined className={styles.icon} />
//       </Button>
//     </Link>
//   );
// };

// export default GoBackButton;

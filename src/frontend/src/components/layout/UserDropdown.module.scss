.dropdownContent {
  align-items: center;
  background-color: #fff;
  box-shadow: $cardBoxShadowHover;
  display: flex;
  flex-direction: column;
  padding: 1rem 1.6rem 0.8rem 1.2rem;
}

.row {
  align-items: center;
  display: flex;
  flex-direction: row;
}

.column {
  display: flex;
  flex-direction: column;
  margin-left: 0.8rem;
}

.userName {
  font-size: 1.6rem;
  font-weight: 500;
}

.userEmail {
  margin-top: -0.1rem;
}

.dropdownButton {
  justify-content: flex-start;
  margin-top: 0.2rem;
  padding-left: 1.2rem !important;
  padding-right: 1.2rem !important;
  width: 110% !important;

  svg {
    font-size: 1.5rem;
    transform: translateY(0.1rem);
  }
}

.sider {
  border-right: 0.1rem solid #ddd;
  padding-top: 3.6rem;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
}

.collapseButtonContainer {
  position: absolute;
  right: 0;
  top: 1rem;
  transform: translateX(50%);
  z-index: 2;
}

.collapseIcon,
.uncollapseIcon {
  font-size: 1.2rem !important;
  stroke-width: 10;
  stroke: $default;
  transform: translate(-0.1rem, -0.1rem);
}

.collapseIcon {
  transform: translate(-0.05rem, -0.1rem);
}

.uncollapseIcon {
  transform: translate(0.05rem, -0.1rem);
}

.menuLabel {
  user-select: none;
}

.appVersionContainer {
  bottom: 1rem;
  color: $gray;
  font-size: 1.3rem;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
  user-select: none;
}

.content {
  background-color: #fff;
  min-height: calc(100vh - $headerHeight);
}

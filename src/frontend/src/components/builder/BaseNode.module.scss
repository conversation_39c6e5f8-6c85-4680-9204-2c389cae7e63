.content {
  background-color: #fff;
  border: 0.1rem solid #000;
  border-radius: 0.8rem;
  cursor: pointer;
  padding: 1rem 1.4rem;
  position: relative;
  text-align: center;
  transition: $bc3s;
}

.contentHover {
  background-color: $lightThemeHover;
}

.nodeType {
  font-size: 1.6rem;
}

.nodeAction {
  font-size: 1rem;
}

.handle {
  // height: 0.6rem !important;
  // width: 0.65rem !important;
  // z-index: 1;´
  height: 0.9rem !important;
  width: 0.95rem !important;
}

.addButton {
  bottom: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  // top: 50%;
  transition: $o3s, $v3s;
  transform: translate(25%, 25%);
  visibility: hidden;
  z-index: 2;
}

.addButtonVisible {
  opacity: 1;
  visibility: visible;
}

.plusIcon {
  font-size: 1rem;
}

.nodeToolbarContainer {
  opacity: 0;
  transition: $o3s, $v3s;
  visibility: hidden;
}

.nodeToolbarContainerVisible {
  opacity: 1;
  visibility: visible;
}

.nodeToolbar {
  background-color: #fff;
  border-radius: 0.8rem;
  box-shadow: $popoverBoxShadow;
  max-height: 50rem;
  overflow-y: auto;
  padding: 1.2rem 1.6rem;
}

.nodeToolbarArrow {
  background-color: #fff;
  height: 1.2rem;
  left: 0;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 1.2rem;
  z-index: 2;
}

.nodeToolbarRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.8rem;
}

.closeButton {
  margin: -0.2rem -0.6rem 0 0;
}

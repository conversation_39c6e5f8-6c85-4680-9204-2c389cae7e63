.select,
.connectionRow {
  margin-bottom: 0.8rem;
  width: 30rem;
}

.connectionRow {
  align-items: center;
  display: flex;
  flex-direction: row;
}

.connectionSelect {
  flex-grow: 1;
  // flex-wrap: wrap;
  margin-right: 0.6rem;
  min-width: 0;
}

.createConnectionButton {
  height: 2.6rem !important;
  flex-shrink: 0;
  width: 2.6rem !important;

  span {
    font-size: 1.6rem !important;
  }
}

.inputFieldContainer {
  margin-bottom: 0.8rem;
  width: 30rem;
}

.inputFieldsTitle {
  margin-bottom: 0.5rem;
}

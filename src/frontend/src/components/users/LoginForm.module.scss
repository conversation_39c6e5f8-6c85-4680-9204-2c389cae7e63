.formContainer {
  width: 100%;
  max-width: 40rem;
}

// .title {
//   font-size: 3.2rem;
//   margin-bottom: 0.8rem;
//   text-align: center;
// }

.logo {
  // filter: $themeFilter;
  display: block;
  margin: 0 auto;
  width: 30rem;
  margin-bottom: 1rem;
}

.input {
  border-radius: 10rem !important;
  font-size: 2rem !important; // changes icon size
  margin-top: 1rem;
  padding: 1rem 1.6rem !important;
}

.input input {
  margin-left: 0.6rem;
}

.button {
  font-size: 1.8rem !important;
  height: auto !important;
  margin-top: 1rem;
  padding: 1rem 1.6rem !important;
  width: 100%;
}

.button svg {
  font-size: 2.2rem !important;
  transform: translateY(0.3rem);
}

.forgotPassword {
  // color: $default;
  color: #fff;
  margin-top: 1rem;
  text-align: center;

  &:hover,
  &:active {
    text-decoration: underline;
  }
}

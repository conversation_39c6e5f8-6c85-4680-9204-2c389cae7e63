.card {
  margin-bottom: 1rem;
  transition: $bc3s, $bs3s;

  &:hover {
    background-color: $lightThemeHover;
    box-shadow: $cardBoxShadowHover;
  }
}

.cardContent {
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.rightSide {
  h3 {
    font-size: 1.6rem;
    margin-bottom: 0.4rem;
  }

  a {
    font-size: 1.4rem;
  }
}

.leftSide {
  align-items: center;
  display: flex;

  > span {
    align-items: center;
    display: flex;
  }
}

.numberIcon {
  color: $gray;
  font-size: 1.6rem;
  margin-left: 0.6rem;
}

.number {
  color: $gray;
  margin-left: 0.4rem;
  margin-right: 1.2rem;
  user-select: none;
}

.switch {
  margin: 0 2rem 0 1rem;
}

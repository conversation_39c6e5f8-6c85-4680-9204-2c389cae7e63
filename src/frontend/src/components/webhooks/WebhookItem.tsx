// import { Link } from 'react-router-dom';
// import { Card, Tooltip, Switch, Button } from 'antd';
// import { LoginOutlined, EyeOutlined } from '@ant-design/icons';
// import { Trans } from '@lingui/macro';

// import { ROUTES } from '@config';

// import { useActivateWebhook } from '@hooks';

// import styles from './WebhookItem.module.scss';

// const WebhookItem = ({ webhook }: { webhook: Webhook }) => {
//   const activateWebhook = useActivateWebhook(webhook);

//   return (
//     <Card className={styles.card} bordered={false} data-testid="webhook-item">
//       <div className={styles.cardContent}>
//         <div className={styles.rightSide}>
//           <h3>{webhook.service_name}</h3>
//           <a href={webhook.url} target="_blank" rel="noreferrer">
//             {webhook.url}
//           </a>
//         </div>
//         <div className={styles.leftSide}>
//           <Tooltip title={<Trans>Number of incomming requests</Trans>}>
//             <LoginOutlined className={styles.numberIcon} />
//             <span className={styles.number}>{webhook.number_of_requests}</span>
//           </Tooltip>
//           <Switch
//             className={styles.switch}
//             checkedChildren={<Trans>On</Trans>}
//             unCheckedChildren={<Trans>Off</Trans>}
//             checked={webhook.active}
//             onChange={() => activateWebhook()}
//             data-testid="webhook-switch"
//           />
//           <Link to={`${ROUTES.WEBHOOKS}/${webhook.uuid}`}>
//             <Button type="primary" icon={<EyeOutlined />} data-testid="detail-button">
//               <span>
//                 <Trans>View detail</Trans>
//               </span>
//             </Button>
//           </Link>
//         </div>
//       </div>
//     </Card>
//   );
// };

// export default WebhookItem;

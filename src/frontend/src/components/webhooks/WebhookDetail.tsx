// import { Tabs, Switch, Tag } from 'antd';
// import { Trans } from '@lingui/macro';

// import styles from './WebhookDetail.module.scss';

// const WebhookDetail = ({ webhook }: { webhook: Webhook }) => {
//   return (
//     <div className={styles.container}>
//       <Tabs
//         defaultActiveKey="1"
//         size="large"
//         items={[
//           {
//             key: '1',
//             label: (
//               <span>
//                 <Trans>Detail</Trans>
//               </span>
//             ),
//             children: (
//               <div className={styles.content}>
//                 <div>
//                   <h4>
//                     <Trans>Webhook status</Trans>
//                   </h4>
//                   <Switch
//                     disabled
//                     checkedChildren={<Trans>On</Trans>}
//                     unCheckedChildren={<Trans>Off</Trans>}
//                     checked={webhook.active}
//                     data-testid="webhook-switch"
//                   />
//                 </div>
//                 <div>
//                   <h4>
//                     <Trans>Service</Trans>
//                   </h4>
//                   {webhook.service_name}
//                 </div>
//                 <div>
//                   <h4>
//                     <Trans>Webhook URL</Trans>
//                   </h4>
//                   <a href={webhook.url} target="_blank" rel="noreferrer">
//                     {webhook.url}
//                   </a>
//                 </div>
//                 <div>
//                   <h4>
//                     <Trans>Webhook UUID</Trans>
//                   </h4>
//                   <Tag>{webhook.uuid}</Tag>
//                 </div>
//                 {/* <div>
//                   <h4>
//                     <Trans>Flows using this webhook</Trans>
//                   </h4>
//                   <ul>
//                     {webhook.flows?.map((flow: Flow | null) => {
//                       if (!flow) return null;
//                       return <li key={flow.id}>{flow.name}</li>;
//                     })}
//                   </ul>
//                 </div> */}
//               </div>
//             ),
//           },
//           {
//             key: '2',
//             label: (
//               <span>
//                 <Trans>Logs</Trans>
//               </span>
//             ),
//             children: <div>TBD</div>,
//           },
//         ]}
//       />
//     </div>
//   );
// };

// export default WebhookDetail;

// Colors
$theme: #3452f8; // should be the same as in config/theme.ts
$themeHover: #5e7cff;
$themeActive: #2136d1;
$theme2: #3bd895;
$theme2Hover: #7ce5b8;
$theme2Active: #24b477;
// $theme2Disabled: #cef5e5;
$theme3: #022b26;
$lightThemeHover: #edeffe;
$default: rgba(0, 0, 0, 0.85);
$gray: #777;
$lightGray: #eee;
$lightGrayHover: #f6f6f6;
$darkGray: rgb(51, 51, 51);

// Transitions
$bc3s: background-color 0.3s ease;
$bs3s: box-shadow 0.3s ease;
$c3s: color 0.3s ease;
$o3s: opacity 0.3s ease;
$v3s: visibility 0.3s ease;

// Antd
$cardBoxShadow:
  0 1px 2px 0 rgba(0, 0, 0, 0.03),
  0 1px 6px -1px rgba(0, 0, 0, 0.02),
  0 2px 4px 0 rgba(0, 0, 0, 0.02);
$cardBoxShadowHover:
  0 1px 2px 0 rgba(0, 0, 0, 0.1),
  0 1px 6px -1px rgba(0, 0, 0, 0.066),
  0 2px 4px 0 rgba(0, 0, 0, 0.066);
$popoverBoxShadow:
  0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 9px 28px 8px rgba(0, 0, 0, 0.05);

// Status colors
$statusSuccess: #45d470;
$statusError: #fe4955;
$statusRetry: #49fef2;
$statusInProgress: #f8d135;
$statusInQueue: #ddd;
$statusSkipped: #bbb;

// Other
$headerHeight: 5rem;
$dashboardSchemaHeight: 40vh;
$dashboardConfigurationHeight: 5rem;
$flowCallSchemaHeight: 45vh;
$themeFilter: invert(20%) sepia(99%) saturate(2688%) hue-rotate(230deg) brightness(101%)
  contrast(95%); // https://codepen.io/sosuke/pen/Pjoqqp

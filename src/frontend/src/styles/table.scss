.table-row-success {
  background-color: lighten($statusSuccess, 35%);
}

.table-row-failed {
  background-color: lighten($statusError, 30%);
}

.table-row-retrying {
  background-color: lighten($statusRetry, 30%);
}

.table-row-in-progress {
  background-color: lighten($statusInProgress, 30%);
}

.table-row-in-queue {
  background-color: lighten($statusInQueue, 0%);
}

.table-row-skipped {
  background-color: lighten($statusSkipped, 20%);
}

.table-row-success,
.table-row-failed,
.table-row-retrying,
.table-row-in-progress,
.table-row-in-queue,
.table-row-skipped {
  &:hover {
    filter: brightness(0.95);
  }

  .ant-table-cell-row-hover {
    background: unset !important;
  }
}

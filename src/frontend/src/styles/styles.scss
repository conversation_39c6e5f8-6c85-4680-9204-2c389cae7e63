@import './builder.scss';
@import './table.scss';

* {
  font-family: 'Inter';
}

// This results in 1rem meaning 10px
:root {
  font-size: 62.5%;
}

// Disable chrome autofill
// https://stackoverflow.com/a/29350537/8699608
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
}

.ant-btn-primary {
  &:disabled {
    background-color: #f5f5f5 !important;

    &:hover {
      background-color: #f5f5f5 !important;
    }

    &:active {
      background-color: #f5f5f5 !important;
    }
  }
}

.button-theme2 {
  background-color: $theme2;

  &:hover {
    background-color: $theme2Hover !important;
  }

  &:active {
    background-color: $theme2Active !important;
  }

  // &:disabled {
  //   background-color: $theme2Disabled !important;
  //   border-color: $theme2Disabled !important;
  //   color: rgba(0, 0, 0, 0.25) !important;
  //   text-shadow: none !important;
  //   box-shadow: none !important;
  // }
}

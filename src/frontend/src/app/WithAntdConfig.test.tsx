import { theme } from 'antd';
import { render, screen } from '@testing-library/react';

import { WithAntdConfig, WithProviders, WithLingui } from '@app';

const THEME = WithAntdConfig.THEME;

const Consumer = () => {
  const { token } = theme.useToken();
  return <div style={{ backgroundColor: token.colorPrimary }} data-testid="div" />;
};

describe('WithAntdConfig', () => {
  it('works correctly', () => {
    render(
      <WithProviders>
        <WithLingui>
          <WithAntdConfig>
            <Consumer />
          </WithAntdConfig>
        </WithLingui>
      </WithProviders>,
    );

    expect(screen.getByTestId('div')).toHaveStyle(
      `background-color: ${THEME.token.colorPrimary}`,
    );
  });
});

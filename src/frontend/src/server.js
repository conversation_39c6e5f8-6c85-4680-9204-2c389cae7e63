const express = require('express');
const path = require('path');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();
const port = process.env.PORT || 3000;

const apiProxy = createProxyMiddleware({
  // target: 'http://localhost:8010',
  target: 'http://web:8000',
  changeOrigin: true,
});

app.use('/api', apiProxy);
app.use('/debug', apiProxy);

app.use(express.static(path.join(__dirname, '..', '/dist')));

app.get('/health', function (req, res) {
  res.status(200).send('0');
});

app.get('*', function (req, res) {
  res.sendFile(path.join(__dirname, '..', 'dist', 'index.html'));
});

app.listen(port);
console.log(`Server started at http://localhost:${port}`);

declare type AppInfo = {
  version?: string;
};

declare type Organization = {
  uuid: string;
  name: string;
};

declare type Project = {
  uuid: string;
  name: string;
};

declare type Flow = {
  uuid: string;
  name: string;
  // interval: number;
  enabled: boolean;
};

// declare interface FieldConfiguration {
//   allow_empty?: boolean;
//   allow_none: boolean;
//   description?: string;
//   fields?: FieldConfiguration[];
//   initial: null;
//   input_formals?: string[];
//   key: string;
//   label: string;
//   max_length?: number;
//   min_length?: number;
//   placeholder?: string;
//   read_only: boolean;
//   required: boolean;
// }

declare type NodeType = {
  uuid: string;
  name: string;
};

declare type NodeAction = {
  uuid: string;
  node_type__uuid: string;
  name: string;
};

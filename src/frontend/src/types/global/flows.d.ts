declare type CallStatus = {
  code: number;
  name: string;
};

declare interface CallItem {
  start: string;
  status: CallStatus;
  groupKey?: string;
}

declare interface ParsedCallItem {
  _start: string;
  start: string;
  statusCode: number;
  statusName: string;
  value: number;
  color?: string;
}

declare interface FlowCall extends CallItem {
  _id: string;
  uuid: string; // flow uuid;
  name: string;
  end: string;
  total_node_count: number;
  executed_node_count: number;
  // node_calls: NodeCall[];
}

declare interface SchemaNodeCall extends RawNode {
  // this can be merged from table data (NodeCall type),
  // see mergeSchemaAndTableNodeCalls function
  status?: CallStatus;
}

declare interface NodeCall extends CallItem {
  uuid: string;
  end: string;
  input_data: null | string[];
  output_data: null | string[];
  errors: string[];
}

// these properties are merged from schema data (RawNode type),
// see mergeSchemaAndTableNodeCalls function
declare interface MergedNodeCall extends NodeCall {
  name: string;
  depth: number;
}

declare type NodeCallDataItem = {
  data: { [key: string]: unknown };
  data_type: string;
  python_type: string;
};

declare type ParsedNodeCallDataItem = {
  [key: string]: unknown;
};

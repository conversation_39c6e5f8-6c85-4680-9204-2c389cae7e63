declare interface RawNode {
  uuid: string;
  name: string;
  node_type_resource?: string;
  action_resource?: string;
  connection?: string;
  parent: string | null;
  defaultX?: number;
  defaultY?: number;
  depth?: number;
}

declare type NodeData = {
  uuid: string;
  parent: string | null;
  children: RawNode[];
  nodeType?: NodeType;
  nodeAction?: NodeAction;
  connection?: string;
  editable?: boolean;
  className?: string;
  hasLoadedPosition?: boolean;
  onCloseToolbar?: (nodeUuid: string) => void;
};

declare type EdgeData = {
  hovered: boolean;
};

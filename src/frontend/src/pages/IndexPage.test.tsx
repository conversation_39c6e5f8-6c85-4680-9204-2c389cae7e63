import { render, waitFor } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('IndexPage', () => {
  const serverConfiguration = setupServer();

  it('should redirect to login', async () => {
    serverConfiguration.loggedIn = false;

    const { router, PageTestContainer } = preparePageTest(ROUTES.INDEX);
    render(<PageTestContainer />);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.LOGIN);
    });
  });

  it('should redirect to dashboard', async () => {
    const { router, PageTestContainer } = preparePageTest(ROUTES.INDEX);
    render(<PageTestContainer />);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.DASHBOARD);
    });
  });
});

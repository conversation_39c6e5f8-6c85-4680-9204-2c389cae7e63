import { render, screen, waitFor, fireEvent } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('ResetPasswordPage', () => {
  const serverConfiguration = setupServer();

  it('should reset password', async () => {
    const path = ROUTES.RESET_PASSWORD.replace(':uuid', 'correct-uuid').replace(
      ':token',
      'correct-token',
    );
    const { router, PageTestContainer } = preparePageTest(path);
    render(<PageTestContainer />);

    const password1Input = await screen.findByTestId('password1-input');
    const password2Input = await screen.findByTestId('password2-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(password1Input, { target: { value: 'new-password' } });
    fireEvent.change(password2Input, { target: { value: 'new-password' } });
    fireEvent.click(submitButton);

    await screen.findByText('New password has been saved');

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.DASHBOARD);
    });
  });

  it('should display password reset error', async () => {
    serverConfiguration.forceError = '/users/reset-password';

    const path = ROUTES.RESET_PASSWORD.replace(':uuid', 'correct-uuid').replace(
      ':token',
      'correct-token',
    );
    const { PageTestContainer } = preparePageTest(path);
    render(<PageTestContainer />);

    const password1Input = await screen.findByTestId('password1-input');
    const password2Input = await screen.findByTestId('password2-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(password1Input, { target: { value: 'new-password' } });
    fireEvent.change(password2Input, { target: { value: 'new-password' } });
    fireEvent.click(submitButton);

    await screen.findByText('An error has occurred while setting new password');
  });

  // password mismatch
  it('should not reset password due to password mismatch', async () => {
    const path = ROUTES.RESET_PASSWORD.replace(':uuid', 'correct-uuid').replace(
      ':token',
      'correct-token',
    );
    const { router, PageTestContainer } = preparePageTest(path);
    render(<PageTestContainer />);

    const password1Input = await screen.findByTestId('password1-input');
    const password2Input = await screen.findByTestId('password2-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(password1Input, { target: { value: 'new-password' } });
    fireEvent.change(password2Input, { target: { value: 'other-password' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(path);
    });
  });

  // wrong uuid and token
  it('should not reset password due to wrong uuid and token', async () => {
    const path = ROUTES.RESET_PASSWORD.replace(':uuid', 'wrong-uuid').replace(
      ':token',
      'wrong-token',
    );
    const { router, PageTestContainer } = preparePageTest(path);
    render(<PageTestContainer />);

    const password1Input = await screen.findByTestId('password1-input');
    const password2Input = await screen.findByTestId('password2-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(password1Input, { target: { value: 'new-password' } });
    fireEvent.change(password2Input, { target: { value: 'new-password' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(path);
    });
  });
});

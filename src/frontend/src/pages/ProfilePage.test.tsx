import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { within } from '@testing-library/dom';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('ProfilePage', () => {
  const serverConfiguration = setupServer();

  it('should render user data', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const avatarContainer = await screen.findByTestId('avatar-container');
    const image = within(avatarContainer).queryByAltText('Profile Image');
    expect(image).not.toBeNull();
    expect((image as HTMLImageElement).src).toBe('https://github.com/philip.png');

    const emailInput = await screen.findByTestId('email-input');
    const firstNameInput = await screen.findByTestId('first-name-input');
    const lastNameInput = await screen.findByTestId('last-name-input');

    expect((emailInput as HTMLInputElement).value).toBe('<EMAIL>');
    expect((emailInput as HTMLInputElement).disabled).toBe(true);
    expect((firstNameInput as HTMLInputElement).value).toBe('Testing');
    expect((lastNameInput as HTMLInputElement).value).toBe('User');
  });

  it('should remove and upload avatar', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const avatarContainer = await screen.findByTestId('avatar-container');
    expect(within(avatarContainer).queryByText('+ Upload')).toBeNull();

    const removeButton = within(avatarContainer).getByTitle('Remove file');
    fireEvent.click(removeButton);

    await screen.findByText('Profile image has been removed');

    await waitFor(async () => {
      expect(within(avatarContainer).queryByText('+ Upload')).not.toBeNull();
    });

    const uploadElement = await screen.findByTestId('avatar-upload');
    const avatarFile = new File(['content'], 'avatar.png', { type: 'image/png' });
    fireEvent.change(uploadElement, { target: { files: [avatarFile] } });

    await waitFor(async () => {
      const message = screen.queryByText('Profile image has been uploaded');
      expect(message).not.toBeNull();
    });

    await waitFor(async () => {
      expect(within(avatarContainer).queryByText('+ Upload')).toBeNull();
    });
  });

  it('should display remove avatar error', async () => {
    serverConfiguration.forceError = '/remove-avatar';

    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const avatarContainer = await screen.findByTestId('avatar-container');
    expect(within(avatarContainer).queryByText('+ Upload')).toBeNull();

    const removeButton = within(avatarContainer).getByTitle('Remove file');
    fireEvent.click(removeButton);

    await screen.findByText('An error has occurred while removing profile image');
  });

  it('should display upload avatar error', async () => {
    serverConfiguration.forceError = '/update-avatar';

    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const avatarContainer = await screen.findByTestId('avatar-container');
    expect(within(avatarContainer).queryByText('+ Upload')).toBeNull();

    const removeButton = within(avatarContainer).getByTitle('Remove file');
    fireEvent.click(removeButton);

    await waitFor(async () => {
      expect(within(avatarContainer).queryByText('+ Upload')).not.toBeNull();
    });

    const uploadElement = await screen.findByTestId('avatar-upload');
    const avatarFile = new File(['content'], 'avatar.png', { type: 'image/png' });
    fireEvent.change(uploadElement, { target: { files: [avatarFile] } });

    await screen.findByText('An error has occurred while uploading profile image');
  });

  it('should update user data', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const firstNameInput = await screen.findByTestId('first-name-input');
    const lastNameInput = await screen.findByTestId('last-name-input');
    const saveButton = await screen.findByTestId('save-button');

    expect(saveButton.style.opacity).toBe('0');

    fireEvent.change(firstNameInput, { target: { value: 'John' } });
    fireEvent.change(lastNameInput, { target: { value: 'Doe' } });

    expect(saveButton.style.opacity).toBe('100');

    fireEvent.click(saveButton);

    await waitFor(async () => {
      const message = screen.queryByText('The changes have been saved');
      expect(message).not.toBeNull();
    });

    expect(saveButton.style.opacity).toBe('0');

    fireEvent.change(firstNameInput, { target: { value: 'Hello' } });
    fireEvent.change(lastNameInput, { target: { value: 'World' } });

    expect(saveButton.style.opacity).toBe('100');

    fireEvent.change(firstNameInput, { target: { value: 'John' } });
    fireEvent.change(lastNameInput, { target: { value: 'Doe' } });

    expect(saveButton.style.opacity).toBe('0');
  });

  it('should display user data error', async () => {
    serverConfiguration.forceError = '/users/update-data';

    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const firstNameInput = await screen.findByTestId('first-name-input');
    const lastNameInput = await screen.findByTestId('last-name-input');
    const saveButton = await screen.findByTestId('save-button');

    expect(saveButton.style.opacity).toBe('0');

    fireEvent.change(firstNameInput, { target: { value: 'Something' } });
    fireEvent.change(lastNameInput, { target: { value: 'Else' } });

    expect(saveButton.style.opacity).toBe('100');

    fireEvent.click(saveButton);

    await screen.findByText('An error has occurred while saving data');
  });

  it('should initiate password reset', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const securityTab = await screen.findByText('Security');

    fireEvent.click(securityTab);

    const resetPasswordButton = await screen.findByTestId('reset-password-button');

    fireEvent.click(resetPasswordButton);

    await waitFor(async () => {
      const message = screen.queryByText(
        'An email with further instructions has been sent',
      );
      expect(message).not.toBeNull();
    });
  });

  it('should display password reset error', async () => {
    serverConfiguration.forceError = '/users/change-password';

    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const securityTab = await screen.findByText('Security');

    fireEvent.click(securityTab);

    const resetPasswordButton = await screen.findByTestId('reset-password-button');

    fireEvent.click(resetPasswordButton);

    await screen.findByText('An error has occurred while trying to reset password');
  });

  it('should display organizations', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const organizationsTab = await screen.findByText('Organizations');

    fireEvent.click(organizationsTab);

    const organizationItems = await screen.findAllByTestId('organization-item');
    expect(organizationItems).toHaveLength(2);
  });

  it('should leave organization', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const organizationsTab = await screen.findByText('Organizations');

    fireEvent.click(organizationsTab);

    const leaveButtons = await screen.findAllByTestId('leave-organization-button');

    fireEvent.click(leaveButtons[0]);

    const yesButton = await screen.findByTestId('yes-button');

    fireEvent.click(yesButton);

    await waitFor(async () => {
      const message = screen.queryByText('You have left Organization 1');
      expect(message).not.toBeNull();
    });
  });

  it('should display leave organization error', async () => {
    serverConfiguration.forceError = '/leave';

    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const organizationsTab = await screen.findByText('Organizations');

    fireEvent.click(organizationsTab);

    const leaveButtons = await screen.findAllByTestId('leave-organization-button');

    fireEvent.click(leaveButtons[0]);

    const yesButton = await screen.findByTestId('yes-button');

    fireEvent.click(yesButton);

    await screen.findByText('An error has occurred while leaving Organization 1');
  });

  it('should render export tab', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.PROFILE);
    render(<PageTestContainer />);

    const exportTab = await screen.findByText('Export data');

    fireEvent.click(exportTab);

    const exportButton = await screen.findByTestId('export-button');
    expect(exportButton).toBeDefined();
  });
});

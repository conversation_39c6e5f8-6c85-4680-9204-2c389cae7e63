.container {
  background-color: $lightGray;
  min-height: calc(100vh - $headerHeight);
  padding: 4rem 3.6rem;
  max-width: 100%;
}

.select1,
.select2 {
  margin: 0 0.8rem;
}

.select1 {
  width: 13rem;
}

.select2 {
  width: 10rem;
}

.rangePicker {
  margin-left: 0.8rem;
  width: 25rem;
}

.spinner {
  margin: 2rem 0 0 1rem;
}

.chartTitle {
  font-size: 2rem;
  margin-top: 2rem;
}

.noDataText {
  margin-top: 1rem;
}

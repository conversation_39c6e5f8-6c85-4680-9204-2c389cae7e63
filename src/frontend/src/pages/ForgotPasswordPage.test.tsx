import { render, screen, waitFor, fireEvent } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('ForgotPasswordPage', () => {
  const serverConfiguration = setupServer();

  it('should reset password', async () => {
    serverConfiguration.loggedIn = false;

    const { PageTestContainer } = preparePageTest(ROUTES.FORGOT_PASSWORD);
    render(<PageTestContainer />);

    const emailInput = await screen.findByTestId('email-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await waitFor(async () => {
      const message = screen.queryByText(
        'An email with further instructions has been sent',
      );
      expect(message).not.toBeNull();
    });
  });

  it('should display reset password error', async () => {
    serverConfiguration.loggedIn = false;
    serverConfiguration.forceError = '/users/forgot-password';

    const { PageTestContainer } = preparePageTest(ROUTES.FORGOT_PASSWORD);
    render(<PageTestContainer />);

    const emailInput = await screen.findByTestId('email-input');
    const submitButton = await screen.findByTestId('submit-button');

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);

    await screen.findByText('An error has occurred while trying to reset password');
  });
});

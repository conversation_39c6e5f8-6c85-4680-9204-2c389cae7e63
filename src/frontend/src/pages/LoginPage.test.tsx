import { render, screen, waitFor, fireEvent } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('LoginPage', () => {
  const serverConfiguration = setupServer();

  it('should not log in', async () => {
    serverConfiguration.loggedIn = false;

    const { router, PageTestContainer } = preparePageTest(ROUTES.LOGIN);
    render(<PageTestContainer />);

    const emailInput = await screen.findByTestId('email-input');
    const passwordInput = await screen.findByTestId('password-input');
    const loginButton = await screen.findByTestId('login-button');

    // Incorrect login detail
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'wrong-password' } });
    fireEvent.click(loginButton);

    // After entering wrong password, user should still be on login page
    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.LOGIN);
    });

    await screen.findByText('You must have entered wrong email or password');
  });

  it('should log in', async () => {
    serverConfiguration.loggedIn = false;

    const { router, PageTestContainer } = preparePageTest(ROUTES.LOGIN);
    render(<PageTestContainer />);

    const emailInput = await screen.findByTestId('email-input');
    const passwordInput = await screen.findByTestId('password-input');
    const loginButton = await screen.findByTestId('login-button');

    // Correct login details
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'correct-password' } });
    fireEvent.click(loginButton);

    // After successfully logging in, user should be redirected to dashboard
    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.DASHBOARD);
    });
  });
});

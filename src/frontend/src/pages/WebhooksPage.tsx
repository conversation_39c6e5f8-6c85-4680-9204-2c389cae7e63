// import { Trans } from '@lingui/macro';

// import { WebhookItem } from '@components';

// import { useActiveOrganization, useWebhooks } from '@hooks';

// import styles from './WebhooksPage.module.scss';

// const WebhooksPage = () => {
//   const activeOrganization = useActiveOrganization();
//   const { data, isSuccess } = useWebhooks(activeOrganization);

//   return (
//     <div className={styles.container}>
//       <div className={styles.content}>
//         <div className={styles.topRow}>
//           <h1 className={styles.title}>
//             <Trans>Webhooks</Trans>
//           </h1>
//         </div>
//         {isSuccess &&
//           (!data || data.length === 0 ? (
//             <p>
//               <Trans>No webhooks are currently available</Trans>
//             </p>
//           ) : (
//             data.map((webhook: Webhook) => (
//               <WebhookItem key={webhook.uuid} webhook={webhook} />
//             ))
//           ))}
//       </div>
//     </div>
//   );
// };

// export default WebhooksPage;

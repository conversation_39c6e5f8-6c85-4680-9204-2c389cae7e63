import { render, screen } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('NotFoundPage', () => {
  const serverConfiguration = setupServer();

  it('should render dashboard redirect', async () => {
    const { PageTestContainer } = preparePageTest('/something');
    render(<PageTestContainer />);

    const button = await screen.findByTestId('404-button');
    const link = button.parentElement;

    expect(link).toHaveAttribute('href', ROUTES.DASHBOARD);
  });

  it('should render login redirect', async () => {
    serverConfiguration.loggedIn = false;

    const { PageTestContainer } = preparePageTest('/something');
    render(<PageTestContainer />);

    const button = await screen.findByTestId('404-button');
    const link = button.parentElement;

    expect(link).toHaveAttribute('href', ROUTES.LOGIN);
  });
});

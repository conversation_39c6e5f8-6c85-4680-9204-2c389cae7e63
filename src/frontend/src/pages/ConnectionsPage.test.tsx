import { render, screen } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('ConnectionsPage', () => {
  const serverConfiguration = setupServer();

  it('should render connection items', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.CONNECTIONS);
    render(<PageTestContainer />);

    const connectionItems = await screen.findAllByTestId('connection-item');
    expect(connectionItems).toHaveLength(2);
  });

  it('should display connections error', async () => {
    serverConfiguration.forceError = 'retrieve-connections';

    const { PageTestContainer } = preparePageTest(ROUTES.CONNECTIONS);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching connections');
  });
});

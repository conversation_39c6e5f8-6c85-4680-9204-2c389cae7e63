import { render, screen, within, fireEvent, waitFor } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('AnalyticsPage', () => {
  const serverConfiguration = setupServer();

  it('should render correctly', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.ANALYTICS);
    render(<PageTestContainer />);

    // Switch option from 'Organization' to 'Project'
    const optionSelect = await screen.findByTestId('option-select');
    let selectionItem = await within(optionSelect).findByTitle('Organization');
    fireEvent.mouseDown(selectionItem);
    const optionSelectDropdown = screen.getByTestId('option-select-dropdown');
    const projectOption = await within(optionSelectDropdown).findByText('Project');
    fireEvent.click(projectOption);

    // // Switch option from 'Project' to 'Flow'
    // optionSelect = await screen.findByTestId('option-select');
    // selectionItem = await within(optionSelect).findByTitle('Project');
    // fireEvent.mouseDown(selectionItem);
    // optionSelectDropdown = screen.getByTestId('option-select-dropdown');
    // const flowOption = await within(optionSelectDropdown).findByText('Flow');
    // fireEvent.click(flowOption);

    // Switch frequency from 'Daily' to 'Weekly'
    let frequencySelect = await screen.findByTestId('frequency-select');
    selectionItem = await within(frequencySelect).findByTitle('Daily');
    fireEvent.mouseDown(selectionItem);
    let frequencySelectDropdown = screen.getByTestId('frequency-select-dropdown');
    const weeklyOption = await within(frequencySelectDropdown).findByText('Weekly');
    fireEvent.click(weeklyOption);

    // Switch frequency from 'Weekly' to 'Monthly'
    frequencySelect = await screen.findByTestId('frequency-select');
    selectionItem = await within(frequencySelect).findByTitle('Weekly');
    fireEvent.mouseDown(selectionItem);
    frequencySelectDropdown = screen.getByTestId('frequency-select-dropdown');
    const monthlyOption = await within(frequencySelectDropdown).findByText('Monthly');
    fireEvent.click(monthlyOption);

    const daterangePickerInputs = await screen.findAllByTestId('daterange-picker');
    expect(daterangePickerInputs).toHaveLength(2);
    fireEvent.click(daterangePickerInputs[0]);
    const daterangePanel = await screen.findByTestId('daterange-panel');

    await waitFor(() => {
      const startDay = within(daterangePanel).getAllByText('1')[0];
      fireEvent.click(startDay);
      const endDay = within(daterangePanel).getAllByText('28')[1];
      fireEvent.click(endDay);
    });

    await screen.findAllByTestId('daterange-picker');
  });

  it('should display analytics error', async () => {
    serverConfiguration.forceError = '/analytics';

    const { PageTestContainer } = preparePageTest(ROUTES.ANALYTICS);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching analytics');
  });
});

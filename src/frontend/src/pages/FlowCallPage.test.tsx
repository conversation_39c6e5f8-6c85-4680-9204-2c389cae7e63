import {
  render,
  screen,
  waitFor,
  within,
  // fireEvent,
} from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('FlowCallPage', () => {
  const serverConfiguration = setupServer();

  it('should render schema and node calls table', async () => {
    const { PageTestContainer } = preparePageTest(
      `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
    );
    render(<PageTestContainer />);

    await waitFor(() => {
      const flowSchema = screen.queryByTestId('flow-builder-container');
      expect(flowSchema).not.toBeNull();
    });

    const nodeCallsTable = await screen.findByTestId('node-calls-table');
    const rows = await within(nodeCallsTable).findAllByTestId('table-row');
    expect(rows).toHaveLength(4);
  });

  it('should display flow call error', async () => {
    serverConfiguration.forceError = '/flows/node-calls';

    const { PageTestContainer } = preparePageTest(
      `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
    );
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching flow call');
  });

  // it('should display node call data error', async () => {
  //   const { PageTestContainer } = preparePageTest(
  //     `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
  //   );
  //   render(<PageTestContainer />);

  //   const nodeCallsTable = await screen.findByTestId('node-calls-table');
  //   const rows = await within(nodeCallsTable).findAllByTestId('table-row');
  //   expect(rows).toHaveLength(4);

  //   const errorRow = rows[0];
  //   fireEvent.click(errorRow);

  //   await waitFor(() => {
  //     const error = screen.queryByText(
  //       "'ActionCall' object has no attribute 'is_datastore_changed'",
  //     );
  //     expect(error).not.toBeNull();
  //   });
  // });

  // it('should display node call detail', async () => {
  //   const { PageTestContainer } = preparePageTest(
  //     `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
  //   );
  //   render(<PageTestContainer />);

  //   const nodeCallsTable = await screen.findByTestId('node-calls-table');
  //   const rows = await within(nodeCallsTable).findAllByTestId('table-row');
  //   expect(rows).toHaveLength(4);

  //   const successRow = rows[1];
  //   fireEvent.click(successRow);

  //   const inputDataJsonContainer = await screen.findByTestId('input-data-json');
  //   await waitFor(() => {
  //     const exampleContent = within(inputDataJsonContainer).queryByText(
  //       '"Make homemade ice cream"',
  //     );
  //     expect(exampleContent).not.toBeNull();
  //   });

  //   const outputDataJsonContainer = await screen.findByTestId('output-data-json');
  //   await waitFor(() => {
  //     const exampleContent = within(outputDataJsonContainer).queryByText(
  //       '"Learn to sew on a button"',
  //     );
  //     expect(exampleContent).not.toBeNull();
  //   });
  // });

  // it('should display node call detail error', async () => {
  //   serverConfiguration.forceError = '/mongo';

  //   const { PageTestContainer } = preparePageTest(
  //     `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
  //   );
  //   render(<PageTestContainer />);

  //   const nodeCallsTable = await screen.findByTestId('node-calls-table');
  //   const rows = await within(nodeCallsTable).findAllByTestId('table-row');
  //   expect(rows).toHaveLength(4);

  //   const successRow = rows[1];
  //   fireEvent.click(successRow);

  //   await screen.findAllByText('An error has occurred while fetching node call detail');
  // });
});

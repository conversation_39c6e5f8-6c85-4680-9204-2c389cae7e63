import { render, screen, waitFor, within, fireEvent } from '@testing-library/react';

import { ROUTES } from '@config';

import { setupServer, preparePageTest } from '@tests';

describe('DashboardPage', () => {
  const serverConfiguration = setupServer();

  it('should display user data error', async () => {
    serverConfiguration.forceError = '/users/user';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching user data');
  });

  it('should change organization', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const flowCallsTable = await screen.findByTestId('flow-calls-table');
    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(3);
    });

    const organizationSelect = await screen.findByTestId('organization-select');
    const selectionItem = await within(organizationSelect).findByTitle(
      'Organization 1',
    );
    fireEvent.mouseDown(selectionItem);
    const organizationSelectDropdown = screen.getByTestId(
      'organization-select-dropdown',
    );
    const organizationOption = await within(organizationSelectDropdown).findByText(
      'Organization 2',
    );
    fireEvent.click(organizationOption);

    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(1);
    });
  });

  it('should change project', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const flowCallsTable = await screen.findByTestId('flow-calls-table');
    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(3);
    });

    const projectSelect = await screen.findByTestId('project-select');
    const selectionItem = await within(projectSelect).findByTitle('Project 1');
    fireEvent.mouseDown(selectionItem);
    const projectSelectDropdown = screen.getByTestId('project-select-dropdown');
    const projectOption = await within(projectSelectDropdown).findByText('Project 2');
    fireEvent.click(projectOption);

    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(1);
    });
  });

  it('should change flow', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const flowCallsTable = await screen.findByTestId('flow-calls-table');
    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(3);
    });

    const flowSelect = await screen.findByTestId('flow-select');
    const selectionItem = await within(flowSelect).findByTitle('All flows');
    fireEvent.mouseDown(selectionItem);
    const flowSelectDropdown = screen.getByTestId('flow-select-dropdown');
    const flowOption = await within(flowSelectDropdown).findByText('Flow 1');
    fireEvent.click(flowOption);

    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(2);
    });
  });

  it('should display organizations error', async () => {
    serverConfiguration.forceError = '/organizations';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching organizations');
  });

  it('should display projects error', async () => {
    serverConfiguration.forceError = '/projects';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching projects');
  });

  it('should display flows error', async () => {
    serverConfiguration.forceError = '/flows';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching flows');
  });

  it('should switch language', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const languageSelect = await screen.findByTestId('language-select');
    const enSelectionItem = await within(languageSelect).findByTitle('en');
    fireEvent.mouseDown(enSelectionItem);
    const languageSelectDropdown = screen.getByTestId('language-select-dropdown');
    const csOption = await within(languageSelectDropdown).findByTitle('cs');
    fireEvent.click(csOption);

    const csSelectionItem = await within(languageSelect).findByTitle('cs');
    fireEvent.mouseDown(csSelectionItem);
    const enOption = await within(languageSelectDropdown).findByTitle('en');
    fireEvent.click(enOption);
  });

  it('should navigate to profile', async () => {
    const { router, PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const userButton = await screen.findByTestId('user-button');
    fireEvent.mouseOver(userButton);

    const profileButton = await screen.findByText('Manage profile');
    fireEvent.click(profileButton);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.PROFILE);
    });
  });

  it('should log out', async () => {
    const { router, PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const userButton = await screen.findByTestId('user-button');
    fireEvent.mouseOver(userButton);

    const logoutButton = await screen.findByText('Log out');
    fireEvent.click(logoutButton);

    await waitFor(() => {
      expect(router.state.location.pathname).toBe(ROUTES.LOGIN);
    });
  });

  it('should display logout error', async () => {
    serverConfiguration.forceError = '/logout';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const userButton = await screen.findByTestId('user-button');
    fireEvent.mouseOver(userButton);

    const logoutButton = await screen.findByText('Log out');
    fireEvent.click(logoutButton);

    await screen.findByText('An error has occurred while logging out');
  });

  it('should collapse sidebar', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const collapseButtonOpen = await screen.findByTestId('collapse-button-open');
    fireEvent.click(collapseButtonOpen);

    const collapseButtonClose = await screen.findByTestId('collapse-button-close');
    fireEvent.click(collapseButtonClose);

    await screen.findByTestId('collapse-button-open');
  });

  it('should render flow calls table', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    const flowCallsTable = await screen.findByTestId('flow-calls-table');
    await waitFor(() => {
      const rows = within(flowCallsTable).queryAllByTestId('table-row');
      expect(rows).toHaveLength(3);

      const [failedRow1, successRow, failedRow2] = rows;
      expect(failedRow1.classList.contains('table-row-failed')).toBe(true);
      expect(successRow.classList.contains('table-row-success')).toBe(true);
      expect(failedRow2.classList.contains('table-row-failed')).toBe(true);
    });
  });

  it('should display flow calls error', async () => {
    serverConfiguration.forceError = '/analytics';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    await screen.findByText('An error has occurred while fetching flow calls');
  });

  // it('should redirect to flow call detail', async () => {
  //   const { router, PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
  //   render(<PageTestContainer />);

  //   const flowCallsTable = await screen.findByTestId('flow-calls-table');
  //   const rows = await within(flowCallsTable).findAllByTestId('table-row');
  //   expect(rows).toHaveLength(3);

  //   const row = rows[0];
  //   const detailButton = await within(row).findByTestId('detail-button');

  //   fireEvent.click(detailButton);

  //   await waitFor(() => {
  //     expect(router.state.location.pathname).toBe(
  //       `${ROUTES.FLOW}/flow-uuid-1${ROUTES.FLOW_CALL}/flow-call-uuid-1`,
  //     );
  //   });
  // });

  it('should display flow schema', async () => {
    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    // Wait for things to render
    await screen.findByTestId('flow-calls-table');

    // Initially, flow schema is hidden
    expect(screen.queryByTestId('flow-builder-container')).toBeNull();

    // We need to change selected flow from
    // the default 'All flows' to 'Flow', since when 'All flows'
    // is selected, schema manipulation is disabled
    const flowSelect = await screen.findByTestId('flow-select');
    const selectionItem = await within(flowSelect).findByTitle('All flows');
    fireEvent.mouseDown(selectionItem);
    const flowSelectDropdown = screen.getByTestId('flow-select-dropdown');
    const flowOption = await within(flowSelectDropdown).findByText('Flow 1');
    fireEvent.click(flowOption);

    // Now schema manipulation is enabled, therefore we click
    // the 'Show schema' button to display flow schema
    const schemaHider = await screen.findByTestId('show-hide-button');
    fireEvent.click(schemaHider);

    // Flow schema should now be visible
    expect(screen.queryByTestId('flow-builder-container')).not.toBeNull();

    await screen.findByTestId('show-hide-button');
    fireEvent.click(schemaHider);

    await screen.findByTestId('show-hide-button');
  });

  it('should display flow schema error', async () => {
    serverConfiguration.forceError = '/flows/retrieve';

    const { PageTestContainer } = preparePageTest(ROUTES.DASHBOARD);
    render(<PageTestContainer />);

    // Wait for things to render
    await screen.findByTestId('flow-calls-table');

    // Initially, flow schema is hidden
    expect(screen.queryByTestId('flow-builder-container')).toBeNull();

    // We need to change selected flow from
    // the default 'All flows' to 'Flow', since when 'All flows'
    // is selected, schema manipulation is disabled
    const flowSelect = await screen.findByTestId('flow-select');
    const selectionItem = await within(flowSelect).findByTitle('All flows');
    fireEvent.mouseDown(selectionItem);
    const flowSelectDropdown = screen.getByTestId('flow-select-dropdown');
    const flowOption = await within(flowSelectDropdown).findByText('Flow 1');
    fireEvent.click(flowOption);

    // Now schema manipulation is enabled, therefore we click
    // the 'Show schema' button to display flow schema
    const schemaHider = await screen.findByTestId('show-hide-button');
    fireEvent.click(schemaHider);

    await screen.findByText('An error has occurred while fetching flow schema');
  });
});

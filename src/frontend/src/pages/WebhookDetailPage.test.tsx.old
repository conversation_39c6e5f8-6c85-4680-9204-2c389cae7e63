// import { render, screen } from '@testing-library/react';

// import { ROUTES } from '@config';

// import { setupServer, preparePageTest } from '@tests';

// describe('WebhookDetailPage', () => {
//   setupServer();

//   it('should render correctly', async () => {
//     const { PageTestContainer } = preparePageTest(`${ROUTES.WEBHOOKS}/webhook-uuid-1`);
//     render(<PageTestContainer />);

//     const webhookSwitch = await screen.findByTestId('webhook-switch');
//     expect(webhookSwitch).toHaveClass('ant-switch-checked');
//   });
// });

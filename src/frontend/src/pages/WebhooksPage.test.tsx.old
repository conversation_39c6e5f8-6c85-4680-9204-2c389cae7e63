// import { render, screen, waitFor, fireEvent } from '@testing-library/react';
// import { within } from '@testing-library/dom';

// import { ROUTES } from '@config';

// import { setupServer, preparePageTest } from '@tests';

// describe('WebhooksPage', () => {
//   const serverConfiguration = setupServer();

//   it('should render webhook items', async () => {
//     const { PageTestContainer } = preparePageTest(ROUTES.WEBHOOKS);
//     render(<PageTestContainer />);

//     const webhookItems = await screen.findAllByTestId('webhook-item');
//     expect(webhookItems).toHaveLength(2);
//   });

//   it('should display webhooks error', async () => {
//     serverConfiguration.forceError = '/webhooks';

//     const { PageTestContainer } = preparePageTest(ROUTES.WEBHOOKS);
//     render(<PageTestContainer />);

//     await screen.findByText('An error has occurred while fetching webhooks');
//   });

//   it('should render link to webhook detail', async () => {
//     const { PageTestContainer } = preparePageTest(ROUTES.WEBHOOKS);
//     render(<PageTestContainer />);

//     const buttons = await screen.findAllByTestId('detail-button');
//     const link = buttons[0].parentElement;
//     expect(link).toHaveAttribute('href', `${ROUTES.WEBHOOKS}/webhook-uuid-1`);
//   });

//   it('should deactivate webhook', async () => {
//     const { PageTestContainer } = preparePageTest(ROUTES.WEBHOOKS);
//     render(<PageTestContainer />);

//     const webhooks = await screen.findAllByTestId('webhook-item');
//     const activeWebhook = webhooks[0];
//     const webhookSwitch = within(activeWebhook).getByTestId('webhook-switch');
//     expect(webhookSwitch).toHaveClass('ant-switch-checked');

//     fireEvent.click(webhookSwitch);

//     waitFor(() => {
//       expect(webhookSwitch).not.toHaveClass('ant-switch-checked');
//     });
//   });

//   it('should activate webhook', async () => {
//     const { PageTestContainer } = preparePageTest(ROUTES.WEBHOOKS);
//     render(<PageTestContainer />);

//     const webhooks = await screen.findAllByTestId('webhook-item');
//     const inactiveWebhook = webhooks[1];
//     const webhookSwitch = within(inactiveWebhook).getByTestId('webhook-switch');
//     expect(webhookSwitch).not.toHaveClass('ant-switch-checked');

//     fireEvent.click(webhookSwitch);

//     waitFor(() => {
//       expect(webhookSwitch).toHaveClass('ant-switch-checked');
//     });
//   });
// });

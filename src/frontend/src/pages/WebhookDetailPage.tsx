// import { WebhookDetail } from '@components';

// import {
//   useParamIdOrRedirect,
//   // useAllFlows,
//   useWebhookDetail,
// } from '@hooks';

// const WebhookDetailPage = () => {
//   const webhookUuid = useParamIdOrRedirect('webhook', true) as string;
//   // const flows = useAllFlows();

//   const { data } = useWebhookDetail(webhookUuid);

//   if (data) {
//     // const dataWithFlows = {
//     //   ...data,
//     //   flows: data.flows.map((flowId: number) => {
//     //     const targetFlow = flows.filter((flow: Flow) => flow.id === flowId)[0];
//     //     return targetFlow || null;
//     //   }),
//     // };
//     // return <WebhookDetail webhook={dataWithFlows as Webhook} />;
//     return <WebhookDetail webhook={data} />;
//   }
// };

// export default WebhookDetailPage;

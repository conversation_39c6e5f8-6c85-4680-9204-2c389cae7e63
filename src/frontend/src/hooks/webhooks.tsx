// import { useQuery, useQueryClient } from '@tanstack/react-query';

// import { API, QUERY_KEYS } from '@config';

// import { useRequest, useDisplayResponseMessage } from '@hooks';

// export const useWebhooks = (organization: Organization | null) => {
//   const request = useRequest();
//   const displayResponseMessage = useDisplayResponseMessage();

//   return useQuery({
//     enabled: !!organization,
//     queryKey: [QUERY_KEYS.ORGANIZATION_WEBHOOKS, organization?.uuid],
//     queryFn: async () => {
//       try {
//         const { data } = await request(API.WEBHOOKS(organization?.uuid as string));
//         return data as Webhook[];
//       } catch (error) {
//         console.error(error);
//         displayResponseMessage('webhooks.fetch.error');
//         return null;
//       }
//     },
//   });
// };

// export const useActivateWebhook = (webhook: Webhook) => {
//   const request = useRequest();
//   const queryClient = useQueryClient();

//   return async () => {
//     try {
//       await request(API.WEBHOOKS_ACTIVATE(webhook.uuid), {
//         method: 'POST',
//       });
//       queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ORGANIZATION_WEBHOOKS] });
//     } catch (error) {
//       console.error(error);
//     }
//   };
// };

// export const useWebhookDetail = (webhookUuid: string) => {
//   const request = useRequest();

//   return useQuery({
//     queryKey: [QUERY_KEYS.WEBHOOK_DETAIL, webhookUuid],
//     queryFn: async () => {
//       try {
//         const { data } = await request(API.WEBHOOKS_DETAIL(webhookUuid));
//         return data;
//       } catch (error) {
//         console.error(error);
//       }
//     },
//   });
// };

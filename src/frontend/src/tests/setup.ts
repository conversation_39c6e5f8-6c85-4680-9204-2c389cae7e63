import axios from 'axios';
import { expect } from 'vitest';
import { cleanup, configure } from '@testing-library/react';
import matchers from '@testing-library/jest-dom/matchers';
import MatchMediaMock from 'vitest-matchmedia-mock';

configure({
  asyncUtilTimeout: process.env.ASYNC_UTIL_TIMEOUT || 4000,
});

// extends Vitest's expect method with methods from react-testing-library
expect.extend(matchers);

let matchMediaMock = new MatchMediaMock();

afterAll(() => {
  cleanup();
  matchMediaMock.clear();
});

Object.defineProperty(window, 'scrollTo', { value: () => {}, writable: true });

const { getComputedStyle } = window;
window.getComputedStyle = (elt) => getComputedStyle(elt);

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}

window.ResizeObserver = ResizeObserver;

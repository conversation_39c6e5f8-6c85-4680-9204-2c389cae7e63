import React from 'react';
import { createM<PERSON>oryRouter, RouterProvider } from 'react-router-dom';

import {
  APP_ROUTES,
  WithAntdConfig,
  WithProviders,
  WithInitialData,
  WithLingui,
  WithQueryClient,
} from '@app';

const preparePageTest = (page: string) => {
  const router = createMemoryRouter(APP_ROUTES, {
    initialEntries: [page],
  });

  return {
    router,
    PageTestContainer: () => (
      <React.StrictMode>
        <WithQueryClient>
          <WithProviders>
            <WithLingui>
              <WithAntdConfig>
                <WithInitialData>
                  <RouterProvider router={router} />
                </WithInitialData>
              </WithAntdConfig>
            </WithLingui>
          </WithProviders>
        </WithQueryClient>
      </React.StrictMode>
    ),
  };
};

export default preparePageTest;

import { http, HttpResponse } from 'msw';

const getCoreHandlers = (serverConfiguration: TestServerConfiguration) => {
  return [
    http.get('/api/v1/core/organizations/list/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json([
        {
          uuid: 'organization-uuid-1',
          name: 'Organization 1',
        },
        {
          uuid: 'organization-uuid-2',
          name: 'Organization 2',
        },
      ]);
    }),
    http.get('/api/v1/core/projects/list/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const organizationUuid = url.searchParams.get('organization_uuids');

      if (organizationUuid === 'organization-uuid-1') {
        return HttpResponse.json([
          {
            uuid: 'project-uuid-1',
            name: 'Project 1',
          },
          {
            uuid: 'project-uuid-2',
            name: 'Project 2',
          },
        ]);
      } else if (organizationUuid === 'organization-uuid-2') {
        return HttpResponse.json([
          {
            uuid: 'project-uuid-3',
            name: 'Project 3',
          },
        ]);
      }
    }),
    http.get(
      '/api/v1/core/organizations/retrieve-connections/organization-uuid-1/',
      ({ request }) => {
        if (!serverConfiguration.loggedIn) {
          return new HttpResponse(null, { status: 400 });
        }
        if (
          serverConfiguration.forceError &&
          request.url.includes(serverConfiguration.forceError)
        ) {
          return new HttpResponse(null, { status: 500 });
        }

        return HttpResponse.json({
          uuid: 'organization-uuid-1',
          name: 'Organization',
          connections: [
            {
              uuid: 'connection-uuid-1',
              name: 'Connection 1',
              created_at: '2024-02-23T19:02:53.288096Z',
              node_count: 5,
            },
            {
              uuid: 'connection-uuid-2',
              name: 'Connection 2',
              created_at: '2024-02-23T19:02:53.288096Z',
              node_count: 10,
            },
          ],
        });
      },
    ),
    http.post(
      '/api/v1/core/organizations/organization-uuid-1/leave/',
      ({ request }) => {
        if (!serverConfiguration.loggedIn) {
          return new HttpResponse(null, { status: 400 });
        }
        if (
          serverConfiguration.forceError &&
          request.url.includes(serverConfiguration.forceError)
        ) {
          return new HttpResponse(null, { status: 500 });
        }

        return new HttpResponse(null);
      },
    ),
  ];
};

export default getCoreHandlers;

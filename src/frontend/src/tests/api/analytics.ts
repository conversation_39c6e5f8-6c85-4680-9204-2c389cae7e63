import { http, HttpResponse } from 'msw';

const FLOW_1_CALLS = [
  {
    _id: 'flow-call-uuid-1',
    uuid: 'flow-uuid-1',
    name: 'Flow 1',
    start: '2024-03-24T17:15:31',
    status: {
      code: 1,
      name: 'Failed',
    },
    executed_node_count: 1,
    total_node_count: 1,
    end: '2024-03-24T17:15:31',
  },
  {
    _id: 'flow-call-uuid-2',
    uuid: 'flow-uuid-1',
    name: 'Flow 1',
    start: '2024-03-23T17:15:31',
    status: {
      code: 0,
      name: 'Success',
    },
    executed_node_count: 1,
    total_node_count: 1,
    end: '2024-03-23T17:15:31',
  },
];

const FLOW_2_CALLS = [
  {
    _id: 'flow-call-uuid-3',
    uuid: 'flow-uuid-2',
    name: 'Flow 2',
    start: '2024-03-22T17:15:31',
    status: {
      code: 1,
      name: 'Failed',
    },
    executed_node_count: 1,
    total_node_count: 1,
    end: '2024-03-22T17:15:31',
  },
];

const FLOW_3_CALLS = [
  {
    _id: 'flow-call-uuid-3',
    uuid: 'flow-uuid-3',
    name: 'Flow 3',
    start: '2024-03-21T17:15:31',
    status: {
      code: 1,
      name: 'Failed',
    },
    executed_node_count: 1,
    total_node_count: 1,
    end: '2024-03-21T17:15:31',
  },
];

const FLOW_4_CALLS = [
  {
    _id: 'flow-call-uuid-4',
    uuid: 'flow-uuid-4',
    name: 'Flow 4',
    start: '2024-03-20T17:15:31',
    status: {
      code: 1,
      name: 'Failed',
    },
    executed_node_count: 1,
    total_node_count: 1,
    end: '2024-03-20T17:15:31',
  },
];

const FLOW_CALL_1_NODE_CALLS = [
  {
    uuid: 'node-uuid-1',
    start: '2024-03-25T12:15:09',
    request_count: 5,
    status: {
      code: 1,
      name: 'Failed',
    },
    errors: ["'ActionCall' object has no attribute 'is_datastore_changed'"],
    input_data: null,
    end: '2024-03-25T12:15:13',
    output_data: null,
    raw_data: null,
    retries: 0,
  },
  {
    uuid: 'node-uuid-2',
    start: '2024-03-24T12:15:09',
    request_count: 1,
    status: {
      code: 0,
      name: 'Success',
    },
    errors: [],
    input_data: ['65fb660ab663e858de0e404b'],
    end: '2024-03-24T12:15:13',
    output_data: ['65fb660ab663e858de0e404b', '65fb660cb663e858de0e404f'],
    raw_data: null,
    retries: 0,
  },
  {
    uuid: 'node-uuid-3',
    start: '2024-03-23T12:15:09',
    request_count: 1,
    status: {
      code: 2,
      name: 'Retrying',
    },
    errors: [],
    input_data: null,
    end: '2024-03-23T12:15:13',
    output_data: null,
    raw_data: null,
    retries: 0,
  },
  {
    uuid: 'node-uuid-4',
    start: '2024-03-22T12:15:09',
    request_count: 1,
    status: {
      code: 3,
      name: 'In progress',
    },
    errors: [],
    input_data: null,
    end: '2024-03-22T12:15:13',
    output_data: null,
    raw_data: null,
    retries: 0,
  },
];

const getAnalyticsHandlers = (serverConfiguration: TestServerConfiguration) => {
  return [
    http.get('/api/v1/analytics/organizations/logs/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const organizationUuid = url.searchParams.get('organization_uuids');

      if (organizationUuid === 'organization-uuid-1') {
        return HttpResponse.json({
          logs: [...FLOW_1_CALLS, ...FLOW_2_CALLS, ...FLOW_3_CALLS],
          total_count: 4,
        });
      } else if (organizationUuid === 'organization-uuid-2') {
        return HttpResponse.json({
          logs: FLOW_4_CALLS,
          total_count: 1,
        });
      }
    }),
    http.get('/api/v1/analytics/organizations/node-calls/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const organizationUuid = url.searchParams.get('organization_uuids');

      if (organizationUuid === 'organization-uuid-1') {
        return HttpResponse.json({
          node_calls: [...FLOW_1_CALLS, ...FLOW_2_CALLS, ...FLOW_3_CALLS],
          total_count: 4,
        });
      } else if (organizationUuid === 'organization-uuid-2') {
        return HttpResponse.json({
          node_calls: FLOW_4_CALLS,
          total_count: 1,
        });
      }
    }),
    http.get('/api/v1/analytics/projects/logs/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const projectUuid = url.searchParams.get('project_uuids');

      if (projectUuid === 'project-uuid-1') {
        return HttpResponse.json({
          logs: [...FLOW_1_CALLS, ...FLOW_2_CALLS],
          total_count: 3,
        });
      } else if (projectUuid === 'project-uuid-2') {
        return HttpResponse.json({
          logs: FLOW_3_CALLS,
          total_count: 1,
        });
      } else if (projectUuid === 'project-uuid-3') {
        return HttpResponse.json({
          logs: FLOW_4_CALLS,
          total_count: 1,
        });
      }
    }),
    http.get('/api/v1/analytics/projects/node-calls/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const projectUuid = url.searchParams.get('project_uuids');

      if (projectUuid === 'project-uuid-1') {
        return HttpResponse.json({
          node_calls: [...FLOW_1_CALLS, ...FLOW_2_CALLS],
          total_count: 3,
        });
      } else if (projectUuid === 'project-uuid-2') {
        return HttpResponse.json({
          node_calls: FLOW_3_CALLS,
          total_count: 1,
        });
      } else if (projectUuid === 'project-uuid-3') {
        return HttpResponse.json({
          node_calls: FLOW_4_CALLS,
          total_count: 1,
        });
      }
    }),
    http.get('/api/v1/analytics/flows/logs/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const flowUuid = url.searchParams.get('flow_uuids');

      if (flowUuid === 'flow-uuid-1') {
        return HttpResponse.json({
          logs: FLOW_1_CALLS,
          total_count: 2,
        });
      } else if (flowUuid === 'flow-uuid-2') {
        return HttpResponse.json({
          logs: FLOW_2_CALLS,
          total_count: 1,
        });
      } else if (flowUuid === 'flow-uuid-3') {
        return HttpResponse.json({
          logs: FLOW_3_CALLS,
          total_count: 1,
        });
      } else if (flowUuid === 'flow-uuid-4') {
        return HttpResponse.json({
          logs: FLOW_4_CALLS,
          total_count: 1,
        });
      }
    }),
    http.get('/api/v1/analytics/flows/node-calls/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const flowCallId = url.searchParams.get('mongo_flow_ids');

      if (flowCallId === 'flow-call-uuid-1') {
        return HttpResponse.json({
          node_calls: FLOW_CALL_1_NODE_CALLS,
        });
      } else if (flowCallId === 'flow-call-uuid-2') {
        return HttpResponse.json({
          node_calls: [],
        });
      }
    }),
  ];
};

export default getAnalyticsHandlers;

import { http, HttpResponse } from "msw";

const getUserHandlers = (serverConfiguration: TestServerConfiguration) => {
  const USER: User = {
    uuid: "user-uuid",
    email: "<EMAIL>",
    first_name: "Testing",
    last_name: "User",
    avatar: "https://github.com/philip.png",
  };

  return [
    http.get("/api/v1/users/user/", ({ request }) => {
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      if (serverConfiguration.loggedIn) {
        return HttpResponse.json(USER);
      } else {
        return new HttpResponse(null, { status: 401 });
      }
    }),
    http.post("/api/v1/users/authenticate/", async ({ request }) => {
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const data = (await request.json()) as {
        email: string;
        password: string;
      };
      const { email, password } = data;
      if (email === "<EMAIL>" && password === "correct-password") {
        serverConfiguration.loggedIn = true;
        return HttpResponse.json({
          user: USER,
          token: "token",
        });
      } else {
        return new HttpResponse(null, { status: 400 });
      }
    }),
    http.post("/api/v1/users/logout/", ({ request }) => {
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      serverConfiguration.loggedIn = false;
      return new HttpResponse(null);
    }),
    http.post("/api/v1/users/forgot-password/", ({ request }) => {
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return new HttpResponse(null);
    }),
    http.patch(
      "/api/v1/users/reset-password/correct-uuid/correct-token/",
      async ({ request }) => {
        if (
          serverConfiguration.forceError &&
          request.url.includes(serverConfiguration.forceError)
        ) {
          return new HttpResponse(null, { status: 500 });
        }

        const data = (await request.json()) as {
          password1: string;
          password2: string;
        };
        const { password1, password2 } = data;
        if (password1 === password2) {
          return new HttpResponse(null);
        } else {
          return new HttpResponse(null, { status: 400 });
        }
      }
    ),
    http.patch(
      "/api/v1/users/reset-password/wrong-uuid/wrong-token/",
      async () => {
        return new HttpResponse(null, { status: 400 });
      }
    ),
    http.delete("/api/v1/users/remove-avatar/", ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      USER.avatar = null;
      return new HttpResponse(null);
    }),
    http.patch("/api/v1/users/update-avatar/", ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      USER.avatar = "https://github.com/philip.png";
      return new HttpResponse(null);
    }),
    http.patch("/api/v1/users/update-data/", async ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const data = (await request.json()) as {
        first_name: string;
        last_name: string;
      };
      const { first_name, last_name } = data;
      USER.first_name = first_name;
      USER.last_name = last_name;
      return new HttpResponse(null);
    }),
    http.post("/api/v1/users/change-password/", ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return new HttpResponse(null);
    }),
  ];
};

export default getUserHandlers;

import { http, HttpResponse } from 'msw';

const getMongoHandlers = (serverConfiguration: TestServerConfiguration) => {
  return [
    http.get('/api/v1/mongo/flow-data/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json([
        {
          data: {
            'node-uuid-1.composite_multiple_levels': {
              activity: 'Make homemade ice cream',
              type: 'cooking',
              participants: 1,
              price: 0.2,
              link: '',
              key: '3818400',
              accessibility: 0.2,
            },
            'node-uuid-1.nested1': {
              activity: 'Start a collection',
              type: 'recreational',
              participants: 1,
              price: 0,
              link: '',
              key: '1718657',
              accessibility: 0.5,
            },
            'node-uuid-1.nested2': {
              composite_single_level: {
                activity: 'Teach your dog a new trick',
                type: 'relaxation',
                participants: 1,
                price: 0.05,
                link: '',
                key: '1668223',
                accessibility: 0.15,
              },
              nested1: {
                activity: 'Create and follow a savings plan',
                type: 'busywork',
                participants: 1,
                price: 0,
                link: '',
                key: '9366464',
                accessibility: 0.2,
              },
              nested2: {
                activity: 'Write a short story',
                type: 'recreational',
                participants: 1,
                price: 0,
                link: '',
                key: '6301585',
                accessibility: 0.1,
              },
            },
          },
          data_type: 'serializable_data',
          python_type: 'dict',
        },
        {
          data: {
            'node-uuid-2.composite_multiple_levels': {
              activity: 'Learn to sew on a button',
              type: 'education',
              participants: 1,
              price: 0.05,
              link: '',
              key: '8731971',
              accessibility: 0.1,
            },
            'node-uuid-2.nested1': {
              activity: 'Mow your lawn',
              type: 'busywork',
              participants: 1,
              price: 0.1,
              link: '',
              key: '3590127',
              accessibility: 0.3,
            },
            'node-uuid-2.nested2': {
              composite_single_level: {
                activity: "Try a food you don't like",
                type: 'recreational',
                participants: 1,
                price: 0.1,
                link: '',
                key: '6693574',
                accessibility: 0.05,
              },
              nested1: {
                activity: 'Prepare a 72-hour kit',
                type: 'busywork',
                participants: 1,
                price: 0.5,
                link: 'https://www.ready.gov/kit',
                key: '4266522',
                accessibility: 0.5,
              },
              nested2: {
                activity: 'Learn the Chinese erhu',
                type: 'music',
                participants: 1,
                price: 0.6,
                link: '',
                key: '2742452',
                accessibility: 0.4,
              },
            },
          },
          data_type: 'serializable_data',
          python_type: 'dict',
        },
      ]);
    }),
  ];
};

export default getMongoHandlers;

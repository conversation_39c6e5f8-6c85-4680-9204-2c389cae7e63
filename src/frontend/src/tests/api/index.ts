import getAnalyticsHandlers from './analytics';
import getCoreHandlers from './core';
import getFlowHandlers from './flows';
import getMongoHandlers from './mongo';
import getUserHandlers from './users';
// import getWebhookHandlers from './webhooks';

const getHandlers = (serverConfiguration: TestServerConfiguration) => {
  return [
    ...getAnalyticsHandlers(serverConfiguration),
    ...getCoreHandlers(serverConfiguration),
    ...getFlowHandlers(serverConfiguration),
    ...getMongoHandlers(serverConfiguration),
    ...getUserHandlers(serverConfiguration),
    // ...getWebhookHandlers(serverConfiguration),
  ];
};

export default getHandlers;

// import { http, HttpResponse } from 'msw';

// const WEBHOOKS = [
//   {
//     uuid: 'webhook-uuid-1',
//     active: true,
//     number_of_requests: 1,
//     service_name: 'Service 1',
//     url: 'https://something.com',
//   },
//   {
//     uuid: 'webhook-uuid-2',
//     active: false,
//     number_of_requests: 2,
//     service_name: 'Service 2',
//     url: 'https://something.com',
//   },
// ];

// const getWebhookHandlers = (serverConfiguration: TestServerConfiguration) => {
//   return [
//     http.get('/api/v1/organizations/organization-uuid-1/webhooks/', ({ request }) => {
//       if (!serverConfiguration.loggedIn) {
//         return new HttpResponse(null, { status: 400 });
//       }
//       if (
//         serverConfiguration.forceError &&
//         request.url.includes(serverConfiguration.forceError)
//       ) {
//         return new HttpResponse(null, { status: 500 });
//       }

//       return HttpResponse.json(WEBHOOKS);
//     }),
//     http.post('/api/v1/webhooks/:uuid/activate/', ({ request, params }) => {
//       if (!serverConfiguration.loggedIn) {
//         return new HttpResponse(null, { status: 400 });
//       }
//       if (
//         serverConfiguration.forceError &&
//         request.url.includes(serverConfiguration.forceError)
//       ) {
//         return new HttpResponse(null, { status: 500 });
//       }

//       const webhook = WEBHOOKS.filter((webhook) => {
//         return webhook.uuid === params.uuid;
//       })[0];
//       if (!webhook) {
//         return new HttpResponse(null, { status: 400 });
//       } else {
//         webhook.active = !webhook.active;
//         return new HttpResponse(null);
//       }
//     }),
//     http.get('/api/v1/webhooks/:uuid/', ({ request, params }) => {
//       if (!serverConfiguration.loggedIn) {
//         return new HttpResponse(null, { status: 400 });
//       }
//       if (
//         serverConfiguration.forceError &&
//         request.url.includes(serverConfiguration.forceError)
//       ) {
//         return new HttpResponse(null, { status: 500 });
//       }

//       const webhook = WEBHOOKS.filter((webhook) => {
//         return webhook.uuid === params.uuid;
//       })[0];
//       return HttpResponse.json(webhook);
//     }),
//   ];
// };

// export default getWebhookHandlers;

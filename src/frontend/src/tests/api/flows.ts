import { http, HttpResponse } from 'msw';

const getFlowHandlers = (serverConfiguration: TestServerConfiguration) => {
  return [
    http.get('/api/v1/flows/list/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      const url = new URL(request.url);
      const projectUuid = url.searchParams.get('project_uuids');

      if (projectUuid === 'project-uuid-1') {
        return HttpResponse.json([
          {
            uuid: 'flow-uuid-1',
            name: 'Flow 1',
          },
          {
            uuid: 'flow-uuid-2',
            name: 'Flow 2',
          },
        ]);
      } else if (projectUuid === 'project-uuid-2') {
        return HttpResponse.json([
          {
            uuid: 'flow-uuid-3',
            name: 'Flow 3',
          },
        ]);
      } else if (projectUuid === 'project-uuid-3') {
        return HttpResponse.json([
          {
            uuid: 'flow-uuid-4',
            name: 'Flow 4',
          },
        ]);
      }
    }),
    http.get('/api/v1/flows/retrieve/flow-uuid-1/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json({
        nodes: [
          {
            uuid: 'node-uuid-1',
            name: 'node1',
          },
          {
            uuid: 'node-uuid-2',
            name: 'node2',
          },
          {
            uuid: 'node-uuid-3',
            name: 'node3',
          },
          {
            uuid: 'node-uuid-4',
            name: 'node4',
          },
        ],
        relations: [
          {
            ancestor: 'node-uuid-1',
            descendant: 'node-uuid-2',
          },
          {
            ancestor: 'node-uuid-1',
            descendant: 'node-uuid-3',
          },
          {
            ancestor: 'node-uuid-2',
            descendant: 'node-uuid-4',
          },
        ],
      });
    }),
    http.get('/api/v1/flows/retrieve/flow-uuid-2/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json({
        nodes: [],
        relations: [],
      });
    }),
    http.get('/api/v1/flows/retrieve/flow-uuid-3/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json({
        nodes: [],
        relations: [],
      });
    }),
    http.get('/api/v1/flows/retrieve/flow-uuid-4/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json({
        nodes: [],
        relations: [],
      });
    }),
    http.get('/api/v1/flows/node/resources/', ({ request }) => {
      if (!serverConfiguration.loggedIn) {
        return new HttpResponse(null, { status: 400 });
      }
      if (
        serverConfiguration.forceError &&
        request.url.includes(serverConfiguration.forceError)
      ) {
        return new HttpResponse(null, { status: 500 });
      }

      return HttpResponse.json({
        node_type_resources: [],
        action_resources: [],
      });
    }),
  ];
};

export default getFlowHandlers;

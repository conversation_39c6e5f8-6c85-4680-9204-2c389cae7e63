import { afterEach } from 'vitest';
import { setupServer as mswSetupServer } from 'msw/node';

import { getHandlers } from '@tests';

const DEFAULT_SERVER_CONFIGURATION = {
  loggedIn: true,
  forceError: '',
};

const setupServer = () => {
  const serverConfiguration: TestServerConfiguration = {
    ...DEFAULT_SERVER_CONFIGURATION,
  };
  const handlers = getHandlers(serverConfiguration);
  const server = mswSetupServer(...handlers);

  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterEach(() => {
    localStorage.clear();

    // Only mutate the serverConfiguration object, do not reassign it!
    serverConfiguration.loggedIn = DEFAULT_SERVER_CONFIGURATION.loggedIn;
    serverConfiguration.forceError = DEFAULT_SERVER_CONFIGURATION.forceError;

    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  return serverConfiguration;
};

export default setupServer;

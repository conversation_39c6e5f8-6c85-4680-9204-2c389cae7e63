msgid ""
msgstr ""
"POT-Creation-Date: 2023-10-27 17:57+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/locales/messages.ts:23
#~ msgid "{0} has been disconnected"
#~ msgstr "{0} has been disconnected"

#: src/components/profile/AvatarUpload.tsx:33
msgid "+ Upload"
msgstr "+ Upload"

#: src/locales/messages.ts:11
#~ msgid "A new connection has been added"
#~ msgstr "A new connection has been added"

#: src/locales/messages.ts:14
msgid "A new connection has been created"
msgstr "A new connection has been created"

#: src/components/builder/NodeConfiguration.tsx:84
msgid "Action"
msgstr "Action"

#: src/components/builder/NodeConfiguration.tsx:124
#~ msgid "Add connection"
#~ msgstr "Add connection"

#: src/components/layout/HeaderSelects.tsx:80
#~ msgid "Add flow"
#~ msgstr "Add flow"

#: src/components/builder/BaseNode.tsx:61
msgid "Add nested node"
msgstr "Add nested node"

#: src/components/layout/HeaderSelects.tsx:70
msgid "All flows"
msgstr "All flows"

#: src/locales/messages.ts:143
msgid "An email with further instructions has been sent"
msgstr "An email with further instructions has been sent"

#: src/locales/messages.ts:14
#~ msgid "An error has occurred while adding new connection"
#~ msgstr "An error has occurred while adding new connection"

#: src/locales/messages.ts:65
msgid "An error has occurred while connecting nodes"
msgstr "An error has occurred while connecting nodes"

#: src/locales/messages.ts:38
msgid "An error has occurred while creating flow"
msgstr "An error has occurred while creating flow"

#: src/locales/messages.ts:59
msgid "An error has occurred while creating nested node"
msgstr "An error has occurred while creating nested node"

#: src/locales/messages.ts:17
msgid "An error has occurred while creating new connection"
msgstr "An error has occurred while creating new connection"

#: src/locales/messages.ts:56
msgid "An error has occurred while creating root node"
msgstr "An error has occurred while creating root node"

#: src/locales/messages.ts:23
msgid "An error has occurred while deleting connection {0}"
msgstr "An error has occurred while deleting connection {0}"

#: src/locales/messages.ts:68
msgid "An error has occurred while deleting edge"
msgstr "An error has occurred while deleting edge"

#: src/locales/messages.ts:44
msgid "An error has occurred while deleting flow"
msgstr "An error has occurred while deleting flow"

#: src/locales/messages.ts:62
msgid "An error has occurred while deleting node"
msgstr "An error has occurred while deleting node"

#: src/locales/messages.ts:26
#~ msgid "An error has occurred while disconnecting {0}"
#~ msgstr "An error has occurred while disconnecting {0}"

#: src/locales/messages.ts:44
#~ msgid "An error has occurred while executing flow"
#~ msgstr "An error has occurred while executing flow"

#: src/locales/messages.ts:47
msgid "An error has occurred while executing flow: {errorText}"
msgstr "An error has occurred while executing flow: {errorText}"

#: src/locales/messages.ts:5
msgid "An error has occurred while fetching analytics"
msgstr "An error has occurred while fetching analytics"

#: src/locales/messages.ts:8
msgid "An error has occurred while fetching application information"
msgstr "An error has occurred while fetching application information"

#: src/locales/messages.ts:11
msgid "An error has occurred while fetching connections"
msgstr "An error has occurred while fetching connections"

#: src/locales/messages.ts:74
msgid "An error has occurred while fetching flow call"
msgstr "An error has occurred while fetching flow call"

#: src/locales/messages.ts:71
msgid "An error has occurred while fetching flow calls"
msgstr "An error has occurred while fetching flow calls"

#: src/locales/messages.ts:53
msgid "An error has occurred while fetching flow schema"
msgstr "An error has occurred while fetching flow schema"

#: src/locales/messages.ts:50
msgid "An error has occurred while fetching flows"
msgstr "An error has occurred while fetching flows"

#: src/locales/messages.ts:80
msgid "An error has occurred while fetching node action"
msgstr "An error has occurred while fetching node action"

#: src/locales/messages.ts:77
msgid "An error has occurred while fetching node call detail"
msgstr "An error has occurred while fetching node call detail"

#: src/locales/messages.ts:86
msgid "An error has occurred while fetching node connection"
msgstr "An error has occurred while fetching node connection"

#: src/locales/messages.ts:95
msgid "An error has occurred while fetching node resources"
msgstr "An error has occurred while fetching node resources"

#: src/locales/messages.ts:44
#~ msgid "An error has occurred while fetching options"
#~ msgstr "An error has occurred while fetching options"

#: src/locales/messages.ts:98
msgid "An error has occurred while fetching organizations"
msgstr "An error has occurred while fetching organizations"

#: src/locales/messages.ts:113
msgid "An error has occurred while fetching projects"
msgstr "An error has occurred while fetching projects"

#: src/locales/messages.ts:116
msgid "An error has occurred while fetching user data"
msgstr "An error has occurred while fetching user data"

#: src/locales/messages.ts:149
#~ msgid "An error has occurred while fetching webhooks"
#~ msgstr "An error has occurred while fetching webhooks"

#: src/locales/messages.ts:104
msgid "An error has occurred while leaving {0}"
msgstr "An error has occurred while leaving {0}"

#: src/locales/messages.ts:122
msgid "An error has occurred while logging out"
msgstr "An error has occurred while logging out"

#: src/locales/messages.ts:134
msgid "An error has occurred while removing profile image"
msgstr "An error has occurred while removing profile image"

#: src/hooks/request.ts:95
#~ msgid "An error has occurred while requesting {url}"
#~ msgstr "An error has occurred while requesting {url}"

#: src/locales/messages.ts:56
#~ msgid "An error has occurred while resetting organizations"
#~ msgstr "An error has occurred while resetting organizations"

#: src/locales/messages.ts:140
msgid "An error has occurred while saving data"
msgstr "An error has occurred while saving data"

#: src/locales/messages.ts:152
msgid "An error has occurred while setting new password"
msgstr "An error has occurred while setting new password"

#: src/locales/messages.ts:83
msgid "An error has occurred while setting node action"
msgstr "An error has occurred while setting node action"

#: src/locales/messages.ts:89
msgid "An error has occurred while setting node connection"
msgstr "An error has occurred while setting node connection"

#: src/locales/messages.ts:92
msgid "An error has occurred while setting node type"
msgstr "An error has occurred while setting node type"

#: src/locales/messages.ts:146
msgid "An error has occurred while trying to reset password"
msgstr "An error has occurred while trying to reset password"

#: src/locales/messages.ts:41
msgid "An error has occurred while updating flow"
msgstr "An error has occurred while updating flow"

#: src/locales/messages.ts:128
msgid "An error has occurred while uploading profile image"
msgstr "An error has occurred while uploading profile image"

#: src/components/layout/PageLayout.tsx:97
#~ msgid "Analytics"
#~ msgstr "Analytics"

#: src/components/connections/ConnectionItem.tsx:46
msgid "Are you sure you want to delete this connection?"
msgstr "Are you sure you want to delete this connection?"

#: src/components/flows/FlowConfiguration.tsx:90
msgid "Are you sure you want to delete this flow?"
msgstr "Are you sure you want to delete this flow?"

#: src/components/profile/OrganizationList.tsx:31
msgid "Are you sure you want to leave {0}?"
msgstr "Are you sure you want to leave {0}?"

#: src/components/dashboard/DashboardConfiguration.tsx:40
msgid "Auto refresh table:"
msgstr "Auto refresh table:"

#: src/components/builder/NodeConfiguration.tsx:131
#~ msgid "Configure node action"
#~ msgstr "Configure node action"

#: src/components/builder/NodeConfiguration.tsx:86
#~ msgid "Configure node connection"
#~ msgstr "Configure node connection"

#: src/components/builder/BaseNode.tsx:90
msgid "Configure node type"
msgstr "Configure node type"

#: src/components/users/ResetPasswordForm.tsx:56
msgid "Confirm new password"
msgstr "Confirm new password"

#: src/components/builder/NodeConfiguration.tsx:91
#~ msgid "Connection"
#~ msgstr "Connection"

#: src/locales/messages.ts:20
msgid "Connection {0} has been deleted"
msgstr "Connection {0} has been deleted"

#: src/components/fields/AddConnectionForm.tsx:25
#~ msgid "Connection has no fields"
#~ msgstr "Connection has no fields"

#: src/components/layout/PageLayout.tsx:90
#: src/pages/ConnectionsPage.tsx:19
msgid "Connections"
msgstr "Connections"

#: src/components/builder/NodeConfiguration.tsx:112
#: src/components/fields/CreateConnectionForm.tsx:83
#~ msgid "Create connection"
#~ msgstr "Create connection"

#: src/components/layout/HeaderSelects.tsx:84
msgid "Create flow"
msgstr "Create flow"

#: src/components/builder/CreateNode.tsx:17
msgid "Create root node"
msgstr "Create root node"

#: src/components/connections/ConnectionItem.tsx:37
msgid "Created on"
msgstr "Created on"

#: src/pages/AnalyticsPage.tsx:64
msgid "Daily"
msgstr "Daily"

#: src/components/layout/PageLayout.tsx:70
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/builder/BaseNode.tsx:111
#: src/components/connections/ConnectionItem.tsx:54
msgid "Delete"
msgstr "Delete"

#: src/components/builder/DeletableEdge.tsx:57
msgid "Delete edge"
msgstr "Delete edge"

#: src/components/flows/FlowConfiguration.tsx:96
msgid "Delete flow"
msgstr "Delete flow"

#: src/components/webhooks/WebhookDetail.tsx:17
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/connections/ConnectionItem.tsx:52
#~ msgid "Disconnect"
#~ msgstr "Disconnect"

#: src/components/flows/NodeCallDetail.tsx:72
#: src/components/flows/NodeCallDetail.tsx:98
msgid "Download"
msgstr "Download"

#: src/components/flows/NodeCallDetail.tsx:64
msgid "Download full input"
msgstr "Download full input"

#: src/components/flows/NodeCallDetail.tsx:90
msgid "Download full output"
msgstr "Download full output"

#: src/components/flows/FlowConfiguration.tsx:66
msgid "Edit flow name"
msgstr "Edit flow name"

#: src/components/profile/AvatarUpload.tsx:42
msgid "Edit image"
msgstr "Edit image"

#: src/components/profile/UserData.tsx:82
msgid "Email"
msgstr "Email"

#: src/components/flows/FlowConfiguration.tsx:86
msgid "Enabled"
msgstr "Enabled"

#: src/components/dashboard/FlowCallsTable.tsx:85
#: src/components/flows/NodeCallsTable.tsx:58
msgid "End"
msgstr "End"

#: src/components/flows/NodeCallDetail.tsx:50
msgid "Errors:"
msgstr "Errors:"

#: src/components/profile/ExportData.tsx:17
msgid "Export"
msgstr "Export"

#: src/components/profile/ExportData.tsx:9
#: src/pages/ProfilePage.tsx:46
msgid "Export data"
msgstr "Export data"

#: src/hooks/flows.tsx:36
msgid "Failed"
msgstr "Failed"

#: src/components/profile/UserData.tsx:87
msgid "First name"
msgstr "First name"

#: src/pages/AnalyticsPage.tsx:51
msgid "Flow"
msgstr "Flow"

#: src/components/layout/PageLayout.tsx:80
msgid "Flow Builder"
msgstr "Flow Builder"

#: src/components/flows/FlowConfiguration.tsx:27
msgid "Flow name cannot be empty"
msgstr "Flow name cannot be empty"

#: src/components/webhooks/WebhookDetail.tsx:55
#~ msgid "Flows using this webhook"
#~ msgstr "Flows using this webhook"

#: src/components/users/LoginForm.tsx:70
msgid "Forgot password?"
msgstr "Forgot password?"

#: src/pages/AnalyticsPage.tsx:75
msgid "frequency"
msgstr "frequency"

#: src/pages/NotFoundPage.tsx:38
msgid "Go to Dashboard"
msgstr "Go to Dashboard"

#: src/components/users/ForgotPasswordForm.tsx:53
msgid "Go to login"
msgstr "Go to login"

#: src/components/other/ShowHideButton.tsx:21
msgid "Hide"
msgstr "Hide"

#: src/components/flows/SchemaHider.tsx:22
msgid "Hide flow schema"
msgstr "Hide flow schema"

#: src/components/dashboard/FlowCallsTable.tsx:59
#: src/components/flows/NodeCallsTable.tsx:32
msgid "ID"
msgstr "ID"

#: src/hooks/flows.tsx:38
msgid "In progress"
msgstr "In progress"

#: src/hooks/flows.tsx:39
msgid "In queue"
msgstr "In queue"

#: src/pages/AnalyticsPage.tsx:75
msgid "in time range"
msgstr "in time range"

#: src/components/flows/NodeCallDetail.tsx:63
msgid "Input"
msgstr "Input"

#: src/components/flows/FlowConfiguration.tsx:83
#~ msgid "Interval"
#~ msgstr "Interval"

#: src/components/profile/UserData.tsx:97
msgid "Last name"
msgstr "Last name"

#: src/components/profile/OrganizationList.tsx:37
msgid "Leave"
msgstr "Leave"

#: src/components/users/LoginForm.tsx:65
#: src/pages/NotFoundPage.tsx:42
msgid "Log in"
msgstr "Log in"

#: src/components/layout/UserDropdown.tsx:56
msgid "Log out"
msgstr "Log out"

#: src/components/webhooks/WebhookDetail.tsx:72
#~ msgid "Logs"
#~ msgstr "Logs"

#: src/components/layout/UserDropdown.tsx:46
msgid "Manage profile"
msgstr "Manage profile"

#: src/components/flows/FlowConfiguration.tsx:83
#~ msgid "minutes"
#~ msgstr "minutes"

#: src/pages/AnalyticsPage.tsx:66
msgid "Monthly"
msgstr "Monthly"

#: src/components/dashboard/FlowCallsTable.tsx:65
#: src/components/flows/NodeCallsTable.tsx:38
msgid "Name"
msgstr "Name"

#: src/components/users/ResetPasswordForm.tsx:44
msgid "New password"
msgstr "New password"

#: src/locales/messages.ts:149
msgid "New password has been saved"
msgstr "New password has been saved"

#: src/components/connections/ConnectionItem.tsx:49
#: src/components/flows/FlowConfiguration.tsx:93
msgid "No"
msgstr "No"

#: src/pages/ConnectionsPage.tsx:30
msgid "No connections are currently available"
msgstr "No connections are currently available"

#: src/pages/WebhooksPage.tsx:24
#~ msgid "No webhooks are currently available"
#~ msgstr "No webhooks are currently available"

#: src/components/builder/NodeConfiguration.tsx:73
msgid "Node type"
msgstr "Node type"

#: src/components/flows/NodeCallsTable.tsx:37
#~ msgid "Node Type"
#~ msgstr "Node Type"

#: src/components/webhooks/WebhookItem.tsx:25
#~ msgid "Number of incomming requests"
#~ msgstr "Number of incomming requests"

#: src/pages/AnalyticsPage.tsx:94
msgid "Number of node calls"
msgstr "Number of node calls"

#: src/components/connections/ConnectionItem.tsx:41
msgid "Number of nodes using this connection"
msgstr "Number of nodes using this connection"

#: src/components/connections/ConnectionItem.tsx:32
#~ msgid "Number of permissions"
#~ msgstr "Number of permissions"

#: src/components/dashboard/DashboardConfiguration.tsx:44
msgid "Off"
msgstr "Off"

#: src/components/dashboard/DashboardConfiguration.tsx:45
msgid "On"
msgstr "On"

#: src/pages/AnalyticsPage.tsx:49
msgid "Organization"
msgstr "Organization"

#: src/components/profile/OrganizationList.tsx:18
#: src/pages/ProfilePage.tsx:37
msgid "Organizations"
msgstr "Organizations"

#: src/locales/messages.ts:53
#~ msgid "Organizations have been reset"
#~ msgstr "Organizations have been reset"

#: src/components/flows/NodeCallDetail.tsx:89
msgid "Output"
msgstr "Output"

#: src/components/users/LoginForm.tsx:51
msgid "Password"
msgstr "Password"

#: src/components/profile/Security.tsx:20
msgid "Password reset"
msgstr "Password reset"

#: src/hooks/user.ts:240
msgid "Passwords don't match"
msgstr "Passwords don't match"

#: src/pages/ProfilePage.tsx:19
msgid "Personal information"
msgstr "Personal information"

#: src/components/profile/UserData.tsx:63
msgid "Personal Information"
msgstr "Personal Information"

#: src/components/profile/UserData.tsx:81
#~ msgid "Phone"
#~ msgstr "Phone"

#: src/components/users/ForgotPasswordForm.tsx:49
#: src/components/users/ResetPasswordForm.tsx:68
msgid "Proceed"
msgstr "Proceed"

#: src/components/layout/PageLayout.tsx:120
msgid "Profile"
msgstr "Profile"

#: src/components/profile/UserData.tsx:56
msgid "Profile image"
msgstr "Profile image"

#: src/locales/messages.ts:131
msgid "Profile image has been removed"
msgstr "Profile image has been removed"

#: src/locales/messages.ts:125
msgid "Profile image has been uploaded"
msgstr "Profile image has been uploaded"

#: src/pages/AnalyticsPage.tsx:50
msgid "Project"
msgstr "Project"

#: src/components/profile/OrganizationList.tsx:46
#~ msgid "Reset organizations"
#~ msgstr "Reset organizations"

#: src/components/profile/Security.tsx:37
#: src/components/users/ForgotPasswordForm.tsx:27
msgid "Reset password"
msgstr "Reset password"

#: src/hooks/flows.tsx:37
msgid "Retrying"
msgstr "Retrying"

msgid "Skipped"
msgstr "Skipped"

#: src/hooks/flows.tsx:41
msgid "Timeout"
msgstr "Timeout"

#: src/components/profile/UserData.tsx:78
#~ msgid "Role"
#~ msgstr "Role"

#: src/components/flows/FlowExecutionButton.tsx:28
msgid "Run once"
msgstr "Run once"

#: src/components/profile/UserData.tsx:75
msgid "Save"
msgstr "Save"

#: src/pages/ProfilePage.tsx:28
msgid "Security"
msgstr "Security"

#: src/pages/FlowBuilderPage.tsx:17
msgid "Select a flow to get started"
msgstr "Select a flow to get started"

#: src/components/webhooks/WebhookDetail.tsx:36
#~ msgid "Service"
#~ msgstr "Service"

#: src/components/users/ResetPasswordForm.tsx:35
msgid "Set new password"
msgstr "Set new password"

#: src/components/other/ShowHideButton.tsx:20
msgid "Show"
msgstr "Show"

#: src/pages/AnalyticsPage.tsx:45
msgid "Show analytics for current"
msgstr "Show analytics for current"

#: src/components/dashboard/FlowCallsTable.tsx:78
#: src/components/flows/NodeCallsTable.tsx:51
msgid "Start"
msgstr "Start"

#: src/components/layout/PageLayout.tsx:110
msgid "Statistics"
msgstr "Statistics"

#: src/components/dashboard/FlowCallsTable.tsx:71
#: src/components/flows/NodeCallsTable.tsx:44
msgid "Status"
msgstr "Status"

#: src/hooks/flows.tsx:35
msgid "Success"
msgstr "Success"

#: src/locales/messages.ts:137
msgid "The changes have been saved"
msgstr "The changes have been saved"

#: src/locales/messages.ts:26
#~ msgid "The connection {0} returned an error: {1}"
#~ msgstr "The connection {0} returned an error: {1}"

#: src/locales/messages.ts:20
#~ msgid "The connection {0} returned an error: {errorText}"
#~ msgstr "The connection {0} returned an error: {errorText}"

#: src/locales/messages.ts:17
#~ msgid "The connection {0} works as expected"
#~ msgstr "The connection {0} works as expected"

#: src/components/profile/UserData.tsx:49
msgid "The form contains errors, please fix them before submitting"
msgstr "The form contains errors, please fix them before submitting"

#: src/pages/AnalyticsPage.tsx:106
msgid "There are no node calls"
msgstr "There are no node calls"

#: src/components/profile/UserData.tsx:90
#: src/components/profile/UserData.tsx:100
msgid "This field is required"
msgstr "This field is required"

#: src/pages/NotFoundPage.tsx:23
msgid "This page does not exist"
msgstr "This page does not exist"

#: src/components/flows/NodeCallsTable.tsx:50
#~ msgid "Timestamp"
#~ msgstr "Timestamp"

#: src/components/profile/ExportData.tsx:14
msgid "To export your data, click the button below."
msgstr "To export your data, click the button below."

#: src/components/profile/Security.tsx:25
msgid "To reset your password, click the button below and wait for an email with further instructions."
msgstr "To reset your password, click the button below and wait for an email with further instructions."

#: src/components/profile/UserData.tsx:89
#: src/components/profile/UserData.tsx:99
msgid "Value must be up to 40 characters"
msgstr "Value must be up to 40 characters"

#: src/components/connections/ConnectionItem.tsx:42
#~ msgid "Verify"
#~ msgstr "Verify"

#: src/components/dashboard/FlowCallsTable.tsx:46
msgid "View detail"
msgstr "View detail"

#: src/components/flows/SchemaHider.tsx:21
msgid "View flow schema"
msgstr "View flow schema"

#: src/components/webhooks/WebhookDetail.tsx:24
#~ msgid "Webhook status"
#~ msgstr "Webhook status"

#: src/components/webhooks/WebhookDetail.tsx:42
#~ msgid "Webhook URL"
#~ msgstr "Webhook URL"

#: src/components/webhooks/WebhookDetail.tsx:50
#~ msgid "Webhook UUID"
#~ msgstr "Webhook UUID"

#: src/pages/WebhooksPage.tsx:18
#~ msgid "Webhooks"
#~ msgstr "Webhooks"

#: src/pages/AnalyticsPage.tsx:65
msgid "Weekly"
msgstr "Weekly"

#: src/pages/AnalyticsPage.tsx:60
msgid "with"
msgstr "with"

#: src/components/connections/ConnectionItem.tsx:48
#: src/components/flows/FlowConfiguration.tsx:92
msgid "Yes"
msgstr "Yes"

#: src/locales/messages.ts:101
msgid "You have left {0}"
msgstr "You have left {0}"

#: src/locales/messages.ts:119
msgid "You must have entered wrong email or password"
msgstr "You must have entered wrong email or password"

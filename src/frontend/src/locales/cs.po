msgid ""
msgstr ""
"POT-Creation-Date: 2023-10-27 17:57+0200\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: cs\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/locales/messages.ts:23
#~ msgid "{0} has been disconnected"
#~ msgstr "Připojení {0} bylo odpojeno"

#: src/components/profile/AvatarUpload.tsx:33
msgid "+ Upload"
msgstr "+ Nahrát"

#: src/locales/messages.ts:11
#~ msgid "A new connection has been added"
#~ msgstr "Nové připojení bylo p<PERSON>idán<PERSON>"

#: src/locales/messages.ts:14
msgid "A new connection has been created"
msgstr "Připojení bylo vytvořené"

#: src/components/builder/NodeConfiguration.tsx:84
msgid "Action"
msgstr "Akce"

#: src/components/builder/NodeConfiguration.tsx:124
#~ msgid "Add connection"
#~ msgstr "Přidat připojení"

#: src/components/layout/HeaderSelects.tsx:80
#~ msgid "Add flow"
#~ msgstr "Přidat flow"

#: src/components/builder/BaseNode.tsx:61
msgid "Add nested node"
msgstr "Přidat navazující node"

#: src/components/layout/HeaderSelects.tsx:70
msgid "All flows"
msgstr "Všechny flows"

#: src/locales/messages.ts:143
msgid "An email with further instructions has been sent"
msgstr "Email s dalšími instrukcemi byl odeslán"

#: src/locales/messages.ts:14
#~ msgid "An error has occurred while adding new connection"
#~ msgstr "Došlo k chybě při přidávání nového připojení"

#: src/locales/messages.ts:65
msgid "An error has occurred while connecting nodes"
msgstr "Došlo k chybě při propojování nodů"

#: src/locales/messages.ts:38
msgid "An error has occurred while creating flow"
msgstr "Došlo k chybě při vytváření flow"

#: src/locales/messages.ts:59
msgid "An error has occurred while creating nested node"
msgstr "Došlo k chybě při vytváření navazujícího nodu"

#: src/locales/messages.ts:17
msgid "An error has occurred while creating new connection"
msgstr "Došlo k chybě při vytváření připojení"

#: src/locales/messages.ts:56
msgid "An error has occurred while creating root node"
msgstr "Došlo k chybě při vytváření kořenového nodu"

#: src/locales/messages.ts:23
msgid "An error has occurred while deleting connection {0}"
msgstr "Došlo k chybě při odstraňování připojení {0}"

#: src/locales/messages.ts:68
msgid "An error has occurred while deleting edge"
msgstr "Došlo k chybě při odstraňování hrany"

#: src/locales/messages.ts:44
msgid "An error has occurred while deleting flow"
msgstr "Došlo k chybě při odstraňování flow"

#: src/locales/messages.ts:62
msgid "An error has occurred while deleting node"
msgstr "Došlo k chybě při odstraňování nodu"

#: src/locales/messages.ts:26
#~ msgid "An error has occurred while disconnecting {0}"
#~ msgstr "Došlo k chybě při odpojování {0}"

#: src/locales/messages.ts:44
#~ msgid "An error has occurred while executing flow"
#~ msgstr "Došlo k chybě při spuštění flow"

#: src/locales/messages.ts:47
msgid "An error has occurred while executing flow: {errorText}"
msgstr "Došlo k chybě při spuštění flow: {errorText}"

#: src/locales/messages.ts:5
msgid "An error has occurred while fetching analytics"
msgstr "Došlo k chybě při načítání statistik"

#: src/locales/messages.ts:8
msgid "An error has occurred while fetching application information"
msgstr "Došlo k chybě při načítání informací o aplikaci"

#: src/locales/messages.ts:11
msgid "An error has occurred while fetching connections"
msgstr "Došlo k chybě při načítání připojení"

#: src/locales/messages.ts:74
msgid "An error has occurred while fetching flow call"
msgstr "Došlo k chybě při načítání flow"

#: src/locales/messages.ts:71
msgid "An error has occurred while fetching flow calls"
msgstr "Došlo k chybě při načítání flow callů"

#: src/locales/messages.ts:53
msgid "An error has occurred while fetching flow schema"
msgstr "Došlo k chybě při načítání flow schématu"

#: src/locales/messages.ts:50
msgid "An error has occurred while fetching flows"
msgstr "Došlo k chybě při načítání flows"

#: src/locales/messages.ts:80
msgid "An error has occurred while fetching node action"
msgstr "Došlo k chybě při načítání informací"

#: src/locales/messages.ts:77
msgid "An error has occurred while fetching node call detail"
msgstr "Došlo k chybě při načítání informací"

#: src/locales/messages.ts:86
msgid "An error has occurred while fetching node connection"
msgstr "Došlo k chybě při načítání informací"

#: src/locales/messages.ts:95
msgid "An error has occurred while fetching node resources"
msgstr "Došlo k chybě při načítání informací"

#: src/locales/messages.ts:44
#~ msgid "An error has occurred while fetching options"
#~ msgstr "Došlo k chybě při načítání možností"

#: src/locales/messages.ts:98
msgid "An error has occurred while fetching organizations"
msgstr "Došlo k chybě při načítání organizací"

#: src/locales/messages.ts:113
msgid "An error has occurred while fetching projects"
msgstr "Došlo k chybě při načítání projektů"

#: src/locales/messages.ts:116
msgid "An error has occurred while fetching user data"
msgstr "Došlo k chybě při načítání uživatelských dat"

#: src/locales/messages.ts:149
#~ msgid "An error has occurred while fetching webhooks"
#~ msgstr "Došlo k chybě při načítání webhooků"

#: src/locales/messages.ts:104
msgid "An error has occurred while leaving {0}"
msgstr "Došlo k chybě při odcházení z {0}"

#: src/locales/messages.ts:122
msgid "An error has occurred while logging out"
msgstr "Došlo k chybě při odhlašování"

#: src/locales/messages.ts:134
msgid "An error has occurred while removing profile image"
msgstr "Došlo k chybě při odstraňování profilového obrázku"

#: src/hooks/request.ts:95
#~ msgid "An error has occurred while requesting {url}"
#~ msgstr "Došlo k chybě při dotazování {url}"

#: src/locales/messages.ts:56
#~ msgid "An error has occurred while resetting organizations"
#~ msgstr "Došlo k chybě při resetování organizací"

#: src/locales/messages.ts:140
msgid "An error has occurred while saving data"
msgstr "Došlo k chybě při ukládání dat"

#: src/locales/messages.ts:152
msgid "An error has occurred while setting new password"
msgstr "Došlo k chybě při nastavování nového hesla"

#: src/locales/messages.ts:83
msgid "An error has occurred while setting node action"
msgstr "Došlo k chybě při ukládání informací"

#: src/locales/messages.ts:89
msgid "An error has occurred while setting node connection"
msgstr "Došlo k chybě při ukládání informací"

#: src/locales/messages.ts:92
msgid "An error has occurred while setting node type"
msgstr "Došlo k chybě při ukládání informací"

#: src/locales/messages.ts:146
msgid "An error has occurred while trying to reset password"
msgstr "Došlo k chybě při pokusu o resetování hesla"

#: src/locales/messages.ts:41
msgid "An error has occurred while updating flow"
msgstr "Došlo k chybě při upravování flow"

#: src/locales/messages.ts:128
msgid "An error has occurred while uploading profile image"
msgstr "Došlo k chybě při nahrávání profilového obrázku"

#: src/components/layout/PageLayout.tsx:97
#~ msgid "Analytics"
#~ msgstr "Analytiky"

#: src/components/connections/ConnectionItem.tsx:46
msgid "Are you sure you want to delete this connection?"
msgstr "Opravdu chcete odstranit toto připojení?"

#: src/components/flows/FlowConfiguration.tsx:90
msgid "Are you sure you want to delete this flow?"
msgstr "Opravdu chcete odstranit toto flow?"

#: src/components/profile/OrganizationList.tsx:31
msgid "Are you sure you want to leave {0}?"
msgstr "Opravdu chcete opustit {0}?"

#: src/components/dashboard/DashboardConfiguration.tsx:40
msgid "Auto refresh table:"
msgstr "Automatická aktualizace tabulky:"

#: src/components/builder/NodeConfiguration.tsx:131
#~ msgid "Configure node action"
#~ msgstr "Konfigurovat akci"

#: src/components/builder/NodeConfiguration.tsx:86
#~ msgid "Configure node connection"
#~ msgstr "Konfigurovat připojení"

#: src/components/builder/BaseNode.tsx:90
msgid "Configure node type"
msgstr "Konfigurovat typ"

#: src/components/users/ResetPasswordForm.tsx:56
msgid "Confirm new password"
msgstr "Potvrdit nové heslo"

#: src/components/builder/NodeConfiguration.tsx:91
#~ msgid "Connection"
#~ msgstr "Připojení"

#: src/locales/messages.ts:20
msgid "Connection {0} has been deleted"
msgstr "Připojení {0} bylo odstraněno"

#: src/components/fields/AddConnectionForm.tsx:25
#~ msgid "Connection has no fields"
#~ msgstr "Připojení nemá žádná pole"

#: src/components/layout/PageLayout.tsx:90
#: src/pages/ConnectionsPage.tsx:19
msgid "Connections"
msgstr "Připojení"

#: src/components/builder/NodeConfiguration.tsx:112
#: src/components/fields/CreateConnectionForm.tsx:83
#~ msgid "Create connection"
#~ msgstr "Vytvořit připojení"

#: src/components/layout/HeaderSelects.tsx:84
msgid "Create flow"
msgstr "Vytvořit flow"

#: src/components/builder/CreateNode.tsx:17
msgid "Create root node"
msgstr "Vytvořit kořenový node"

#: src/components/connections/ConnectionItem.tsx:37
msgid "Created on"
msgstr "Vytvořeno"

#: src/pages/AnalyticsPage.tsx:64
msgid "Daily"
msgstr "Denní"

#: src/components/layout/PageLayout.tsx:70
msgid "Dashboard"
msgstr "Dashboard"

#: src/components/builder/BaseNode.tsx:111
#: src/components/connections/ConnectionItem.tsx:54
msgid "Delete"
msgstr "Smazat"

#: src/components/builder/DeletableEdge.tsx:57
msgid "Delete edge"
msgstr "Odstranit hranu"

#: src/components/flows/FlowConfiguration.tsx:96
msgid "Delete flow"
msgstr "Odstranit flow"

#: src/components/webhooks/WebhookDetail.tsx:17
#~ msgid "Detail"
#~ msgstr "Detail"

#: src/components/connections/ConnectionItem.tsx:52
#~ msgid "Disconnect"
#~ msgstr "Odpojit"

#: src/components/flows/NodeCallDetail.tsx:72
#: src/components/flows/NodeCallDetail.tsx:98
msgid "Download"
msgstr "Stáhnout"

#: src/components/flows/NodeCallDetail.tsx:64
msgid "Download full input"
msgstr "Stáhnout celý vstup"

#: src/components/flows/NodeCallDetail.tsx:90
msgid "Download full output"
msgstr "Stáhnout celý výstup"

#: src/components/flows/FlowConfiguration.tsx:66
msgid "Edit flow name"
msgstr "Upravit název"

#: src/components/profile/AvatarUpload.tsx:42
msgid "Edit image"
msgstr "Upravit obrázek"

#: src/components/profile/UserData.tsx:82
msgid "Email"
msgstr "Email"

#: src/components/flows/FlowConfiguration.tsx:86
msgid "Enabled"
msgstr "Spuštěno"

#: src/components/dashboard/FlowCallsTable.tsx:85
#: src/components/flows/NodeCallsTable.tsx:58
msgid "End"
msgstr "Konec"

#: src/components/flows/NodeCallDetail.tsx:50
msgid "Errors:"
msgstr "Chyby:"

#: src/components/profile/ExportData.tsx:17
msgid "Export"
msgstr "Exportovat"

#: src/components/profile/ExportData.tsx:9
#: src/pages/ProfilePage.tsx:46
msgid "Export data"
msgstr "Exportovat data"

#: src/hooks/flows.tsx:36
msgid "Failed"
msgstr "Neúspěch"

#: src/components/profile/UserData.tsx:87
msgid "First name"
msgstr "Jméno"

#: src/pages/AnalyticsPage.tsx:51
msgid "Flow"
msgstr "Flow"

#: src/components/layout/PageLayout.tsx:80
msgid "Flow Builder"
msgstr "Tvůrce flow"

#: src/components/flows/FlowConfiguration.tsx:27
msgid "Flow name cannot be empty"
msgstr "Název nemůže být prázdný"

#: src/components/webhooks/WebhookDetail.tsx:55
#~ msgid "Flows using this webhook"
#~ msgstr "Flows využívající tento webhook"

#: src/components/users/LoginForm.tsx:70
msgid "Forgot password?"
msgstr "Zapomenuté heslo?"

#: src/pages/AnalyticsPage.tsx:75
msgid "frequency"
msgstr "frekvencí"

#: src/pages/NotFoundPage.tsx:38
msgid "Go to Dashboard"
msgstr "Přejít na dashboard"

#: src/components/users/ForgotPasswordForm.tsx:53
msgid "Go to login"
msgstr "Přejít na přihlášení"

#: src/components/other/ShowHideButton.tsx:21
msgid "Hide"
msgstr "Schovat"

#: src/components/flows/SchemaHider.tsx:22
msgid "Hide flow schema"
msgstr "Schovat flow schéma"

#: src/components/dashboard/FlowCallsTable.tsx:59
#: src/components/flows/NodeCallsTable.tsx:32
msgid "ID"
msgstr "ID"

#: src/hooks/flows.tsx:38
msgid "In progress"
msgstr "Probíhá"

#: src/hooks/flows.tsx:39
msgid "In queue"
msgstr "Ve frontě"

#: src/pages/AnalyticsPage.tsx:75
msgid "in time range"
msgstr "v časovém rozmezí"

#: src/components/flows/NodeCallDetail.tsx:63
msgid "Input"
msgstr "Vstup"

#: src/components/flows/FlowConfiguration.tsx:83
#~ msgid "Interval"
#~ msgstr "Interval"

#: src/components/profile/UserData.tsx:97
msgid "Last name"
msgstr "Příjmení"

#: src/components/profile/OrganizationList.tsx:37
msgid "Leave"
msgstr "Opustit"

#: src/components/users/LoginForm.tsx:65
#: src/pages/NotFoundPage.tsx:42
msgid "Log in"
msgstr "Přihlásit se"

#: src/components/layout/UserDropdown.tsx:56
msgid "Log out"
msgstr "Odhlásit se"

#: src/components/webhooks/WebhookDetail.tsx:72
#~ msgid "Logs"
#~ msgstr "Logy"

#: src/components/layout/UserDropdown.tsx:46
msgid "Manage profile"
msgstr "Spravovat profil"

#: src/components/flows/FlowConfiguration.tsx:83
#~ msgid "minutes"
#~ msgstr "minut"

#: src/pages/AnalyticsPage.tsx:66
msgid "Monthly"
msgstr "Měsíční"

#: src/components/dashboard/FlowCallsTable.tsx:65
#: src/components/flows/NodeCallsTable.tsx:38
msgid "Name"
msgstr "Název"

#: src/components/users/ResetPasswordForm.tsx:44
msgid "New password"
msgstr "Nové heslo"

#: src/locales/messages.ts:149
msgid "New password has been saved"
msgstr "Nové heslo bylo uloženo"

#: src/components/connections/ConnectionItem.tsx:49
#: src/components/flows/FlowConfiguration.tsx:93
msgid "No"
msgstr "Ne"

#: src/pages/ConnectionsPage.tsx:30
msgid "No connections are currently available"
msgstr "Žádná připojení nejsou dostupná"

#: src/pages/WebhooksPage.tsx:24
#~ msgid "No webhooks are currently available"
#~ msgstr "Žádné webhooky nejsou dostupné"

#: src/components/builder/NodeConfiguration.tsx:73
msgid "Node type"
msgstr "Typ nodu"

#: src/components/flows/NodeCallsTable.tsx:37
#~ msgid "Node Type"
#~ msgstr "Typ nodu"

#: src/components/webhooks/WebhookItem.tsx:25
#~ msgid "Number of incomming requests"
#~ msgstr "Počet příchozích požadavků"

#: src/pages/AnalyticsPage.tsx:94
msgid "Number of node calls"
msgstr "Počet node callů"

#: src/components/connections/ConnectionItem.tsx:41
msgid "Number of nodes using this connection"
msgstr "Počet nodů využívající toto připojení"

#: src/components/connections/ConnectionItem.tsx:32
#~ msgid "Number of permissions"
#~ msgstr "Počet přístupů"

#: src/components/dashboard/DashboardConfiguration.tsx:44
msgid "Off"
msgstr "Vypnuto"

#: src/components/dashboard/DashboardConfiguration.tsx:45
msgid "On"
msgstr "Zapnuto"

#: src/pages/AnalyticsPage.tsx:49
msgid "Organization"
msgstr "Organizaci"

#: src/components/profile/OrganizationList.tsx:18
#: src/pages/ProfilePage.tsx:37
msgid "Organizations"
msgstr "Organizace"

#: src/locales/messages.ts:53
#~ msgid "Organizations have been reset"
#~ msgstr "Organizace byly resetovány"

#: src/components/flows/NodeCallDetail.tsx:89
msgid "Output"
msgstr "Výstup"

#: src/components/users/LoginForm.tsx:51
msgid "Password"
msgstr "Heslo"

#: src/components/profile/Security.tsx:20
msgid "Password reset"
msgstr "Resetovat heslo"

#: src/hooks/user.ts:240
msgid "Passwords don't match"
msgstr "Hesla se neshodují"

#: src/pages/ProfilePage.tsx:19
msgid "Personal information"
msgstr "Osobní informace"

#: src/components/profile/UserData.tsx:63
msgid "Personal Information"
msgstr "Osobní informace"

#: src/components/profile/UserData.tsx:81
#~ msgid "Phone"
#~ msgstr "Telefonní číslo"

#: src/components/users/ForgotPasswordForm.tsx:49
#: src/components/users/ResetPasswordForm.tsx:68
msgid "Proceed"
msgstr "Potvrdit"

#: src/components/layout/PageLayout.tsx:120
msgid "Profile"
msgstr "Profil"

#: src/components/profile/UserData.tsx:56
msgid "Profile image"
msgstr "Profilový obrázek"

#: src/locales/messages.ts:131
msgid "Profile image has been removed"
msgstr "Profilový obrázek byl odstraněn"

#: src/locales/messages.ts:125
msgid "Profile image has been uploaded"
msgstr "Profilový obrázek byl nahrán"

#: src/pages/AnalyticsPage.tsx:50
msgid "Project"
msgstr "Projekt"

#: src/components/profile/OrganizationList.tsx:46
#~ msgid "Reset organizations"
#~ msgstr "Resetovat organizace"

#: src/components/profile/Security.tsx:37
#: src/components/users/ForgotPasswordForm.tsx:27
msgid "Reset password"
msgstr "Resetovat heslo"

#: src/hooks/flows.tsx:37
msgid "Retrying"
msgstr "Opakování"

msgid "Skipped"
msgstr "Přeskočeno"

#: src/hooks/flows.tsx:41
msgid "Timeout"
msgstr "Časový limit"

#: src/components/profile/UserData.tsx:78
#~ msgid "Role"
#~ msgstr "Role"

#: src/components/flows/FlowExecutionButton.tsx:28
msgid "Run once"
msgstr "Spustit jednou"

#: src/components/profile/UserData.tsx:75
msgid "Save"
msgstr "Uložit"

#: src/pages/ProfilePage.tsx:28
msgid "Security"
msgstr "Zabezpečení"

#: src/pages/FlowBuilderPage.tsx:17
msgid "Select a flow to get started"
msgstr "Vyberte flow, se kterým chcete pracovat"

#: src/components/webhooks/WebhookDetail.tsx:36
#~ msgid "Service"
#~ msgstr "Služba"

#: src/components/users/ResetPasswordForm.tsx:35
msgid "Set new password"
msgstr "Nastavit nové heslo"

#: src/components/other/ShowHideButton.tsx:20
msgid "Show"
msgstr "Zobrazit"

#: src/pages/AnalyticsPage.tsx:45
msgid "Show analytics for current"
msgstr "Zobrazit analytiky pro aktuální"

#: src/components/dashboard/FlowCallsTable.tsx:78
#: src/components/flows/NodeCallsTable.tsx:51
msgid "Start"
msgstr "Začátek"

#: src/components/layout/PageLayout.tsx:110
msgid "Statistics"
msgstr "Statistiky"

#: src/components/dashboard/FlowCallsTable.tsx:71
#: src/components/flows/NodeCallsTable.tsx:44
msgid "Status"
msgstr "Status"

#: src/hooks/flows.tsx:35
msgid "Success"
msgstr "Úspěch"

#: src/locales/messages.ts:137
msgid "The changes have been saved"
msgstr "Změny byly uloženy"

#: src/locales/messages.ts:26
#~ msgid "The connection {0} returned an error: {1}"
#~ msgstr "Připojení {0} vrátilo chybu: {1}"

#: src/locales/messages.ts:20
#~ msgid "The connection {0} returned an error: {errorText}"
#~ msgstr "Připojení {0} vrátilo chybu: {errorText}"

#: src/locales/messages.ts:17
#~ msgid "The connection {0} works as expected"
#~ msgstr "Připojení {0} funguje správně"

#: src/components/profile/UserData.tsx:49
msgid "The form contains errors, please fix them before submitting"
msgstr "Opravte prosím chyby ve formuláři"

#: src/pages/AnalyticsPage.tsx:106
msgid "There are no node calls"
msgstr "Nejsou evidovány žádné node cally"

#: src/components/profile/UserData.tsx:90
#: src/components/profile/UserData.tsx:100
msgid "This field is required"
msgstr "Toto pole je povinné"

#: src/pages/NotFoundPage.tsx:23
msgid "This page does not exist"
msgstr "Tato stránka neexistuje"

#: src/components/flows/NodeCallsTable.tsx:50
#~ msgid "Timestamp"
#~ msgstr "Čas"

#: src/components/profile/ExportData.tsx:14
msgid "To export your data, click the button below."
msgstr "Pro exportování dat klikněte na tlačítko níže."

#: src/components/profile/Security.tsx:25
msgid "To reset your password, click the button below and wait for an email with further instructions."
msgstr "Pro resetování hesla klikněte na tlačítko a vyčkejte na email s dalšími instrukcemi."

#: src/components/profile/UserData.tsx:89
#: src/components/profile/UserData.tsx:99
msgid "Value must be up to 40 characters"
msgstr "Hodnota musí mít maximálně 40 znaků"

#: src/components/connections/ConnectionItem.tsx:42
#~ msgid "Verify"
#~ msgstr "Ověřit"

#: src/components/dashboard/FlowCallsTable.tsx:46
msgid "View detail"
msgstr "Zobrazit detail"

#: src/components/flows/SchemaHider.tsx:21
msgid "View flow schema"
msgstr "Zobrazit flow schéma"

#: src/components/webhooks/WebhookDetail.tsx:24
#~ msgid "Webhook status"
#~ msgstr "Status"

#: src/components/webhooks/WebhookDetail.tsx:42
#~ msgid "Webhook URL"
#~ msgstr "URL"

#: src/components/webhooks/WebhookDetail.tsx:50
#~ msgid "Webhook UUID"
#~ msgstr "UUID"

#: src/pages/WebhooksPage.tsx:18
#~ msgid "Webhooks"
#~ msgstr "Webhooky"

#: src/pages/AnalyticsPage.tsx:65
msgid "Weekly"
msgstr "Týdenní"

#: src/pages/AnalyticsPage.tsx:60
msgid "with"
msgstr "s"

#: src/components/connections/ConnectionItem.tsx:48
#: src/components/flows/FlowConfiguration.tsx:92
msgid "Yes"
msgstr "Ano"

#: src/locales/messages.ts:101
msgid "You have left {0}"
msgstr "Odešli jste z {0}"

#: src/locales/messages.ts:119
msgid "You must have entered wrong email or password"
msgstr "Museli jste zadat špatný email nebo heslo"

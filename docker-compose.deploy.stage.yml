version: '3.8'

services:
  web:
    image: docker.whys.eu/symmy/symmy:backend-stage
    command: /start
    volumes:
      # - src-data:/app
      - staticfiles:/app/staticfiles
    ports:
      - '8010:8000'
    env_file:
      - src/backend/.env
    depends_on:
      - mongo
      - redis
      - db
    deploy:
      replicas: 1
      update_config:
        parallelism: 3
        order: start-first
        failure_action: rollback
        delay: 10s
      restart_policy:
        condition: on-failure
        max_attempts: 3
    healthcheck:
      test: curl -f http://127.0.0.1:8000/api/v1/live/healthcheck/ || echo "1"
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - symmy-network

  db:
    image: postgres:15
    ports:
      - '5432:5432'
    env_file:
      - src/backend/.env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - symmy-network

  mongo:
    image: mongo:7.0.4
    ports:
      - 27017:27017
    env_file:
      - src/backend/.env
    volumes:
      - mongo_data:/data/db
    networks:
      - symmy-network

  rabbitmq:
    image: rabbitmq:3.12.8
    environment:
      - TZ=Europe/Prague
    ports:
      - '5672:5672'
      - '15672:15672'
    networks:
      - symmy-network

  redis:
    image: redis:7.2.2
    environment:
      - TZ=Europe/Prague
    ports:
      - '6379:6379'
    healthcheck:
      test: ['CMD-SHELL', 'redis-cli ping | grep -q PONG']
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - symmy-network

  celery_worker:
    image: docker.whys.eu/symmy/symmy:backend-stage
    command: /start-celeryworker
    # volumes:
    #   - src-data:/app
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - rabbitmq
      - db
    deploy:
      replicas: 1
      update_config:
        parallelism: 3
        order: start-first
        failure_action: rollback
        delay: 10s
      restart_policy:
        condition: on-failure
        max_attempts: 3
    healthcheck:
      test: curl -f http://127.0.0.1:8000/api/v1/live/healthcheck/ || echo "1"
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - symmy-network

  celery_beat:
    image: docker.whys.eu/symmy/symmy:backend-stage
    command: /start-celerybeat
    # volumes:
    #   - src-data:/app
    env_file:
      - src/backend/.env
    depends_on:
      - redis
      - rabbitmq
      - db
    deploy:
      replicas: 1
      update_config:
        parallelism: 3
        order: start-first
        failure_action: rollback
        delay: 10s
      restart_policy:
        condition: on-failure
        max_attempts: 3
    healthcheck:
      test: curl -f http://127.0.0.1:8000/api/v1/live/healthcheck/ || echo "1"
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - symmy-network

  flower:
    image: docker.whys.eu/symmy/symmy:backend-stage
    command: /start-flower
    # volumes:
    #   - src-data:/app
    env_file:
      - src/backend/.env
    ports:
      - 5557:5555
    depends_on:
      - redis
      - db
    deploy:
      replicas: 1
      update_config:
        parallelism: 1
        order: start-first
        failure_action: rollback
        delay: 10s
    healthcheck:
      test: curl -f http://127.0.0.1:5555/api/v1/live/healthcheck/ || echo "1"
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - symmy-network

volumes:
  flower_data:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/postgresql/15/data
  mongo_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/mongo/data
  staticfiles:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /home/<USER>/app/staticfiles

networks:
  symmy-network:

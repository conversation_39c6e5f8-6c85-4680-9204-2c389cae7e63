version: '3'

services:
  db:
    container_name: db
    image: postgres:15
    ports:
      - "5438:5432"
    environment:
      - POSTGRES_USER=symmy
      - POSTGRES_PASSWORD=symmy
      - POSTGRES_DB=symmy

  mongo:
    container_name: mongo
    image: mongo:7.0.4
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=symmy
      - MONGO_INITDB_ROOT_PASSWORD=symmy

  redis:
    container_name: redis
    image: redis:latest
    ports:
      - "6380:6379"

volumes:
  flower_data:
  postgres_data:
